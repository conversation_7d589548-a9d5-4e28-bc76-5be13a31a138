import{r as u,R as ue}from"./vendor-Dneogk0_.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},N.apply(this,arguments)}var R;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(R||(R={}));const z="popstate";function ce(e){e===void 0&&(e={});function t(n,a){let{pathname:l,search:i,hash:s}=n.location;return $("",{pathname:l,search:i,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:T(a)}return he(t,r,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Z(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function fe(){return Math.random().toString(36).substr(2,8)}function K(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,r,n){return r===void 0&&(r=null),N({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:r,key:t&&t.key||n||fe()})}function T(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function U(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function he(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:l=!1}=n,i=a.history,s=R.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(N({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){s=R.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:s,location:m.location,delta:x})}function p(d,x){s=R.Push;let E=$(m.location,d,x);f=h()+1;let C=K(E,f),P=m.createHref(E);try{i.pushState(C,"",P)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;a.location.assign(P)}l&&o&&o({action:s,location:m.location,delta:1})}function y(d,x){s=R.Replace;let E=$(m.location,d,x);f=h();let C=K(E,f),P=m.createHref(E);i.replaceState(C,"",P),l&&o&&o({action:s,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:T(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return s},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(z,c),o=d,()=>{a.removeEventListener(z,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var q;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(q||(q={}));function de(e,t,r){return r===void 0&&(r="/"),pe(e,t,r)}function pe(e,t,r,n){let a=typeof t=="string"?U(t):t,l=M(a.pathname||"/",r);if(l==null)return null;let i=ee(e);me(i);let s=null;for(let o=0;s==null&&o<i.length;++o){let f=Le(l);s=we(i[o],f)}return s}function ee(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(l,i,s)=>{let o={relativePath:s===void 0?l.path||"":s,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(n),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(n.length));let f=w([n,o.relativePath]),h=r.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ee(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Pe(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var s;if(l.path===""||!((s=l.path)!=null&&s.includes("?")))a(l,i);else for(let o of te(l.path))a(l,i,o)}),t}function te(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),l=r.replace(/\?$/,"");if(n.length===0)return a?[l,""]:[l];let i=te(n.join("/")),s=[];return s.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&s.push(...i),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function me(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Re(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const ve=/^:[\w-]+$/,ge=3,ye=2,xe=1,Ce=10,Ee=-2,G=e=>e==="*";function Pe(e,t){let r=e.split("/"),n=r.length;return r.some(G)&&(n+=Ee),t&&(n+=ye),r.filter(a=>!G(a)).reduce((a,l)=>a+(ve.test(l)?ge:l===""?xe:Ce),n)}function Re(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function we(e,t,r){let{routesMeta:n}=e,a={},l="/",i=[];for(let s=0;s<n.length;++s){let o=n[s],f=s===n.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=Se({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:w([l,c.pathname]),pathnameBase:Ne(w([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=w([l,c.pathnameBase]))}return i}function Se(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=be(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:n.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=s[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=s[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function be(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Z(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,s,o)=>(n.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Le(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Z(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function M(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ue(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:r?r.startsWith("/")?r:Oe(r,t):t,search:Ie(n),hash:Te(a)}}function Oe(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function _(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Be(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function A(e,t){let r=Be(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function V(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=U(e):(a=N({},e),v(!a.pathname||!a.pathname.includes("?"),_("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),_("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),_("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,s;if(i==null)s=r;else{let c=t.length-1;if(!n&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}s=c>=0?t[c]:"/"}let o=Ue(a,s),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&r.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const w=e=>e.join("/").replace(/\/\/+/g,"/"),Ne=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ie=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Te=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function je(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const re=["post","put","patch","delete"];new Set(re);const _e=["get",...re];new Set(_e);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},I.apply(this,arguments)}const D=u.createContext(null),$e=u.createContext(null),S=u.createContext(null),j=u.createContext(null),b=u.createContext({outlet:null,matches:[],isDataRoute:!1}),ne=u.createContext(null);function ke(e,t){let{relative:r}=t===void 0?{}:t;O()||v(!1);let{basename:n,navigator:a}=u.useContext(S),{hash:l,pathname:i,search:s}=le(e,{relative:r}),o=i;return n!=="/"&&(o=i==="/"?n:w([n,i])),a.createHref({pathname:o,search:s,hash:l})}function O(){return u.useContext(j)!=null}function B(){return O()||v(!1),u.useContext(j).location}function ae(e){u.useContext(S).static||u.useLayoutEffect(e)}function J(){let{isDataRoute:e}=u.useContext(b);return e?He():We()}function We(){O()||v(!1);let e=u.useContext(D),{basename:t,future:r,navigator:n}=u.useContext(S),{matches:a}=u.useContext(b),{pathname:l}=B(),i=JSON.stringify(A(a,r.v7_relativeSplatPath)),s=u.useRef(!1);return ae(()=>{s.current=!0}),u.useCallback(function(f,h){if(h===void 0&&(h={}),!s.current)return;if(typeof f=="number"){n.go(f);return}let c=V(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:w([t,c.pathname])),(h.replace?n.replace:n.push)(c,h.state,h)},[t,n,i,l,e])}function le(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=u.useContext(S),{matches:a}=u.useContext(b),{pathname:l}=B(),i=JSON.stringify(A(a,n.v7_relativeSplatPath));return u.useMemo(()=>V(e,JSON.parse(i),l,r==="path"),[e,i,l,r])}function Fe(e,t){return Me(e,t)}function Me(e,t,r,n){O()||v(!1);let{navigator:a}=u.useContext(S),{matches:l}=u.useContext(b),i=l[l.length-1],s=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=B(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=de(e,{pathname:y}),m=ze(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},s,d.params),pathname:w([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:w([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,r,n);return t&&m?u.createElement(j.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:R.Pop}},m):m}function Ae(){let e=Xe(),t=je(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),r?u.createElement("pre",{style:a},r):null,null)}const Ve=u.createElement(Ae,null);class De extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?u.createElement(b.Provider,{value:this.props.routeContext},u.createElement(ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Je(e){let{routeContext:t,match:r,children:n}=e,a=u.useContext(D);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),u.createElement(b.Provider,{value:t},n)}function ze(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var l;if(!r)return null;if(r.errors)e=r.matches;else if((l=n)!=null&&l.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,s=(a=r)==null?void 0:a.errors;if(s!=null){let h=i.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(r&&n&&n.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=r,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;r&&(y=s&&c.route.id?s[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(Qe("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=u.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,u.createElement(Je,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:r!=null},children:C})};return r&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?u.createElement(De,{location:r.location,revalidation:r.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var ie=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ie||{}),oe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(oe||{});function Ke(e){let t=u.useContext(D);return t||v(!1),t}function qe(e){let t=u.useContext($e);return t||v(!1),t}function Ge(e){let t=u.useContext(b);return t||v(!1),t}function se(e){let t=Ge(),r=t.matches[t.matches.length-1];return r.route.id||v(!1),r.route.id}function Xe(){var e;let t=u.useContext(ne),r=qe(),n=se();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function He(){let{router:e}=Ke(ie.UseNavigateStable),t=se(oe.UseNavigateStable),r=u.useRef(!1);return ae(()=>{r.current=!0}),u.useCallback(function(a,l){l===void 0&&(l={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const X={};function Qe(e,t,r){X[e]||(X[e]=!0)}function Ye(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ht(e){let{to:t,replace:r,state:n,relative:a}=e;O()||v(!1);let{future:l,static:i}=u.useContext(S),{matches:s}=u.useContext(b),{pathname:o}=B(),f=J(),h=V(t,A(s,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return u.useEffect(()=>f(JSON.parse(c),{replace:r,state:n,relative:a}),[f,c,a,r,n]),null}function Ze(e){v(!1)}function et(e){let{basename:t="/",children:r=null,location:n,navigationType:a=R.Pop,navigator:l,static:i=!1,future:s}=e;O()&&v(!1);let o=t.replace(/^\/*/,"/"),f=u.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},s)}),[o,s,l,i]);typeof n=="string"&&(n=U(n));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=n,m=u.useMemo(()=>{let d=M(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:u.createElement(S.Provider,{value:f},u.createElement(j.Provider,{children:r,value:m}))}function dt(e){let{children:t,location:r}=e;return Fe(k(t),r)}new Promise(()=>{});function k(e,t){t===void 0&&(t=[]);let r=[];return u.Children.forEach(e,(n,a)=>{if(!u.isValidElement(n))return;let l=[...t,a];if(n.type===u.Fragment){r.push.apply(r,k(n.props.children,l));return}n.type!==Ze&&v(!1),!n.props.index||!n.props.children||v(!1);let i={id:n.props.id||l.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=k(n.props.children,l)),r.push(i)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},W.apply(this,arguments)}function tt(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,l;for(l=0;l<n.length;l++)a=n[l],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function rt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nt(e,t){return e.button===0&&(!t||t==="_self")&&!rt(e)}function F(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function at(e,t){let r=F(e);return t&&t.forEach((n,a)=>{r.has(a)||t.getAll(a).forEach(l=>{r.append(a,l)})}),r}const lt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],it="6";try{window.__reactRouterVersion=it}catch{}const ot="startTransition",H=ue[ot];function pt(e){let{basename:t,children:r,future:n,window:a}=e,l=u.useRef();l.current==null&&(l.current=ce({window:a,v5Compat:!0}));let i=l.current,[s,o]=u.useState({action:i.action,location:i.location}),{v7_startTransition:f}=n||{},h=u.useCallback(c=>{f&&H?H(()=>o(c)):o(c)},[o,f]);return u.useLayoutEffect(()=>i.listen(h),[i,h]),u.useEffect(()=>Ye(n),[n]),u.createElement(et,{basename:t,children:r,location:s.location,navigationType:s.action,navigator:i,future:n})}const st=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mt=u.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:l,replace:i,state:s,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=tt(t,lt),{basename:y}=u.useContext(S),g,m=!1;if(typeof f=="string"&&ut.test(f)&&(g=f,st))try{let C=new URL(window.location.href),P=f.startsWith("//")?new URL(C.protocol+f):new URL(f),L=M(P.pathname,y);P.origin===C.origin&&L!=null?f=L+P.search+P.hash:m=!0}catch{}let d=ke(f,{relative:a}),x=ct(f,{replace:i,state:s,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){n&&n(C),C.defaultPrevented||x(C)}return u.createElement("a",W({},p,{href:g||d,onClick:m||l?n:E,ref:r,target:o}))});var Q;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Q||(Q={}));var Y;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Y||(Y={}));function ct(e,t){let{target:r,replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:s}=t===void 0?{}:t,o=J(),f=B(),h=le(e,{relative:i});return u.useCallback(c=>{if(nt(c,r)){c.preventDefault();let p=n!==void 0?n:T(f)===T(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:s})}},[f,o,h,n,a,r,e,l,i,s])}function vt(e){let t=u.useRef(F(e)),r=u.useRef(!1),n=B(),a=u.useMemo(()=>at(n.search,r.current?null:t.current),[n.search]),l=J(),i=u.useCallback((s,o)=>{const f=F(typeof s=="function"?s(a):s);r.current=!0,l("?"+f,o)},[l,a]);return[a,i]}export{pt as B,mt as L,ht as N,dt as R,Ze as a,vt as b,J as c,B as u};
//# sourceMappingURL=router-DMCr7QLp.js.map
