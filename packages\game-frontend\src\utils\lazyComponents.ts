import { lazy } from 'react'

// 懒加载游戏相关组件
export const LazyGamePage = lazy(() => import('../pages/GamePage'))
export const LazyLevelsPage = lazy(() => import('../pages/LevelsPage'))
export const LazyLeaderboardPage = lazy(() => import('../pages/LeaderboardPage'))
export const LazyAchievementsPage = lazy(() => import('../pages/AchievementsPage'))
export const LazyProfilePage = lazy(() => import('../pages/ProfilePage'))

// 懒加载游戏组件
export const LazyGameBoard = lazy(() => import('../components/Game/GameBoard'))
export const LazyPowerUpPanel = lazy(() => import('../components/Game/PowerUpPanel'))
export const LazyShareModal = lazy(() => import('../components/Game/ShareModal'))
export const LazyGameOverModal = lazy(() => import('../components/Game/GameOverModal'))

// 懒加载管理工具 - 这些不是React组件，所以不应该使用lazy
// 改为直接导入工厂函数
export const createAnimationManager = () => import('../utils/animationManager').then(module => module.AnimationManager)
export const createSpecialGemManager = () => import('../utils/specialGemManager').then(module => module.SpecialGemManager)
export const createPowerUpManager = () => import('../utils/powerUpManager').then(module => module.PowerUpManager)
export const createAchievementManager = () => import('../utils/achievementManager').then(module => module.AchievementManager)

// 预加载函数
export const preloadGameComponents = () => {
  // 预加载核心游戏组件
  LazyGamePage
  LazyGameBoard
  LazyPowerUpPanel
  
  // 预加载游戏管理器
  createAnimationManager()
  createSpecialGemManager()
  createPowerUpManager()
}

export const preloadSocialComponents = () => {
  // 预加载社交功能组件
  LazyLeaderboardPage
  LazyAchievementsPage
  LazyShareModal
}

export const preloadAllComponents = () => {
  preloadGameComponents()
  preloadSocialComponents()
  LazyLevelsPage
  LazyProfilePage
  LazyGameOverModal
  createAchievementManager()
}

// 组件预加载策略
export class ComponentPreloader {
  private static preloadedComponents = new Set<string>()
  
  static async preloadComponent(componentName: string, loader: () => Promise<any>) {
    if (this.preloadedComponents.has(componentName)) {
      return
    }
    
    try {
      await loader()
      this.preloadedComponents.add(componentName)
      console.log(`预加载组件成功: ${componentName}`)
    } catch (error) {
      console.error(`预加载组件失败: ${componentName}`, error)
    }
  }
  
  static async preloadOnIdle() {
    if ('requestIdleCallback' in window) {
      return new Promise<void>((resolve) => {
        requestIdleCallback(() => {
          preloadAllComponents()
          resolve()
        })
      })
    } else {
      // 降级方案
      setTimeout(() => {
        preloadAllComponents()
      }, 100)
    }
  }
  
  static async preloadOnUserInteraction() {
    const events = ['mousedown', 'touchstart', 'keydown']
    
    const preloadOnce = () => {
      preloadGameComponents()
      events.forEach(event => {
        document.removeEventListener(event, preloadOnce)
      })
    }
    
    events.forEach(event => {
      document.addEventListener(event, preloadOnce, { once: true, passive: true })
    })
  }
  
  static getPreloadedComponents() {
    return Array.from(this.preloadedComponents)
  }
  
  static clearPreloadedComponents() {
    this.preloadedComponents.clear()
  }
}

// 资源预加载
export class ResourcePreloader {
  private static loadedResources = new Set<string>()
  
  static async preloadImages(imageUrls: string[]) {
    const promises = imageUrls.map(url => this.preloadImage(url))
    return Promise.allSettled(promises)
  }
  
  static preloadImage(url: string): Promise<void> {
    if (this.loadedResources.has(url)) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.loadedResources.add(url)
        resolve()
      }
      img.onerror = reject
      img.src = url
    })
  }
  
  static async preloadAudio(audioUrls: string[]) {
    const promises = audioUrls.map(url => this.preloadAudioFile(url))
    return Promise.allSettled(promises)
  }
  
  static preloadAudioFile(url: string): Promise<void> {
    if (this.loadedResources.has(url)) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      audio.oncanplaythrough = () => {
        this.loadedResources.add(url)
        resolve()
      }
      audio.onerror = reject
      audio.src = url
      audio.load()
    })
  }
  
  static getLoadedResources() {
    return Array.from(this.loadedResources)
  }
}

// 内存管理
export class MemoryManager {
  private static readonly MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB
  private static cache = new Map<string, any>()
  private static cacheSize = 0
  
  static set(key: string, value: any, size: number = 0) {
    // 如果缓存过大，清理最旧的条目
    while (this.cacheSize + size > this.MAX_CACHE_SIZE && this.cache.size > 0) {
      const firstKey = this.cache.keys().next().value
      this.delete(firstKey!)
    }
    
    this.cache.set(key, value)
    this.cacheSize += size
  }
  
  static get(key: string) {
    return this.cache.get(key)
  }
  
  static delete(key: string) {
    if (this.cache.has(key)) {
      this.cache.delete(key)
      // 简化的大小计算
      this.cacheSize = Math.max(0, this.cacheSize - 1024)
    }
  }
  
  static clear() {
    this.cache.clear()
    this.cacheSize = 0
  }
  
  static getCacheInfo() {
    return {
      size: this.cache.size,
      memoryUsage: this.cacheSize,
      maxSize: this.MAX_CACHE_SIZE
    }
  }
  
  static cleanup() {
    // 清理过期或不常用的缓存项
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5分钟
    
    for (const [key, value] of this.cache.entries()) {
      if (value.timestamp && now - value.timestamp > maxAge) {
        this.delete(key)
      }
    }
  }
}

// 自动清理定时器
setInterval(() => {
  MemoryManager.cleanup()
}, 60000) // 每分钟清理一次
