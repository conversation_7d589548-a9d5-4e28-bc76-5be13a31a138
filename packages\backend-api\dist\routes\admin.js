"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../database/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.adminRoutes = router;
const db = database_1.Database.getInstance();
router.use(auth_1.authenticateToken);
router.use(auth_1.requireAdmin);
router.get('/stats/overview', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const [userStats, levelStats, gameStats] = await Promise.all([
        db.get(`
      SELECT 
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN last_login > datetime('now', '-7 days') THEN 1 END) as activeUsers,
        COUNT(CASE WHEN created_at > datetime('now', '-1 day') THEN 1 END) as newUsersToday
      FROM users
    `),
        db.get(`
      SELECT 
        COUNT(*) as totalLevels,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as activeLevels
      FROM levels
    `),
        db.get(`
      SELECT 
        COUNT(*) as totalGames,
        COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completedGames,
        AVG(best_score) as averageScore
      FROM user_progress
    `)
    ]);
    res.json({
        success: true,
        data: {
            users: {
                total: userStats.totalUsers || 0,
                active: userStats.activeUsers || 0,
                newToday: userStats.newUsersToday || 0
            },
            levels: {
                total: levelStats.totalLevels || 0,
                active: levelStats.activeLevels || 0
            },
            games: {
                total: gameStats.totalGames || 0,
                completed: gameStats.completedGames || 0,
                averageScore: Math.round(gameStats.averageScore || 0)
            }
        }
    });
}));
router.get('/levels', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;
    const [levels, totalResult] = await Promise.all([
        db.all(`
      SELECT 
        l.*,
        COUNT(up.user_id) as playCount,
        COUNT(CASE WHEN up.is_completed = 1 THEN 1 END) as completionCount,
        AVG(up.best_score) as averageScore
      FROM levels l
      LEFT JOIN user_progress up ON l.level_number = up.level_number
      GROUP BY l.id
      ORDER BY l.level_number
      LIMIT ? OFFSET ?
    `, [limit, offset]),
        db.get('SELECT COUNT(*) as count FROM levels')
    ]);
    const formattedLevels = levels.map(level => ({
        id: level.id,
        levelNumber: level.level_number,
        boardSize: level.board_size,
        maxMoves: level.max_moves,
        targetScore: level.target_score,
        targetType: level.target_type,
        difficultyLevel: level.difficulty_level,
        rewardCoins: level.reward_coins,
        isActive: Boolean(level.is_active),
        stats: {
            playCount: level.playCount || 0,
            completionCount: level.completionCount || 0,
            completionRate: level.playCount > 0 ? ((level.completionCount || 0) / level.playCount * 100).toFixed(1) : '0.0',
            averageScore: Math.round(level.averageScore || 0)
        },
        createdAt: level.created_at,
        updatedAt: level.updated_at
    }));
    res.json({
        success: true,
        data: {
            levels: formattedLevels,
            pagination: {
                page,
                limit,
                total: totalResult.count,
                totalPages: Math.ceil(totalResult.count / limit)
            }
        }
    });
}));
const createLevelSchema = joi_1.default.object({
    levelNumber: joi_1.default.number().integer().min(1).required(),
    boardSize: joi_1.default.number().integer().valid(6, 8).default(6),
    maxMoves: joi_1.default.number().integer().min(1).required(),
    targetScore: joi_1.default.number().integer().min(1).required(),
    targetType: joi_1.default.string().valid('score', 'collect', 'clear').required(),
    targetData: joi_1.default.object().optional(),
    obstacles: joi_1.default.array().optional(),
    specialGems: joi_1.default.array().optional(),
    difficultyLevel: joi_1.default.number().integer().min(1).max(5).default(1),
    rewardCoins: joi_1.default.number().integer().min(0).default(10),
    rewardItems: joi_1.default.array().optional()
});
router.post('/levels', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createLevelSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const { levelNumber, boardSize, maxMoves, targetScore, targetType, targetData, obstacles, specialGems, difficultyLevel, rewardCoins, rewardItems } = value;
    const existingLevel = await db.get('SELECT id FROM levels WHERE level_number = ?', [levelNumber]);
    if (existingLevel) {
        throw (0, errorHandler_1.createError)(409, 'LEVEL_EXISTS', '关卡编号已存在');
    }
    const result = await db.run(`
    INSERT INTO levels (
      level_number, board_size, max_moves, target_score, target_type,
      target_data, obstacles, special_gems, difficulty_level, reward_coins, reward_items
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [
        levelNumber, boardSize, maxMoves, targetScore, targetType,
        targetData ? JSON.stringify(targetData) : null,
        obstacles ? JSON.stringify(obstacles) : null,
        specialGems ? JSON.stringify(specialGems) : null,
        difficultyLevel, rewardCoins,
        rewardItems ? JSON.stringify(rewardItems) : null
    ]);
    const newLevel = await db.get('SELECT * FROM levels WHERE id = ?', [result.lastID]);
    res.status(201).json({
        success: true,
        data: {
            id: newLevel.id,
            levelNumber: newLevel.level_number,
            boardSize: newLevel.board_size,
            maxMoves: newLevel.max_moves,
            targetScore: newLevel.target_score,
            targetType: newLevel.target_type,
            difficultyLevel: newLevel.difficulty_level,
            rewardCoins: newLevel.reward_coins,
            isActive: Boolean(newLevel.is_active)
        },
        message: '关卡创建成功'
    });
}));
router.put('/levels/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const levelId = parseInt(req.params.id);
    const { error, value } = createLevelSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const level = await db.get('SELECT * FROM levels WHERE id = ?', [levelId]);
    if (!level) {
        throw (0, errorHandler_1.createError)(404, 'LEVEL_001', '关卡不存在');
    }
    const { levelNumber, boardSize, maxMoves, targetScore, targetType, targetData, obstacles, specialGems, difficultyLevel, rewardCoins, rewardItems } = value;
    await db.run(`
    UPDATE levels SET
      level_number = ?, board_size = ?, max_moves = ?, target_score = ?, target_type = ?,
      target_data = ?, obstacles = ?, special_gems = ?, difficulty_level = ?, 
      reward_coins = ?, reward_items = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `, [
        levelNumber, boardSize, maxMoves, targetScore, targetType,
        targetData ? JSON.stringify(targetData) : null,
        obstacles ? JSON.stringify(obstacles) : null,
        specialGems ? JSON.stringify(specialGems) : null,
        difficultyLevel, rewardCoins,
        rewardItems ? JSON.stringify(rewardItems) : null,
        levelId
    ]);
    res.json({
        success: true,
        message: '关卡更新成功'
    });
}));
router.get('/users', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;
    const [users, totalResult] = await Promise.all([
        db.all(`
      SELECT 
        u.id, u.username, u.display_name, u.email, u.coins, u.gems,
        u.current_level, u.total_score, u.created_at, u.last_login,
        COUNT(up.level_number) as levelsPlayed,
        COUNT(CASE WHEN up.is_completed = 1 THEN 1 END) as levelsCompleted
      FROM users u
      LEFT JOIN user_progress up ON u.id = up.user_id
      GROUP BY u.id
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `, [limit, offset]),
        db.get('SELECT COUNT(*) as count FROM users')
    ]);
    res.json({
        success: true,
        data: {
            users: users.map(user => ({
                id: user.id,
                username: user.username,
                displayName: user.display_name,
                email: user.email,
                coins: user.coins,
                gems: user.gems,
                currentLevel: user.current_level,
                totalScore: user.total_score,
                stats: {
                    levelsPlayed: user.levelsPlayed || 0,
                    levelsCompleted: user.levelsCompleted || 0
                },
                createdAt: user.created_at,
                lastLogin: user.last_login
            })),
            pagination: {
                page,
                limit,
                total: totalResult.count,
                totalPages: Math.ceil(totalResult.count / limit)
            }
        }
    });
}));
router.get('/items', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query._start) || 0;
    const limit = parseInt(req.query._end) || 10;
    const actualLimit = limit - page;
    const [items, totalResult] = await Promise.all([
        db.all(`
      SELECT
        id, item_code, name, type, description, rarity,
        price_coins, price_gems, max_stack, is_purchasable,
        is_active, created_at, updated_at
      FROM items
      ORDER BY id ASC
      LIMIT ? OFFSET ?
    `, [actualLimit, page]),
        db.get('SELECT COUNT(*) as total FROM items')
    ]);
    res.set('X-Total-Count', totalResult.total.toString());
    res.json(items);
}));
router.get('/items/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const itemId = parseInt(req.params.id);
    const item = await db.get('SELECT * FROM items WHERE id = ?', [itemId]);
    if (!item) {
        throw (0, errorHandler_1.createError)(404, 'ITEM_001', '道具不存在');
    }
    res.json(item);
}));
const createItemSchema = joi_1.default.object({
    item_code: joi_1.default.string().required(),
    name: joi_1.default.string().required(),
    type: joi_1.default.string().valid('tool', 'booster', 'consumable').required(),
    description: joi_1.default.string().required(),
    rarity: joi_1.default.string().valid('common', 'rare', 'epic', 'legendary').default('common'),
    effect_data: joi_1.default.string().optional(),
    price_coins: joi_1.default.number().integer().min(0).default(0),
    price_gems: joi_1.default.number().integer().min(0).default(0),
    max_stack: joi_1.default.number().integer().min(1).default(99),
    icon_url: joi_1.default.string().optional(),
    is_purchasable: joi_1.default.boolean().default(true),
    is_active: joi_1.default.boolean().default(true)
});
router.post('/items', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = createItemSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const { item_code, name, type, description, rarity, effect_data, price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active } = value;
    const result = await db.run(`
    INSERT INTO items (
      item_code, name, type, description, rarity, effect_data,
      price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [item_code, name, type, description, rarity, effect_data,
        price_coins, price_gems, max_stack, icon_url, is_purchasable ? 1 : 0, is_active ? 1 : 0]);
    const newItem = await db.get('SELECT * FROM items WHERE id = ?', [result.lastID]);
    res.status(201).json(newItem);
}));
router.put('/items/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const itemId = parseInt(req.params.id);
    const { error, value } = createItemSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const existingItem = await db.get('SELECT id FROM items WHERE id = ?', [itemId]);
    if (!existingItem) {
        throw (0, errorHandler_1.createError)(404, 'ITEM_001', '道具不存在');
    }
    const { item_code, name, type, description, rarity, effect_data, price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active } = value;
    await db.run(`
    UPDATE items SET
      item_code = ?, name = ?, type = ?, description = ?, rarity = ?, effect_data = ?,
      price_coins = ?, price_gems = ?, max_stack = ?, icon_url = ?,
      is_purchasable = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `, [item_code, name, type, description, rarity, effect_data,
        price_coins, price_gems, max_stack, icon_url, is_purchasable ? 1 : 0, is_active ? 1 : 0, itemId]);
    const updatedItem = await db.get('SELECT * FROM items WHERE id = ?', [itemId]);
    res.json(updatedItem);
}));
router.delete('/items/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const itemId = parseInt(req.params.id);
    const existingItem = await db.get('SELECT id FROM items WHERE id = ?', [itemId]);
    if (!existingItem) {
        throw (0, errorHandler_1.createError)(404, 'ITEM_001', '道具不存在');
    }
    await db.run('DELETE FROM items WHERE id = ?', [itemId]);
    res.json({
        success: true,
        message: '道具删除成功'
    });
}));
//# sourceMappingURL=admin.js.map