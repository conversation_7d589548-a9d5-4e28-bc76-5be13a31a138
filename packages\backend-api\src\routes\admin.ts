import { Router, Response } from 'express';
import Jo<PERSON> from 'joi';
import { Database } from '../database/database';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, requireAdmin, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 所有路由都需要管理员权限
router.use(authenticateToken);
router.use(requireAdmin);

// 获取系统概览统计
router.get('/stats/overview', asyncHandler(async (req: AuthRequest, res: Response) => {
  const [userStats, levelStats, gameStats] = await Promise.all([
    // 用户统计
    db.get(`
      SELECT 
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN last_login > datetime('now', '-7 days') THEN 1 END) as activeUsers,
        COUNT(CASE WHEN created_at > datetime('now', '-1 day') THEN 1 END) as newUsersToday
      FROM users
    `),
    
    // 关卡统计
    db.get(`
      SELECT 
        COUNT(*) as totalLevels,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as activeLevels
      FROM levels
    `),
    
    // 游戏统计
    db.get(`
      SELECT 
        COUNT(*) as totalGames,
        COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completedGames,
        AVG(best_score) as averageScore
      FROM user_progress
    `)
  ]);

  res.json({
    success: true,
    data: {
      users: {
        total: userStats.totalUsers || 0,
        active: userStats.activeUsers || 0,
        newToday: userStats.newUsersToday || 0
      },
      levels: {
        total: levelStats.totalLevels || 0,
        active: levelStats.activeLevels || 0
      },
      games: {
        total: gameStats.totalGames || 0,
        completed: gameStats.completedGames || 0,
        averageScore: Math.round(gameStats.averageScore || 0)
      }
    }
  });
}));

// 获取关卡管理列表
router.get('/levels', asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const offset = (page - 1) * limit;

  const [levels, totalResult] = await Promise.all([
    db.all(`
      SELECT 
        l.*,
        COUNT(up.user_id) as playCount,
        COUNT(CASE WHEN up.is_completed = 1 THEN 1 END) as completionCount,
        AVG(up.best_score) as averageScore
      FROM levels l
      LEFT JOIN user_progress up ON l.level_number = up.level_number
      GROUP BY l.id
      ORDER BY l.level_number
      LIMIT ? OFFSET ?
    `, [limit, offset]),
    
    db.get('SELECT COUNT(*) as count FROM levels')
  ]);

  const formattedLevels = levels.map(level => ({
    id: level.id,
    levelNumber: level.level_number,
    boardSize: level.board_size,
    maxMoves: level.max_moves,
    targetScore: level.target_score,
    targetType: level.target_type,
    difficultyLevel: level.difficulty_level,
    rewardCoins: level.reward_coins,
    isActive: Boolean(level.is_active),
    stats: {
      playCount: level.playCount || 0,
      completionCount: level.completionCount || 0,
      completionRate: level.playCount > 0 ? ((level.completionCount || 0) / level.playCount * 100).toFixed(1) : '0.0',
      averageScore: Math.round(level.averageScore || 0)
    },
    createdAt: level.created_at,
    updatedAt: level.updated_at
  }));

  res.json({
    success: true,
    data: {
      levels: formattedLevels,
      pagination: {
        page,
        limit,
        total: totalResult.count,
        totalPages: Math.ceil(totalResult.count / limit)
      }
    }
  });
}));

// 创建新关卡
const createLevelSchema = Joi.object({
  levelNumber: Joi.number().integer().min(1).required(),
  boardSize: Joi.number().integer().valid(6, 8).default(6),
  maxMoves: Joi.number().integer().min(1).required(),
  targetScore: Joi.number().integer().min(1).required(),
  targetType: Joi.string().valid('score', 'collect', 'clear').required(),
  targetData: Joi.object().optional(),
  obstacles: Joi.array().optional(),
  specialGems: Joi.array().optional(),
  difficultyLevel: Joi.number().integer().min(1).max(5).default(1),
  rewardCoins: Joi.number().integer().min(0).default(10),
  rewardItems: Joi.array().optional()
});

router.post('/levels', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = createLevelSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const {
    levelNumber, boardSize, maxMoves, targetScore, targetType,
    targetData, obstacles, specialGems, difficultyLevel, rewardCoins, rewardItems
  } = value;

  // 检查关卡编号是否已存在
  const existingLevel = await db.get(
    'SELECT id FROM levels WHERE level_number = ?',
    [levelNumber]
  );

  if (existingLevel) {
    throw createError(409, 'LEVEL_EXISTS', '关卡编号已存在');
  }

  const result = await db.run(`
    INSERT INTO levels (
      level_number, board_size, max_moves, target_score, target_type,
      target_data, obstacles, special_gems, difficulty_level, reward_coins, reward_items
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [
    levelNumber, boardSize, maxMoves, targetScore, targetType,
    targetData ? JSON.stringify(targetData) : null,
    obstacles ? JSON.stringify(obstacles) : null,
    specialGems ? JSON.stringify(specialGems) : null,
    difficultyLevel, rewardCoins,
    rewardItems ? JSON.stringify(rewardItems) : null
  ]);

  const newLevel = await db.get(
    'SELECT * FROM levels WHERE id = ?',
    [result.lastID]
  );

  res.status(201).json({
    success: true,
    data: {
      id: newLevel.id,
      levelNumber: newLevel.level_number,
      boardSize: newLevel.board_size,
      maxMoves: newLevel.max_moves,
      targetScore: newLevel.target_score,
      targetType: newLevel.target_type,
      difficultyLevel: newLevel.difficulty_level,
      rewardCoins: newLevel.reward_coins,
      isActive: Boolean(newLevel.is_active)
    },
    message: '关卡创建成功'
  });
}));

// 更新关卡
router.put('/levels/:id', asyncHandler(async (req: AuthRequest, res: Response) => {
  const levelId = parseInt(req.params.id);
  const { error, value } = createLevelSchema.validate(req.body);
  
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const level = await db.get('SELECT * FROM levels WHERE id = ?', [levelId]);
  if (!level) {
    throw createError(404, 'LEVEL_001', '关卡不存在');
  }

  const {
    levelNumber, boardSize, maxMoves, targetScore, targetType,
    targetData, obstacles, specialGems, difficultyLevel, rewardCoins, rewardItems
  } = value;

  await db.run(`
    UPDATE levels SET
      level_number = ?, board_size = ?, max_moves = ?, target_score = ?, target_type = ?,
      target_data = ?, obstacles = ?, special_gems = ?, difficulty_level = ?, 
      reward_coins = ?, reward_items = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `, [
    levelNumber, boardSize, maxMoves, targetScore, targetType,
    targetData ? JSON.stringify(targetData) : null,
    obstacles ? JSON.stringify(obstacles) : null,
    specialGems ? JSON.stringify(specialGems) : null,
    difficultyLevel, rewardCoins,
    rewardItems ? JSON.stringify(rewardItems) : null,
    levelId
  ]);

  res.json({
    success: true,
    message: '关卡更新成功'
  });
}));

// 获取用户管理列表
router.get('/users', asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const offset = (page - 1) * limit;

  const [users, totalResult] = await Promise.all([
    db.all(`
      SELECT 
        u.id, u.username, u.display_name, u.email, u.coins, u.gems,
        u.current_level, u.total_score, u.created_at, u.last_login,
        COUNT(up.level_number) as levelsPlayed,
        COUNT(CASE WHEN up.is_completed = 1 THEN 1 END) as levelsCompleted
      FROM users u
      LEFT JOIN user_progress up ON u.id = up.user_id
      GROUP BY u.id
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `, [limit, offset]),
    
    db.get('SELECT COUNT(*) as count FROM users')
  ]);

  res.json({
    success: true,
    data: {
      users: users.map(user => ({
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        email: user.email,
        coins: user.coins,
        gems: user.gems,
        currentLevel: user.current_level,
        totalScore: user.total_score,
        stats: {
          levelsPlayed: user.levelsPlayed || 0,
          levelsCompleted: user.levelsCompleted || 0
        },
        createdAt: user.created_at,
        lastLogin: user.last_login
      })),
      pagination: {
        page,
        limit,
        total: totalResult.count,
        totalPages: Math.ceil(totalResult.count / limit)
      }
    }
  });
}));

// 获取道具管理列表
router.get('/items', asyncHandler(async (req: AuthRequest, res: Response) => {
  const page = parseInt(req.query._start as string) || 0;
  const limit = parseInt(req.query._end as string) || 10;
  const actualLimit = limit - page;

  const [items, totalResult] = await Promise.all([
    db.all(`
      SELECT
        id, item_code, name, type, description, rarity,
        price_coins, price_gems, max_stack, is_purchasable,
        is_active, created_at, updated_at
      FROM items
      ORDER BY id ASC
      LIMIT ? OFFSET ?
    `, [actualLimit, page]),

    db.get('SELECT COUNT(*) as total FROM items')
  ]);

  // React Admin需要的格式
  res.set('X-Total-Count', totalResult.total.toString());
  res.json(items);
}));

// 获取单个道具
router.get('/items/:id', asyncHandler(async (req: AuthRequest, res: Response) => {
  const itemId = parseInt(req.params.id);

  const item = await db.get(
    'SELECT * FROM items WHERE id = ?',
    [itemId]
  );

  if (!item) {
    throw createError(404, 'ITEM_001', '道具不存在');
  }

  res.json(item);
}));

// 道具创建验证模式
const createItemSchema = Joi.object({
  item_code: Joi.string().required(),
  name: Joi.string().required(),
  type: Joi.string().valid('tool', 'booster', 'consumable').required(),
  description: Joi.string().required(),
  rarity: Joi.string().valid('common', 'rare', 'epic', 'legendary').default('common'),
  effect_data: Joi.string().optional(),
  price_coins: Joi.number().integer().min(0).default(0),
  price_gems: Joi.number().integer().min(0).default(0),
  max_stack: Joi.number().integer().min(1).default(99),
  icon_url: Joi.string().optional(),
  is_purchasable: Joi.boolean().default(true),
  is_active: Joi.boolean().default(true)
});

// 创建道具
router.post('/items', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = createItemSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const {
    item_code, name, type, description, rarity, effect_data,
    price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active
  } = value;

  const result = await db.run(`
    INSERT INTO items (
      item_code, name, type, description, rarity, effect_data,
      price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [item_code, name, type, description, rarity, effect_data,
      price_coins, price_gems, max_stack, icon_url, is_purchasable ? 1 : 0, is_active ? 1 : 0]);

  const newItem = await db.get(
    'SELECT * FROM items WHERE id = ?',
    [result.lastID]
  );

  res.status(201).json(newItem);
}));

// 更新道具
router.put('/items/:id', asyncHandler(async (req: AuthRequest, res: Response) => {
  const itemId = parseInt(req.params.id);
  const { error, value } = createItemSchema.validate(req.body);

  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  // 检查道具是否存在
  const existingItem = await db.get('SELECT id FROM items WHERE id = ?', [itemId]);
  if (!existingItem) {
    throw createError(404, 'ITEM_001', '道具不存在');
  }

  const {
    item_code, name, type, description, rarity, effect_data,
    price_coins, price_gems, max_stack, icon_url, is_purchasable, is_active
  } = value;

  await db.run(`
    UPDATE items SET
      item_code = ?, name = ?, type = ?, description = ?, rarity = ?, effect_data = ?,
      price_coins = ?, price_gems = ?, max_stack = ?, icon_url = ?,
      is_purchasable = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `, [item_code, name, type, description, rarity, effect_data,
      price_coins, price_gems, max_stack, icon_url, is_purchasable ? 1 : 0, is_active ? 1 : 0, itemId]);

  const updatedItem = await db.get(
    'SELECT * FROM items WHERE id = ?',
    [itemId]
  );

  res.json(updatedItem);
}));

// 删除道具
router.delete('/items/:id', asyncHandler(async (req: AuthRequest, res: Response) => {
  const itemId = parseInt(req.params.id);

  // 检查道具是否存在
  const existingItem = await db.get('SELECT id FROM items WHERE id = ?', [itemId]);
  if (!existingItem) {
    throw createError(404, 'ITEM_001', '道具不存在');
  }

  await db.run('DELETE FROM items WHERE id = ?', [itemId]);

  res.json({
    success: true,
    message: '道具删除成功'
  });
}));

export { router as adminRoutes };
