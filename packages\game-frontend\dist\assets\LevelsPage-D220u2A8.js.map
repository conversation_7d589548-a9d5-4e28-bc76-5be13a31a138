{"version": 3, "file": "LevelsPage-D220u2A8.js", "sources": ["../../src/pages/LevelsPage.tsx"], "sourcesContent": ["import React from 'react'\nimport styled from 'styled-components'\nimport { useNavigate } from 'react-router-dom'\nimport { useQuery } from 'react-query'\n// import { LevelCard as UILevelCard } from '../components/UI/Card'\n// import Button from '../components/UI/Button'\nimport { theme, media } from '../styles/theme'\n\nconst Container = styled.div`\n  flex: 1;\n  padding: 2rem;\n  min-height: 100vh;\n  max-width: 1200px;\n  margin: 0 auto;\n\n  ${media.maxMd} {\n    padding: 1rem;\n  }\n\n  ${media.maxSm} {\n    padding: 0.5rem;\n  }\n`\n\nconst Title = styled.h1`\n  color: ${theme.colors.secondary[400]};\n  font-size: ${theme.fontSizes['4xl']};\n  margin-bottom: 3rem;\n  text-align: center;\n  font-weight: ${theme.fontWeights.bold};\n  font-family: ${theme.fonts.primary};\n  text-shadow: 0 0 30px rgba(251, 191, 36, 0.6);\n  background: linear-gradient(135deg, ${theme.colors.secondary[300]} 0%, ${theme.colors.secondary[500]} 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  ${media.maxMd} {\n    font-size: ${theme.fontSizes['3xl']};\n    margin-bottom: 2rem;\n  }\n\n  ${media.maxSm} {\n    font-size: ${theme.fontSizes['2xl']};\n    margin-bottom: 1.5rem;\n  }\n`\n\nconst LevelsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n  gap: 1.5rem;\n  max-width: 1000px;\n  margin: 0 auto 3rem;\n\n  ${media.maxMd} {\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n    gap: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  ${media.maxSm} {\n    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));\n    gap: 0.75rem;\n    margin-bottom: 1.5rem;\n  }\n`\n\nconst LevelCard = styled.div<{ isLocked: boolean; isCompleted: boolean }>`\n  aspect-ratio: 1;\n  background: ${props =>\n    props.isCompleted\n      ? `linear-gradient(135deg, ${theme.colors.success} 0%, #059669 100%)`\n      : props.isLocked\n        ? `linear-gradient(135deg, ${theme.colors.gray[700]} 0%, ${theme.colors.gray[800]} 100%)`\n        : `linear-gradient(135deg, ${theme.colors.primary[500]} 0%, ${theme.colors.primary[600]} 100%)`\n  };\n  border-radius: ${theme.borderRadius.xl};\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};\n  transition: all ${theme.transitions.base} ease-in-out;\n  border: 2px solid transparent;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n  box-shadow: ${theme.shadows.lg};\n\n  &:hover {\n    ${props => !props.isLocked && `\n      transform: translateY(-6px);\n      box-shadow: ${theme.shadows.xl}, ${props.isCompleted\n        ? '0 0 30px rgba(16, 185, 129, 0.4)'\n        : '0 0 30px rgba(99, 102, 241, 0.4)'\n      };\n      border-color: ${theme.colors.secondary[400]};\n    `}\n  }\n\n  &:active {\n    ${props => !props.isLocked && `\n      transform: translateY(-2px);\n    `}\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n    pointer-events: none;\n    opacity: 0.8;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 1px;\n    left: 1px;\n    right: 1px;\n    bottom: 1px;\n    border-radius: ${theme.borderRadius.lg};\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 30%);\n    pointer-events: none;\n  }\n`\n\nconst LevelNumber = styled.div`\n  font-size: ${theme.fontSizes.xl};\n  font-weight: ${theme.fontWeights.bold};\n  color: ${theme.colors.text.primary};\n  margin-bottom: ${theme.spacing[2]};\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n\n  ${media.maxSm} {\n    font-size: ${theme.fontSizes.lg};\n  }\n`\n\nconst LevelStars = styled.div`\n  display: flex;\n  gap: ${theme.spacing[1]};\n  margin-bottom: ${theme.spacing[1]};\n`\n\nconst Star = styled.div<{ filled: boolean }>`\n  width: 14px;\n  height: 14px;\n  color: ${props => props.filled ? theme.colors.secondary[400] : 'rgba(255, 255, 255, 0.3)'};\n  font-size: 14px;\n  filter: ${props => props.filled ? 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.6))' : 'none'};\n  transition: all ${theme.transitions.fast} ease-in-out;\n\n  ${media.maxSm} {\n    width: 12px;\n    height: 12px;\n    font-size: 12px;\n  }\n`\n\nconst LockIcon = styled.div`\n  font-size: 1.5rem;\n  color: rgba(255, 255, 255, 0.5);\n`\n\nconst LevelInfo = styled.div`\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.8);\n  text-align: center;\n  margin-top: 0.25rem;\n`\n\n// Mock levels data\nconst mockLevels = Array.from({ length: 50 }, (_, index) => ({\n  id: index + 1,\n  levelNumber: index + 1,\n  targetScore: 1000 + (index * 500),\n  maxMoves: 20 - Math.floor(index / 10),\n  difficulty: Math.floor(index / 10) + 1,\n  isLocked: index > 4, // First 5 levels unlocked\n  userProgress: index < 3 ? {\n    isCompleted: true,\n    stars: Math.floor(Math.random() * 3) + 1,\n    bestScore: 1000 + (index * 600)\n  } : null\n}))\n\nconst LevelsPage: React.FC = () => {\n  const navigate = useNavigate()\n\n  // In a real app, this would fetch from the API\n  const { data: levels = mockLevels, isLoading } = useQuery(\n    'levels',\n    async () => {\n      try {\n        const token = localStorage.getItem('token')\n        const response = await fetch('/api/levels', {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        })\n        \n        if (!response.ok) {\n          throw new Error('Failed to fetch levels')\n        }\n        \n        return response.json()\n      } catch (error) {\n        console.warn('Backend not available, using mock data')\n        return mockLevels\n      }\n    },\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  )\n\n  const handleLevelClick = (level: any) => {\n    if (level.isLocked) return\n    \n    // Navigate to game with selected level\n    navigate(`/game?level=${level.levelNumber}`)\n  }\n\n  if (isLoading) {\n    return (\n      <Container>\n        <Title>关卡选择</Title>\n        <div style={{ textAlign: 'center', color: 'white', marginTop: '2rem' }}>\n          正在加载关卡...\n        </div>\n      </Container>\n    )\n  }\n\n  return (\n    <Container>\n      <Title>关卡选择</Title>\n      <LevelsGrid>\n        {levels.map((level: any) => (\n          <LevelCard\n            key={level.id}\n            isLocked={level.isLocked}\n            isCompleted={!!level.userProgress?.isCompleted}\n            onClick={() => handleLevelClick(level)}\n          >\n            {level.isLocked ? (\n              <>\n                <LockIcon>🔒</LockIcon>\n                <LevelInfo>已锁定</LevelInfo>\n              </>\n            ) : (\n              <>\n                <LevelNumber>{level.levelNumber}</LevelNumber>\n                {level.userProgress?.isCompleted && (\n                  <LevelStars>\n                    {[1, 2, 3].map(star => (\n                      <Star key={star} filled={star <= (level.userProgress?.stars || 0)}>\n                        ⭐\n                      </Star>\n                    ))}\n                  </LevelStars>\n                )}\n                <LevelInfo>\n                  目标: {level.targetScore?.toLocaleString()}<br />\n                  步数: {level.maxMoves}\n                </LevelInfo>\n              </>\n            )}\n          </LevelCard>\n        ))}\n      </LevelsGrid>\n    </Container>\n  )\n}\n\nexport default LevelsPage\n"], "names": ["Container", "styled", "media", "Title", "theme", "LevelsGrid", "LevelCard", "props", "LevelNumber", "LevelStars", "Star", "LockIcon", "LevelInfo", "mockLevels", "_", "index", "LevelsPage", "navigate", "useNavigate", "levels", "isLoading", "useQuery", "token", "response", "handleLevelClick", "level", "jsx", "_a", "jsxs", "Fragment", "_b", "star", "_c"], "mappings": "wKAQA,MAAMA,EAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOrBC,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA,IAIXA,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTC,EAAQF,EAAO;AAAA,WACVG,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA,eACvBA,EAAM,UAAU,KAAK,CAAC;AAAA;AAAA;AAAA,iBAGpBA,EAAM,YAAY,IAAI;AAAA,iBACtBA,EAAM,MAAM,OAAO;AAAA;AAAA,wCAEIA,EAAM,OAAO,UAAU,GAAG,CAAC,QAAQA,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKlGF,EAAM,KAAK;AAAA,iBACEE,EAAM,UAAU,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,IAInCF,EAAM,KAAK;AAAA,iBACEE,EAAM,UAAU,KAAK,CAAC;AAAA;AAAA;AAAA,EAKjCC,EAAaJ,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOtBC,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMXA,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOTI,EAAYL,EAAO;AAAA;AAAA,gBAETM,GACZA,EAAM,YACF,2BAA2BH,EAAM,OAAO,OAAO,qBAC/CG,EAAM,SACJ,2BAA2BH,EAAM,OAAO,KAAK,GAAG,CAAC,QAAQA,EAAM,OAAO,KAAK,GAAG,CAAC,SAC/E,2BAA2BA,EAAM,OAAO,QAAQ,GAAG,CAAC,QAAQA,EAAM,OAAO,QAAQ,GAAG,CAAC,QAC7F;AAAA,mBACiBA,EAAM,aAAa,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,YAK5BG,GAASA,EAAM,SAAW,cAAgB,SAAS;AAAA,oBAC3CH,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,gBAK1BA,EAAM,QAAQ,EAAE;AAAA;AAAA;AAAA,MAG1BG,GAAS,CAACA,EAAM,UAAY;AAAA;AAAA,oBAEdH,EAAM,QAAQ,EAAE,KAAKG,EAAM,YACrC,mCACA,kCACJ;AAAA,sBACgBH,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA,KAC5C;AAAA;AAAA;AAAA;AAAA,MAICG,GAAS,CAACA,EAAM,UAAY;AAAA;AAAA,KAE7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAsBgBH,EAAM,aAAa,EAAE;AAAA;AAAA;AAAA;AAAA,EAMpCI,EAAcP,EAAO;AAAA,eACZG,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,IAAI;AAAA,WAC5BA,EAAM,OAAO,KAAK,OAAO;AAAA,mBACjBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG/BF,EAAM,KAAK;AAAA,iBACEE,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BK,EAAaR,EAAO;AAAA;AAAA,SAEjBG,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACNA,EAAM,QAAQ,CAAC,CAAC;AAAA,EAG7BM,EAAOT,EAAO;AAAA;AAAA;AAAA,WAGTM,GAASA,EAAM,OAASH,EAAM,OAAO,UAAU,GAAG,EAAI,0BAA0B;AAAA;AAAA,YAE/EG,GAASA,EAAM,OAAS,+CAAiD,MAAM;AAAA,oBACvEH,EAAM,YAAY,IAAI;AAAA;AAAA,IAEtCF,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOTS,EAAWV,EAAO;AAAA;AAAA;AAAA,EAKlBW,EAAYX,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnBY,EAAa,MAAM,KAAK,CAAE,OAAQ,IAAM,CAACC,EAAGC,KAAW,CAC3D,GAAIA,EAAQ,EACZ,YAAaA,EAAQ,EACrB,YAAa,IAAQA,EAAQ,IAC7B,SAAU,GAAK,KAAK,MAAMA,EAAQ,EAAE,EACpC,WAAY,KAAK,MAAMA,EAAQ,EAAE,EAAI,EACrC,SAAUA,EAAQ,EAClB,aAAcA,EAAQ,EAAI,CACxB,YAAa,GACb,MAAO,KAAK,MAAM,KAAK,OAAA,EAAW,CAAC,EAAI,EACvC,UAAW,IAAQA,EAAQ,GAAA,EACzB,IACN,EAAE,EAEIC,EAAuB,IAAM,CACjC,MAAMC,EAAWC,EAAA,EAGX,CAAE,KAAMC,EAASN,EAAY,UAAAO,GAAcC,EAC/C,SACA,SAAY,CACV,GAAI,CACF,MAAMC,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,cAAe,CAC1C,QAAS,CACP,cAAiB,UAAUD,CAAK,EAAA,CAClC,CACD,EAED,GAAI,CAACC,EAAS,GACZ,MAAM,IAAI,MAAM,wBAAwB,EAG1C,OAAOA,EAAS,KAAA,CAClB,MAAgB,CACd,eAAQ,KAAK,wCAAwC,EAC9CV,CACT,CACF,EACA,CACE,UAAW,EAAI,GAAK,GAAA,CACtB,EAGIW,EAAoBC,GAAe,CACnCA,EAAM,UAGVR,EAAS,eAAeQ,EAAM,WAAW,EAAE,CAC7C,EAEA,OAAIL,SAECpB,EAAA,CACC,SAAA,CAAA0B,EAAAA,IAACvB,GAAM,SAAA,MAAA,CAAI,EACXuB,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,MAAO,QAAS,UAAW,QAAU,SAAA,WAAA,CAExE,CAAA,EACF,SAKD1B,EAAA,CACC,SAAA,CAAA0B,EAAAA,IAACvB,GAAM,SAAA,MAAA,CAAI,EACXuB,EAAAA,IAACrB,EAAA,CACE,SAAAc,EAAO,IAAKM,cACXC,OAAAA,EAAAA,IAACpB,EAAA,CAEC,SAAUmB,EAAM,SAChB,YAAa,CAAC,GAACE,EAAAF,EAAM,eAAN,MAAAE,EAAoB,aACnC,QAAS,IAAMH,EAAiBC,CAAK,EAEpC,SAAAA,EAAM,SACLG,EAAAA,KAAAC,EAAAA,SAAA,CACE,SAAA,CAAAH,EAAAA,IAACf,GAAS,SAAA,IAAA,CAAE,EACZe,EAAAA,IAACd,GAAU,SAAA,KAAA,CAAG,CAAA,CAAA,CAChB,EAEAgB,EAAAA,KAAAC,EAAAA,SAAA,CACE,SAAA,CAAAH,EAAAA,IAAClB,EAAA,CAAa,WAAM,WAAA,CAAY,IAC/BsB,EAAAL,EAAM,eAAN,YAAAK,EAAoB,cACnBJ,EAAAA,IAACjB,GACE,SAAA,CAAC,EAAG,EAAG,CAAC,EAAE,cACTiB,OAAAA,MAAChB,EAAA,CAAgB,OAAQqB,MAASJ,EAAAF,EAAM,eAAN,YAAAE,EAAoB,QAAS,GAAI,SAAA,GAAA,EAAxDI,CAEX,EACD,CAAA,CACH,SAEDnB,EAAA,CAAU,SAAA,CAAA,QACJoB,EAAAP,EAAM,cAAN,YAAAO,EAAmB,uBAAkB,KAAA,EAAG,EAAE,OAC1CP,EAAM,QAAA,CAAA,CACb,CAAA,CAAA,CACF,CAAA,EA1BGA,EAAM,EAAA,EA6Bd,CAAA,CACH,CAAA,EACF,CAEJ"}