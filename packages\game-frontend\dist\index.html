<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta name="description" content="星光对对碰 - 一款精美的三消游戏" />
    <meta name="theme-color" content="#4f46e5" />
    
    <!-- PWA相关 -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>星光对对碰 - Starry Match</title>
    
    <style>
      /* 初始加载样式 */
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background: linear-gradient(135deg, #1e1b4b 0%, #3730a3 100%);
        color: white;
        overflow: hidden;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .loading-screen {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2rem;
      }
      
      .loading-logo {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: glow 2s ease-in-out infinite alternate;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid #fbbf24;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes glow {
        from { text-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
        to { text-shadow: 0 0 30px rgba(251, 191, 36, 0.8); }
      }
      
      /* 移动端适配 */
      @media (max-width: 768px) {
        .loading-logo {
          font-size: 2rem;
        }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CNCEp3EQ.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-Dneogk0_.js">
    <link rel="modulepreload" crossorigin href="/assets/router-DMCr7QLp.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-ldAE8JkK.js">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body>
    <div id="root">
      <div class="loading-screen">
        <div class="loading-logo">✨ 星光对对碰</div>
        <div class="loading-spinner"></div>
        <div style="font-size: 0.9rem; opacity: 0.8;">正在加载游戏...</div>
      </div>
    </div>
  </body>
</html>
