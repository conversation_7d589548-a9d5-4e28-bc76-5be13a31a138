import {
  Achievement,
  PlayerStats,
  // AchievementProgress,
  AchievementUnlockedEvent,
  ACHIEVEMENTS
} from '../types/achievements'

export class AchievementManager {
  private achievements: Map<string, Achievement> = new Map()
  private playerStats: PlayerStats
  private onAchievementUnlocked?: (event: AchievementUnlockedEvent) => void

  constructor(
    initialStats?: Partial<PlayerStats>,
    onAchievementUnlocked?: (event: AchievementUnlockedEvent) => void
  ) {
    // 初始化成就
    ACHIEVEMENTS.forEach(achievement => {
      this.achievements.set(achievement.id, { ...achievement })
    })

    // 初始化玩家统计
    this.playerStats = {
      totalScore: 0,
      totalGamesPlayed: 0,
      totalGamesWon: 0,
      totalTimePlayed: 0,
      levelsCompleted: 0,
      highestLevel: 0,
      perfectLevels: 0,
      maxCombo: 0,
      totalCombos: 0,
      comboCount: {},
      specialGemsCreated: {},
      specialGemsUsed: {},
      powerupsUsed: {},
      powerupsPurchased: {},
      gemsMatched: 0,
      totalMoves: 0,
      fastestLevel: Infinity,
      achievementsUnlocked: 0,
      badgesCollected: [],
      titlesEarned: [],
      ...initialStats
    }

    this.onAchievementUnlocked = onAchievementUnlocked
  }

  // 更新玩家统计并检查成就
  public updateStats(updates: Partial<PlayerStats>): AchievementUnlockedEvent[] {
    const oldStats = { ...this.playerStats }
    
    // 更新统计数据
    Object.assign(this.playerStats, updates)
    
    // 检查成就解锁
    return this.checkAchievements(oldStats)
  }

  // 记录游戏结束
  public recordGameEnd(gameData: {
    score: number
    won: boolean
    level: number
    timePlayed: number
    moves: number
    maxCombo: number
    specialGemsCreated: Record<string, number>
    powerupsUsed: Record<string, number>
    perfect?: boolean
  }): AchievementUnlockedEvent[] {
    const updates: Partial<PlayerStats> = {
      totalScore: this.playerStats.totalScore + gameData.score,
      totalGamesPlayed: this.playerStats.totalGamesPlayed + 1,
      totalTimePlayed: this.playerStats.totalTimePlayed + gameData.timePlayed,
      totalMoves: this.playerStats.totalMoves + gameData.moves,
      maxCombo: Math.max(this.playerStats.maxCombo, gameData.maxCombo),
      totalCombos: this.playerStats.totalCombos + (gameData.maxCombo > 1 ? 1 : 0)
    }

    if (gameData.won) {
      updates.totalGamesWon = this.playerStats.totalGamesWon + 1
      updates.levelsCompleted = this.playerStats.levelsCompleted + 1
      updates.highestLevel = Math.max(this.playerStats.highestLevel, gameData.level)
      
      if (gameData.perfect) {
        updates.perfectLevels = this.playerStats.perfectLevels + 1
      }
      
      if (gameData.timePlayed < this.playerStats.fastestLevel) {
        updates.fastestLevel = gameData.timePlayed
      }
    }

    // 更新连击统计
    if (gameData.maxCombo > 1) {
      const comboCount = { ...this.playerStats.comboCount }
      comboCount[gameData.maxCombo] = (comboCount[gameData.maxCombo] || 0) + 1
      updates.comboCount = comboCount
    }

    // 更新特殊宝石统计
    const specialGemsCreated = { ...this.playerStats.specialGemsCreated }
    Object.entries(gameData.specialGemsCreated).forEach(([type, count]) => {
      specialGemsCreated[type] = (specialGemsCreated[type] || 0) + count
    })
    updates.specialGemsCreated = specialGemsCreated

    // 更新道具使用统计
    const powerupsUsed = { ...this.playerStats.powerupsUsed }
    Object.entries(gameData.powerupsUsed).forEach(([type, count]) => {
      powerupsUsed[type] = (powerupsUsed[type] || 0) + count
    })
    updates.powerupsUsed = powerupsUsed

    return this.updateStats(updates)
  }

  // 检查成就解锁
  private checkAchievements(_oldStats: PlayerStats): AchievementUnlockedEvent[] {
    const unlockedEvents: AchievementUnlockedEvent[] = []

    for (const achievement of this.achievements.values()) {
      if (achievement.unlocked) continue

      const isUnlocked = this.checkAchievementCondition(achievement)
      
      if (isUnlocked) {
        achievement.unlocked = true
        achievement.unlockedAt = new Date()
        achievement.progress = 100

        const event = this.createUnlockedEvent(achievement)
        unlockedEvents.push(event)

        // 应用奖励
        this.applyRewards(achievement)

        // 触发回调
        if (this.onAchievementUnlocked) {
          this.onAchievementUnlocked(event)
        }
      } else {
        // 更新进度
        achievement.progress = this.calculateProgress(achievement)
      }
    }

    return unlockedEvents
  }

  // 检查单个成就条件
  private checkAchievementCondition(achievement: Achievement): boolean {
    const { condition } = achievement
    const stats = this.playerStats

    switch (condition.type) {
      case 'single_game_score':
        // 这个需要在游戏结束时单独检查
        return false
      
      case 'total_score':
        return stats.totalScore >= condition.target
      
      case 'levels_completed':
        return stats.levelsCompleted >= condition.target
      
      case 'perfect_levels':
        return stats.perfectLevels >= condition.target
      
      case 'max_combo':
        return stats.maxCombo >= condition.target
      
      case 'games_won':
        return stats.totalGamesWon >= condition.target
      
      case 'total_time_played':
        return stats.totalTimePlayed >= condition.target
      
      case 'fastest_level':
        return stats.fastestLevel <= condition.target
      
      case 'total_powerups_used':
        const totalPowerups = Object.values(stats.powerupsUsed).reduce((sum, count) => sum + count, 0)
        return totalPowerups >= condition.target
      
      default:
        // 处理特殊宝石相关条件
        if (condition.type.startsWith('special_gems_created_')) {
          const gemType = condition.type.replace('special_gems_created_', '')
          return (stats.specialGemsCreated[gemType] || 0) >= condition.target
        }
        
        return false
    }
  }

  // 计算成就进度
  private calculateProgress(achievement: Achievement): number {
    const { condition } = achievement
    const stats = this.playerStats

    let current = 0

    switch (condition.type) {
      case 'total_score':
        current = stats.totalScore
        break
      case 'levels_completed':
        current = stats.levelsCompleted
        break
      case 'perfect_levels':
        current = stats.perfectLevels
        break
      case 'max_combo':
        current = stats.maxCombo
        break
      case 'games_won':
        current = stats.totalGamesWon
        break
      case 'total_time_played':
        current = stats.totalTimePlayed
        break
      case 'total_powerups_used':
        current = Object.values(stats.powerupsUsed).reduce((sum, count) => sum + count, 0)
        break
      default:
        if (condition.type.startsWith('special_gems_created_')) {
          const gemType = condition.type.replace('special_gems_created_', '')
          current = stats.specialGemsCreated[gemType] || 0
        }
        break
    }

    return Math.min(100, (current / condition.target) * 100)
  }

  // 创建解锁事件
  private createUnlockedEvent(achievement: Achievement): AchievementUnlockedEvent {
    return {
      achievement: { ...achievement },
      timestamp: new Date(),
      newRewards: {
        coins: achievement.rewards.coins || 0,
        powerups: achievement.rewards.powerups || [],
        titles: achievement.rewards.title ? [achievement.rewards.title] : [],
        badges: achievement.rewards.badge ? [achievement.rewards.badge] : []
      }
    }
  }

  // 应用奖励
  private applyRewards(achievement: Achievement): void {
    this.playerStats.achievementsUnlocked++
    
    if (achievement.rewards.title) {
      this.playerStats.titlesEarned.push(achievement.rewards.title)
    }
    
    if (achievement.rewards.badge) {
      this.playerStats.badgesCollected.push(achievement.rewards.badge)
    }
  }

  // 检查单局分数成就
  public checkSingleGameScore(score: number): AchievementUnlockedEvent[] {
    const events: AchievementUnlockedEvent[] = []
    
    for (const achievement of this.achievements.values()) {
      if (achievement.unlocked || achievement.condition.type !== 'single_game_score') {
        continue
      }
      
      if (score >= achievement.condition.target) {
        achievement.unlocked = true
        achievement.unlockedAt = new Date()
        achievement.progress = 100
        
        const event = this.createUnlockedEvent(achievement)
        events.push(event)
        
        this.applyRewards(achievement)
        
        if (this.onAchievementUnlocked) {
          this.onAchievementUnlocked(event)
        }
      }
    }
    
    return events
  }

  // 获取所有成就
  public getAllAchievements(): Achievement[] {
    return Array.from(this.achievements.values())
      .filter(achievement => !achievement.hidden || achievement.unlocked)
      .sort((a, b) => a.order - b.order)
  }

  // 获取已解锁成就
  public getUnlockedAchievements(): Achievement[] {
    return this.getAllAchievements().filter(achievement => achievement.unlocked)
  }

  // 获取进行中的成就
  public getInProgressAchievements(): Achievement[] {
    return this.getAllAchievements().filter(achievement => !achievement.unlocked && achievement.progress > 0)
  }

  // 获取玩家统计
  public getPlayerStats(): PlayerStats {
    return { ...this.playerStats }
  }

  // 获取成就完成率
  public getCompletionRate(): number {
    const total = this.getAllAchievements().length
    const unlocked = this.getUnlockedAchievements().length
    return total > 0 ? (unlocked / total) * 100 : 0
  }
}
