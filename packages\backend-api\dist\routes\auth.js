"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../database/database");
const password_1 = require("../utils/password");
const jwt_1 = require("../utils/jwt");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.authRoutes = router;
const db = database_1.Database.getInstance();
const registerSchema = joi_1.default.object({
    username: joi_1.default.string().alphanum().min(3).max(30).required(),
    email: joi_1.default.string().email().optional(),
    password: joi_1.default.string().min(6).required(),
    displayName: joi_1.default.string().max(50).optional()
});
const loginSchema = joi_1.default.object({
    username: joi_1.default.string().required(),
    password: joi_1.default.string().required()
});
router.post('/register', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const { username, email, password, displayName } = value;
    const existingUser = await db.get('SELECT id FROM users WHERE username = ?', [username]);
    if (existingUser) {
        throw (0, errorHandler_1.createError)(409, 'USER_001', '用户名已存在');
    }
    if (email) {
        const existingEmail = await db.get('SELECT id FROM users WHERE email = ?', [email]);
        if (existingEmail) {
            throw (0, errorHandler_1.createError)(409, 'USER_003', '邮箱已被使用');
        }
    }
    const passwordHash = await (0, password_1.hashPassword)(password);
    const result = await db.run(`INSERT INTO users (username, email, password_hash, display_name, coins, gems) 
     VALUES (?, ?, ?, ?, ?, ?)`, [username, email, passwordHash, displayName || username, 100, 5]);
    const userId = result.lastID;
    const tokenPayload = { id: userId, username };
    const token = (0, jwt_1.generateToken)(tokenPayload);
    const refreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
    const user = await db.get(`SELECT id, username, display_name, coins, gems, current_level, total_score 
     FROM users WHERE id = ?`, [userId]);
    res.status(201).json({
        success: true,
        data: {
            user: {
                id: user.id,
                username: user.username,
                displayName: user.display_name,
                coins: user.coins,
                gems: user.gems,
                currentLevel: user.current_level,
                totalScore: user.total_score
            },
            token,
            refreshToken
        },
        message: '注册成功'
    });
}));
router.post('/login', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const { username, password } = value;
    const user = await db.get(`SELECT id, username, password_hash, display_name, coins, gems, current_level, total_score 
     FROM users WHERE username = ?`, [username]);
    if (!user) {
        throw (0, errorHandler_1.createError)(401, 'USER_002', '用户名或密码错误');
    }
    const isValidPassword = await (0, password_1.comparePassword)(password, user.password_hash);
    if (!isValidPassword) {
        throw (0, errorHandler_1.createError)(401, 'USER_002', '用户名或密码错误');
    }
    await db.run('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);
    const tokenPayload = { id: user.id, username: user.username };
    const token = (0, jwt_1.generateToken)(tokenPayload);
    const refreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                username: user.username,
                displayName: user.display_name,
                coins: user.coins,
                gems: user.gems,
                currentLevel: user.current_level,
                totalScore: user.total_score
            },
            token,
            refreshToken
        },
        message: '登录成功'
    });
}));
router.post('/refresh', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const tokenPayload = { id: user.id, username: user.username };
    const token = (0, jwt_1.generateToken)(tokenPayload);
    const refreshToken = (0, jwt_1.generateRefreshToken)(tokenPayload);
    res.json({
        success: true,
        data: {
            token,
            refreshToken
        },
        message: 'Token刷新成功'
    });
}));
router.get('/verify', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const user = await db.get(`SELECT id, username, display_name, coins, gems, current_level, total_score
     FROM users WHERE id = ?`, [userId]);
    if (!user) {
        throw (0, errorHandler_1.createError)(404, 'USER_002', '用户不存在');
    }
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                username: user.username,
                displayName: user.display_name,
                coins: user.coins,
                gems: user.gems,
                currentLevel: user.current_level,
                totalScore: user.total_score
            }
        },
        message: 'Token验证成功'
    });
}));
router.get('/me', auth_1.authenticateToken, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const user = await db.get(`SELECT id, username, email, display_name, coins, gems, current_level, total_score,
            created_at, last_login_at, is_active, is_premium
     FROM users WHERE id = ?`, [userId]);
    if (!user) {
        throw (0, errorHandler_1.createError)(404, 'USER_002', '用户不存在');
    }
    res.json({
        id: user.id,
        username: user.username,
        email: user.email,
        level: user.current_level,
        experience: user.total_score,
        coins: user.coins,
        gems: user.gems,
        isActive: user.is_active === 1,
        isPremium: user.is_premium === 1,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at,
    });
}));
//# sourceMappingURL=auth.js.map