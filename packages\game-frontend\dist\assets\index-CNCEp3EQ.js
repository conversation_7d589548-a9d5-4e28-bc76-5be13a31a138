const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/GamePage-h55HzyeI.js","assets/vendor-Dneogk0_.js","assets/ui-ldAE8JkK.js","assets/router-DMCr7QLp.js","assets/GameBoard-DvXOxAln.js","assets/animationManager-DTsUvCq5.js","assets/specialGems-xbtj_zef.js","assets/GameOverModal-4jLpsjyZ.js","assets/PowerUpPanel-Cy3BrM2g.js","assets/powerups-DNw9s1Qv.js","assets/Button-BlkTGlvm.js","assets/Card-B65VmGcU.js","assets/powerUpManager-DAya6dWG.js","assets/LevelsPage-D220u2A8.js","assets/LeaderboardPage-x048hut8.js","assets/leaderboard-Or32oZ16.js","assets/AchievementsPage-BeJEZHzx.js","assets/achievements-BKGiRpq5.js","assets/ProfilePage-CsLxkWtu.js","assets/ShareModal-GIcMGaE5.js","assets/specialGemManager-S1dnzDs3.js","assets/achievementManager-Bt3BvN3k.js"])))=>i.map(i=>d[i]);
var Nt=Object.defineProperty;var Kt=(i,e,r)=>e in i?Nt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[e]=r;var F=(i,e,r)=>Kt(i,typeof e!="symbol"?e+"":e,r);import{r as m,b as Vt,c as Ht,a as j}from"./vendor-Dneogk0_.js";import{L as ue,u as Wt,R as Jt,a as U,N as Xe,B as Yt}from"./router-DMCr7QLp.js";import{d as v,f as Xt,o as Zt}from"./ui-ldAE8JkK.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const s of n)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&t(o)}).observe(document,{childList:!0,subtree:!0});function r(n){const s={};return n.integrity&&(s.integrity=n.integrity),n.referrerPolicy&&(s.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?s.credentials="include":n.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function t(n){if(n.ep)return;n.ep=!0;const s=r(n);fetch(n.href,s)}})();var St={exports:{}},Ie={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var er=m,tr=Symbol.for("react.element"),rr=Symbol.for("react.fragment"),nr=Object.prototype.hasOwnProperty,ir=er.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sr={key:!0,ref:!0,__self:!0,__source:!0};function Ct(i,e,r){var t,n={},s=null,o=null;r!==void 0&&(s=""+r),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(o=e.ref);for(t in e)nr.call(e,t)&&!sr.hasOwnProperty(t)&&(n[t]=e[t]);if(i&&i.defaultProps)for(t in e=i.defaultProps,e)n[t]===void 0&&(n[t]=e[t]);return{$$typeof:tr,type:i,key:s,ref:o,props:n,_owner:ir.current}}Ie.Fragment=rr;Ie.jsx=Ct;Ie.jsxs=Ct;St.exports=Ie;var u=St.exports,Be={},rt=Vt;Be.createRoot=rt.createRoot,Be.hydrateRoot=rt.hydrateRoot;function Ge(i,e){return Ge=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,t){return r.__proto__=t,r},Ge(i,e)}function ee(i,e){i.prototype=Object.create(e.prototype),i.prototype.constructor=i,Ge(i,e)}var te=function(){function i(){this.listeners=[]}var e=i.prototype;return e.subscribe=function(t){var n=this,s=t||function(){};return this.listeners.push(s),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(o){return o!==s}),n.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},i}();function y(){return y=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)({}).hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},y.apply(null,arguments)}var Me=typeof window>"u";function R(){}function or(i,e){return typeof i=="function"?i(e):i}function Ne(i){return typeof i=="number"&&i>=0&&i!==1/0}function Ae(i){return Array.isArray(i)?i:[i]}function Ot(i,e){return Math.max(i+(e||0)-Date.now(),0)}function Ce(i,e,r){return ce(i)?typeof e=="function"?y({},r,{queryKey:i,queryFn:e}):y({},e,{queryKey:i}):i}function ar(i,e,r){return ce(i)?typeof e=="function"?y({},r,{mutationKey:i,mutationFn:e}):y({},e,{mutationKey:i}):typeof i=="function"?y({},e,{mutationFn:i}):y({},i)}function q(i,e,r){return ce(i)?[y({},e,{queryKey:i}),r]:[i||{},e]}function ur(i,e){if(i===!0&&e===!0||i==null&&e==null)return"all";if(i===!1&&e===!1)return"none";var r=i??!e;return r?"active":"inactive"}function nt(i,e){var r=i.active,t=i.exact,n=i.fetching,s=i.inactive,o=i.predicate,a=i.queryKey,c=i.stale;if(ce(a)){if(t){if(e.queryHash!==Ze(a,e.options))return!1}else if(!ke(e.queryKey,a))return!1}var l=ur(r,s);if(l==="none")return!1;if(l!=="all"){var d=e.isActive();if(l==="active"&&!d||l==="inactive"&&d)return!1}return!(typeof c=="boolean"&&e.isStale()!==c||typeof n=="boolean"&&e.isFetching()!==n||o&&!o(e))}function it(i,e){var r=i.exact,t=i.fetching,n=i.predicate,s=i.mutationKey;if(ce(s)){if(!e.options.mutationKey)return!1;if(r){if(W(e.options.mutationKey)!==W(s))return!1}else if(!ke(e.options.mutationKey,s))return!1}return!(typeof t=="boolean"&&e.state.status==="loading"!==t||n&&!n(e))}function Ze(i,e){var r=(e==null?void 0:e.queryKeyHashFn)||W;return r(i)}function W(i){var e=Ae(i);return cr(e)}function cr(i){return JSON.stringify(i,function(e,r){return Ke(r)?Object.keys(r).sort().reduce(function(t,n){return t[n]=r[n],t},{}):r})}function ke(i,e){return Et(Ae(i),Ae(e))}function Et(i,e){return i===e?!0:typeof i!=typeof e?!1:i&&e&&typeof i=="object"&&typeof e=="object"?!Object.keys(e).some(function(r){return!Et(i[r],e[r])}):!1}function Fe(i,e){if(i===e)return i;var r=Array.isArray(i)&&Array.isArray(e);if(r||Ke(i)&&Ke(e)){for(var t=r?i.length:Object.keys(i).length,n=r?e:Object.keys(e),s=n.length,o=r?[]:{},a=0,c=0;c<s;c++){var l=r?c:n[c];o[l]=Fe(i[l],e[l]),o[l]===i[l]&&a++}return t===s&&a===t?i:o}return e}function lr(i,e){if(i&&!e||e&&!i)return!1;for(var r in i)if(i[r]!==e[r])return!1;return!0}function Ke(i){if(!st(i))return!1;var e=i.constructor;if(typeof e>"u")return!0;var r=e.prototype;return!(!st(r)||!r.hasOwnProperty("isPrototypeOf"))}function st(i){return Object.prototype.toString.call(i)==="[object Object]"}function ce(i){return typeof i=="string"||Array.isArray(i)}function dr(i){return new Promise(function(e){setTimeout(e,i)})}function ot(i){Promise.resolve().then(i).catch(function(e){return setTimeout(function(){throw e})})}function jt(){if(typeof AbortController=="function")return new AbortController}var hr=function(i){ee(e,i);function e(){var t;return t=i.call(this)||this,t.setup=function(n){var s;if(!Me&&((s=window)!=null&&s.addEventListener)){var o=function(){return n()};return window.addEventListener("visibilitychange",o,!1),window.addEventListener("focus",o,!1),function(){window.removeEventListener("visibilitychange",o),window.removeEventListener("focus",o)}}},t}var r=e.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},r.setEventListener=function(n){var s,o=this;this.setup=n,(s=this.cleanup)==null||s.call(this),this.cleanup=n(function(a){typeof a=="boolean"?o.setFocused(a):o.onFocus()})},r.setFocused=function(n){this.focused=n,n&&this.onFocus()},r.onFocus=function(){this.listeners.forEach(function(n){n()})},r.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},e}(te),oe=new hr,fr=function(i){ee(e,i);function e(){var t;return t=i.call(this)||this,t.setup=function(n){var s;if(!Me&&((s=window)!=null&&s.addEventListener)){var o=function(){return n()};return window.addEventListener("online",o,!1),window.addEventListener("offline",o,!1),function(){window.removeEventListener("online",o),window.removeEventListener("offline",o)}}},t}var r=e.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},r.setEventListener=function(n){var s,o=this;this.setup=n,(s=this.cleanup)==null||s.call(this),this.cleanup=n(function(a){typeof a=="boolean"?o.setOnline(a):o.onOnline()})},r.setOnline=function(n){this.online=n,n&&this.onOnline()},r.onOnline=function(){this.listeners.forEach(function(n){n()})},r.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},e}(te),Oe=new fr;function pr(i){return Math.min(1e3*Math.pow(2,i),3e4)}function Te(i){return typeof(i==null?void 0:i.cancel)=="function"}var Pt=function(e){this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent};function Ee(i){return i instanceof Pt}var Rt=function(e){var r=this,t=!1,n,s,o,a;this.abort=e.abort,this.cancel=function(p){return n==null?void 0:n(p)},this.cancelRetry=function(){t=!0},this.continueRetry=function(){t=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(p,h){o=p,a=h});var c=function(h){r.isResolved||(r.isResolved=!0,e.onSuccess==null||e.onSuccess(h),s==null||s(),o(h))},l=function(h){r.isResolved||(r.isResolved=!0,e.onError==null||e.onError(h),s==null||s(),a(h))},d=function(){return new Promise(function(h){s=h,r.isPaused=!0,e.onPause==null||e.onPause()}).then(function(){s=void 0,r.isPaused=!1,e.onContinue==null||e.onContinue()})},f=function p(){if(!r.isResolved){var h;try{h=e.fn()}catch(g){h=Promise.reject(g)}n=function(x){if(!r.isResolved&&(l(new Pt(x)),r.abort==null||r.abort(),Te(h)))try{h.cancel()}catch{}},r.isTransportCancelable=Te(h),Promise.resolve(h).then(c).catch(function(g){var x,S;if(!r.isResolved){var C=(x=e.retry)!=null?x:3,w=(S=e.retryDelay)!=null?S:pr,b=typeof w=="function"?w(r.failureCount,g):w,P=C===!0||typeof C=="number"&&r.failureCount<C||typeof C=="function"&&C(r.failureCount,g);if(t||!P){l(g);return}r.failureCount++,e.onFail==null||e.onFail(r.failureCount,g),dr(b).then(function(){if(!oe.isFocused()||!Oe.isOnline())return d()}).then(function(){t?l(g):p()})}})}};f()},mr=function(){function i(){this.queue=[],this.transactions=0,this.notifyFn=function(r){r()},this.batchNotifyFn=function(r){r()}}var e=i.prototype;return e.batch=function(t){var n;this.transactions++;try{n=t()}finally{this.transactions--,this.transactions||this.flush()}return n},e.schedule=function(t){var n=this;this.transactions?this.queue.push(t):ot(function(){n.notifyFn(t)})},e.batchCalls=function(t){var n=this;return function(){for(var s=arguments.length,o=new Array(s),a=0;a<s;a++)o[a]=arguments[a];n.schedule(function(){t.apply(void 0,o)})}},e.flush=function(){var t=this,n=this.queue;this.queue=[],n.length&&ot(function(){t.batchNotifyFn(function(){n.forEach(function(s){t.notifyFn(s)})})})},e.setNotifyFunction=function(t){this.notifyFn=t},e.setBatchNotifyFunction=function(t){this.batchNotifyFn=t},i}(),O=new mr,_t=console;function De(){return _t}function vr(i){_t=i}var gr=function(){function i(r){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=r.defaultOptions,this.setOptions(r.options),this.observers=[],this.cache=r.cache,this.queryKey=r.queryKey,this.queryHash=r.queryHash,this.initialState=r.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=r.meta,this.scheduleGc()}var e=i.prototype;return e.setOptions=function(t){var n;this.options=y({},this.defaultOptions,t),this.meta=t==null?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},e.setDefaultOptions=function(t){this.defaultOptions=t},e.scheduleGc=function(){var t=this;this.clearGcTimeout(),Ne(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){t.optionalRemove()},this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(t,n){var s,o,a=this.state.data,c=or(t,a);return(s=(o=this.options).isDataEqual)!=null&&s.call(o,a,c)?c=a:this.options.structuralSharing!==!1&&(c=Fe(a,c)),this.dispatch({data:c,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),c},e.setState=function(t,n){this.dispatch({type:"setState",state:t,setStateOptions:n})},e.cancel=function(t){var n,s=this.promise;return(n=this.retryer)==null||n.cancel(t),s?s.then(R).catch(R):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some(function(t){return t.options.enabled!==!1})},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(t){return t.getCurrentResult().isStale})},e.isStaleByTime=function(t){return t===void 0&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Ot(this.state.dataUpdatedAt,t)},e.onFocus=function(){var t,n=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});n&&n.refetch(),(t=this.retryer)==null||t.continue()},e.onOnline=function(){var t,n=this.observers.find(function(s){return s.shouldFetchOnReconnect()});n&&n.refetch(),(t=this.retryer)==null||t.continue()},e.addObserver=function(t){this.observers.indexOf(t)===-1&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},e.removeObserver=function(t){this.observers.indexOf(t)!==-1&&(this.observers=this.observers.filter(function(n){return n!==t}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(t,n){var s=this,o,a,c;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var l;return(l=this.retryer)==null||l.continueRetry(),this.promise}}if(t&&this.setOptions(t),!this.options.queryFn){var d=this.observers.find(function(w){return w.options.queryFn});d&&this.setOptions(d.options)}var f=Ae(this.queryKey),p=jt(),h={queryKey:f,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(p)return s.abortSignalConsumed=!0,p.signal}});var g=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(h)):Promise.reject("Missing queryFn")},x={fetchOptions:n,options:this.options,queryKey:f,state:this.state,fetchFn:g,meta:this.meta};if((o=this.options.behavior)!=null&&o.onFetch){var S;(S=this.options.behavior)==null||S.onFetch(x)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((a=x.fetchOptions)==null?void 0:a.meta)){var C;this.dispatch({type:"fetch",meta:(C=x.fetchOptions)==null?void 0:C.meta})}return this.retryer=new Rt({fn:x.fetchFn,abort:p==null||(c=p.abort)==null?void 0:c.bind(p),onSuccess:function(b){s.setData(b),s.cache.config.onSuccess==null||s.cache.config.onSuccess(b,s),s.cacheTime===0&&s.optionalRemove()},onError:function(b){Ee(b)&&b.silent||s.dispatch({type:"error",error:b}),Ee(b)||(s.cache.config.onError==null||s.cache.config.onError(b,s),De().error(b)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:x.options.retry,retryDelay:x.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(t){var n=this;this.state=this.reducer(this.state,t),O.batch(function(){n.observers.forEach(function(s){s.onQueryUpdate(t)}),n.cache.notify({query:n,type:"queryUpdated",action:t})})},e.getDefaultState=function(t){var n=typeof t.initialData=="function"?t.initialData():t.initialData,s=typeof t.initialData<"u",o=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,a=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:a?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:a?"success":"idle"}},e.reducer=function(t,n){var s,o;switch(n.type){case"failed":return y({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return y({},t,{isPaused:!0});case"continue":return y({},t,{isPaused:!1});case"fetch":return y({},t,{fetchFailureCount:0,fetchMeta:(s=n.meta)!=null?s:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return y({},t,{data:n.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:(o=n.dataUpdatedAt)!=null?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var a=n.error;return Ee(a)&&a.revert&&this.revertState?y({},this.revertState):y({},t,{error:a,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return y({},t,{isInvalidated:!0});case"setState":return y({},t,n.state);default:return t}},i}(),yr=function(i){ee(e,i);function e(t){var n;return n=i.call(this)||this,n.config=t||{},n.queries=[],n.queriesMap={},n}var r=e.prototype;return r.build=function(n,s,o){var a,c=s.queryKey,l=(a=s.queryHash)!=null?a:Ze(c,s),d=this.get(l);return d||(d=new gr({cache:this,queryKey:c,queryHash:l,options:n.defaultQueryOptions(s),state:o,defaultOptions:n.getQueryDefaults(c),meta:s.meta}),this.add(d)),d},r.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},r.remove=function(n){var s=this.queriesMap[n.queryHash];s&&(n.destroy(),this.queries=this.queries.filter(function(o){return o!==n}),s===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},r.clear=function(){var n=this;O.batch(function(){n.queries.forEach(function(s){n.remove(s)})})},r.get=function(n){return this.queriesMap[n]},r.getAll=function(){return this.queries},r.find=function(n,s){var o=q(n,s),a=o[0];return typeof a.exact>"u"&&(a.exact=!0),this.queries.find(function(c){return nt(a,c)})},r.findAll=function(n,s){var o=q(n,s),a=o[0];return Object.keys(a).length>0?this.queries.filter(function(c){return nt(a,c)}):this.queries},r.notify=function(n){var s=this;O.batch(function(){s.listeners.forEach(function(o){o(n)})})},r.onFocus=function(){var n=this;O.batch(function(){n.queries.forEach(function(s){s.onFocus()})})},r.onOnline=function(){var n=this;O.batch(function(){n.queries.forEach(function(s){s.onOnline()})})},e}(te),br=function(){function i(r){this.options=y({},r.defaultOptions,r.options),this.mutationId=r.mutationId,this.mutationCache=r.mutationCache,this.observers=[],this.state=r.state||Mt(),this.meta=r.meta}var e=i.prototype;return e.setState=function(t){this.dispatch({type:"setState",state:t})},e.addObserver=function(t){this.observers.indexOf(t)===-1&&this.observers.push(t)},e.removeObserver=function(t){this.observers=this.observers.filter(function(n){return n!==t})},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(R).catch(R)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var t=this,n,s=this.state.status==="loading",o=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),o=o.then(function(){t.mutationCache.config.onMutate==null||t.mutationCache.config.onMutate(t.state.variables,t)}).then(function(){return t.options.onMutate==null?void 0:t.options.onMutate(t.state.variables)}).then(function(a){a!==t.state.context&&t.dispatch({type:"loading",context:a,variables:t.state.variables})})),o.then(function(){return t.executeMutation()}).then(function(a){n=a,t.mutationCache.config.onSuccess==null||t.mutationCache.config.onSuccess(n,t.state.variables,t.state.context,t)}).then(function(){return t.options.onSuccess==null?void 0:t.options.onSuccess(n,t.state.variables,t.state.context)}).then(function(){return t.options.onSettled==null?void 0:t.options.onSettled(n,null,t.state.variables,t.state.context)}).then(function(){return t.dispatch({type:"success",data:n}),n}).catch(function(a){return t.mutationCache.config.onError==null||t.mutationCache.config.onError(a,t.state.variables,t.state.context,t),De().error(a),Promise.resolve().then(function(){return t.options.onError==null?void 0:t.options.onError(a,t.state.variables,t.state.context)}).then(function(){return t.options.onSettled==null?void 0:t.options.onSettled(void 0,a,t.state.variables,t.state.context)}).then(function(){throw t.dispatch({type:"error",error:a}),a})})},e.executeMutation=function(){var t=this,n;return this.retryer=new Rt({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(t){var n=this;this.state=xr(this.state,t),O.batch(function(){n.observers.forEach(function(s){s.onMutationUpdate(t)}),n.mutationCache.notify(n)})},i}();function Mt(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function xr(i,e){switch(e.type){case"failed":return y({},i,{failureCount:i.failureCount+1});case"pause":return y({},i,{isPaused:!0});case"continue":return y({},i,{isPaused:!1});case"loading":return y({},i,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return y({},i,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return y({},i,{data:void 0,error:e.error,failureCount:i.failureCount+1,isPaused:!1,status:"error"});case"setState":return y({},i,e.state);default:return i}}var wr=function(i){ee(e,i);function e(t){var n;return n=i.call(this)||this,n.config=t||{},n.mutations=[],n.mutationId=0,n}var r=e.prototype;return r.build=function(n,s,o){var a=new br({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(s),state:o,defaultOptions:s.mutationKey?n.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(a),a},r.add=function(n){this.mutations.push(n),this.notify(n)},r.remove=function(n){this.mutations=this.mutations.filter(function(s){return s!==n}),n.cancel(),this.notify(n)},r.clear=function(){var n=this;O.batch(function(){n.mutations.forEach(function(s){n.remove(s)})})},r.getAll=function(){return this.mutations},r.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(s){return it(n,s)})},r.findAll=function(n){return this.mutations.filter(function(s){return it(n,s)})},r.notify=function(n){var s=this;O.batch(function(){s.listeners.forEach(function(o){o(n)})})},r.onFocus=function(){this.resumePausedMutations()},r.onOnline=function(){this.resumePausedMutations()},r.resumePausedMutations=function(){var n=this.mutations.filter(function(s){return s.state.isPaused});return O.batch(function(){return n.reduce(function(s,o){return s.then(function(){return o.continue().catch(R)})},Promise.resolve())})},e}(te);function Sr(){return{onFetch:function(e){e.fetchFn=function(){var r,t,n,s,o,a,c=(r=e.fetchOptions)==null||(t=r.meta)==null?void 0:t.refetchPage,l=(n=e.fetchOptions)==null||(s=n.meta)==null?void 0:s.fetchMore,d=l==null?void 0:l.pageParam,f=(l==null?void 0:l.direction)==="forward",p=(l==null?void 0:l.direction)==="backward",h=((o=e.state.data)==null?void 0:o.pages)||[],g=((a=e.state.data)==null?void 0:a.pageParams)||[],x=jt(),S=x==null?void 0:x.signal,C=g,w=!1,b=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},P=function(L,X,T,ie){return C=ie?[X].concat(C):[].concat(C,[X]),ie?[T].concat(L):[].concat(L,[T])},Q=function(L,X,T,ie){if(w)return Promise.reject("Cancelled");if(typeof T>"u"&&!X&&L.length)return Promise.resolve(L);var z={queryKey:e.queryKey,signal:S,pageParam:T,meta:e.meta},V=b(z),fe=Promise.resolve(V).then(function(Gt){return P(L,T,Gt,ie)});if(Te(V)){var Le=fe;Le.cancel=V.cancel}return fe},E;if(!h.length)E=Q([]);else if(f){var ne=typeof d<"u",Qe=ne?d:at(e.options,h);E=Q(h,ne,Qe)}else if(p){var de=typeof d<"u",k=de?d:Cr(e.options,h);E=Q(h,de,k,!0)}else(function(){C=[];var K=typeof e.options.getNextPageParam>"u",L=c&&h[0]?c(h[0],0,h):!0;E=L?Q([],K,g[0]):Promise.resolve(P([],g[0],h[0]));for(var X=function(z){E=E.then(function(V){var fe=c&&h[z]?c(h[z],z,h):!0;if(fe){var Le=K?g[z]:at(e.options,V);return Q(V,K,Le)}return Promise.resolve(P(V,g[z],h[z]))})},T=1;T<h.length;T++)X(T)})();var he=E.then(function(K){return{pages:K,pageParams:C}}),N=he;return N.cancel=function(){w=!0,x==null||x.abort(),Te(E)&&E.cancel()},he}}}}function at(i,e){return i.getNextPageParam==null?void 0:i.getNextPageParam(e[e.length-1],e)}function Cr(i,e){return i.getPreviousPageParam==null?void 0:i.getPreviousPageParam(e[0],e)}var Or=function(){function i(r){r===void 0&&(r={}),this.queryCache=r.queryCache||new yr,this.mutationCache=r.mutationCache||new wr,this.defaultOptions=r.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=i.prototype;return e.mount=function(){var t=this;this.unsubscribeFocus=oe.subscribe(function(){oe.isFocused()&&Oe.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())}),this.unsubscribeOnline=Oe.subscribe(function(){oe.isFocused()&&Oe.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())})},e.unmount=function(){var t,n;(t=this.unsubscribeFocus)==null||t.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},e.isFetching=function(t,n){var s=q(t,n),o=s[0];return o.fetching=!0,this.queryCache.findAll(o).length},e.isMutating=function(t){return this.mutationCache.findAll(y({},t,{fetching:!0})).length},e.getQueryData=function(t,n){var s;return(s=this.queryCache.find(t,n))==null?void 0:s.state.data},e.getQueriesData=function(t){return this.getQueryCache().findAll(t).map(function(n){var s=n.queryKey,o=n.state,a=o.data;return[s,a]})},e.setQueryData=function(t,n,s){var o=Ce(t),a=this.defaultQueryOptions(o);return this.queryCache.build(this,a).setData(n,s)},e.setQueriesData=function(t,n,s){var o=this;return O.batch(function(){return o.getQueryCache().findAll(t).map(function(a){var c=a.queryKey;return[c,o.setQueryData(c,n,s)]})})},e.getQueryState=function(t,n){var s;return(s=this.queryCache.find(t,n))==null?void 0:s.state},e.removeQueries=function(t,n){var s=q(t,n),o=s[0],a=this.queryCache;O.batch(function(){a.findAll(o).forEach(function(c){a.remove(c)})})},e.resetQueries=function(t,n,s){var o=this,a=q(t,n,s),c=a[0],l=a[1],d=this.queryCache,f=y({},c,{active:!0});return O.batch(function(){return d.findAll(c).forEach(function(p){p.reset()}),o.refetchQueries(f,l)})},e.cancelQueries=function(t,n,s){var o=this,a=q(t,n,s),c=a[0],l=a[1],d=l===void 0?{}:l;typeof d.revert>"u"&&(d.revert=!0);var f=O.batch(function(){return o.queryCache.findAll(c).map(function(p){return p.cancel(d)})});return Promise.all(f).then(R).catch(R)},e.invalidateQueries=function(t,n,s){var o,a,c,l=this,d=q(t,n,s),f=d[0],p=d[1],h=y({},f,{active:(o=(a=f.refetchActive)!=null?a:f.active)!=null?o:!0,inactive:(c=f.refetchInactive)!=null?c:!1});return O.batch(function(){return l.queryCache.findAll(f).forEach(function(g){g.invalidate()}),l.refetchQueries(h,p)})},e.refetchQueries=function(t,n,s){var o=this,a=q(t,n,s),c=a[0],l=a[1],d=O.batch(function(){return o.queryCache.findAll(c).map(function(p){return p.fetch(void 0,y({},l,{meta:{refetchPage:c==null?void 0:c.refetchPage}}))})}),f=Promise.all(d).then(R);return l!=null&&l.throwOnError||(f=f.catch(R)),f},e.fetchQuery=function(t,n,s){var o=Ce(t,n,s),a=this.defaultQueryOptions(o);typeof a.retry>"u"&&(a.retry=!1);var c=this.queryCache.build(this,a);return c.isStaleByTime(a.staleTime)?c.fetch(a):Promise.resolve(c.state.data)},e.prefetchQuery=function(t,n,s){return this.fetchQuery(t,n,s).then(R).catch(R)},e.fetchInfiniteQuery=function(t,n,s){var o=Ce(t,n,s);return o.behavior=Sr(),this.fetchQuery(o)},e.prefetchInfiniteQuery=function(t,n,s){return this.fetchInfiniteQuery(t,n,s).then(R).catch(R)},e.cancelMutations=function(){var t=this,n=O.batch(function(){return t.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(n).then(R).catch(R)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(t){return this.mutationCache.build(this,t).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(t){this.defaultOptions=t},e.setQueryDefaults=function(t,n){var s=this.queryDefaults.find(function(o){return W(t)===W(o.queryKey)});s?s.defaultOptions=n:this.queryDefaults.push({queryKey:t,defaultOptions:n})},e.getQueryDefaults=function(t){var n;return t?(n=this.queryDefaults.find(function(s){return ke(t,s.queryKey)}))==null?void 0:n.defaultOptions:void 0},e.setMutationDefaults=function(t,n){var s=this.mutationDefaults.find(function(o){return W(t)===W(o.mutationKey)});s?s.defaultOptions=n:this.mutationDefaults.push({mutationKey:t,defaultOptions:n})},e.getMutationDefaults=function(t){var n;return t?(n=this.mutationDefaults.find(function(s){return ke(t,s.mutationKey)}))==null?void 0:n.defaultOptions:void 0},e.defaultQueryOptions=function(t){if(t!=null&&t._defaulted)return t;var n=y({},this.defaultOptions.queries,this.getQueryDefaults(t==null?void 0:t.queryKey),t,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=Ze(n.queryKey,n)),n},e.defaultQueryObserverOptions=function(t){return this.defaultQueryOptions(t)},e.defaultMutationOptions=function(t){return t!=null&&t._defaulted?t:y({},this.defaultOptions.mutations,this.getMutationDefaults(t==null?void 0:t.mutationKey),t,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},i}(),Er=function(i){ee(e,i);function e(t,n){var s;return s=i.call(this)||this,s.client=t,s.options=n,s.trackedProps=[],s.selectError=null,s.bindMethods(),s.setOptions(n),s}var r=e.prototype;return r.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},r.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),ut(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},r.onUnsubscribe=function(){this.listeners.length||this.destroy()},r.shouldFetchOnReconnect=function(){return Ve(this.currentQuery,this.options,this.options.refetchOnReconnect)},r.shouldFetchOnWindowFocus=function(){return Ve(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},r.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},r.setOptions=function(n,s){var o=this.options,a=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=o.queryKey),this.updateQuery();var c=this.hasListeners();c&&ct(this.currentQuery,a,this.options,o)&&this.executeFetch(),this.updateResult(s),c&&(this.currentQuery!==a||this.options.enabled!==o.enabled||this.options.staleTime!==o.staleTime)&&this.updateStaleTimeout();var l=this.computeRefetchInterval();c&&(this.currentQuery!==a||this.options.enabled!==o.enabled||l!==this.currentRefetchInterval)&&this.updateRefetchInterval(l)},r.getOptimisticResult=function(n){var s=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,s);return this.createResult(o,s)},r.getCurrentResult=function(){return this.currentResult},r.trackResult=function(n,s){var o=this,a={},c=function(d){o.trackedProps.includes(d)||o.trackedProps.push(d)};return Object.keys(n).forEach(function(l){Object.defineProperty(a,l,{configurable:!1,enumerable:!0,get:function(){return c(l),n[l]}})}),(s.useErrorBoundary||s.suspense)&&c("error"),a},r.getNextResult=function(n){var s=this;return new Promise(function(o,a){var c=s.subscribe(function(l){l.isFetching||(c(),l.isError&&(n!=null&&n.throwOnError)?a(l.error):o(l))})})},r.getCurrentQuery=function(){return this.currentQuery},r.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},r.refetch=function(n){return this.fetch(y({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},r.fetchOptimistic=function(n){var s=this,o=this.client.defaultQueryObserverOptions(n),a=this.client.getQueryCache().build(this.client,o);return a.fetch().then(function(){return s.createResult(a,o)})},r.fetch=function(n){var s=this;return this.executeFetch(n).then(function(){return s.updateResult(),s.currentResult})},r.executeFetch=function(n){this.updateQuery();var s=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(s=s.catch(R)),s},r.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(Me||this.currentResult.isStale||!Ne(this.options.staleTime))){var s=Ot(this.currentResult.dataUpdatedAt,this.options.staleTime),o=s+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},o)}},r.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},r.updateRefetchInterval=function(n){var s=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(Me||this.options.enabled===!1||!Ne(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(s.options.refetchIntervalInBackground||oe.isFocused())&&s.executeFetch()},this.currentRefetchInterval))},r.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},r.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},r.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},r.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},r.createResult=function(n,s){var o=this.currentQuery,a=this.options,c=this.currentResult,l=this.currentResultState,d=this.currentResultOptions,f=n!==o,p=f?n.state:this.currentQueryInitialState,h=f?this.currentResult:this.previousQueryResult,g=n.state,x=g.dataUpdatedAt,S=g.error,C=g.errorUpdatedAt,w=g.isFetching,b=g.status,P=!1,Q=!1,E;if(s.optimisticResults){var ne=this.hasListeners(),Qe=!ne&&ut(n,s),de=ne&&ct(n,o,s,a);(Qe||de)&&(w=!0,x||(b="loading"))}if(s.keepPreviousData&&!g.dataUpdateCount&&(h!=null&&h.isSuccess)&&b!=="error")E=h.data,x=h.dataUpdatedAt,b=h.status,P=!0;else if(s.select&&typeof g.data<"u")if(c&&g.data===(l==null?void 0:l.data)&&s.select===this.selectFn)E=this.selectResult;else try{this.selectFn=s.select,E=s.select(g.data),s.structuralSharing!==!1&&(E=Fe(c==null?void 0:c.data,E)),this.selectResult=E,this.selectError=null}catch(N){De().error(N),this.selectError=N}else E=g.data;if(typeof s.placeholderData<"u"&&typeof E>"u"&&(b==="loading"||b==="idle")){var k;if(c!=null&&c.isPlaceholderData&&s.placeholderData===(d==null?void 0:d.placeholderData))k=c.data;else if(k=typeof s.placeholderData=="function"?s.placeholderData():s.placeholderData,s.select&&typeof k<"u")try{k=s.select(k),s.structuralSharing!==!1&&(k=Fe(c==null?void 0:c.data,k)),this.selectError=null}catch(N){De().error(N),this.selectError=N}typeof k<"u"&&(b="success",E=k,Q=!0)}this.selectError&&(S=this.selectError,E=this.selectResult,C=Date.now(),b="error");var he={status:b,isLoading:b==="loading",isSuccess:b==="success",isError:b==="error",isIdle:b==="idle",data:E,dataUpdatedAt:x,error:S,errorUpdatedAt:C,failureCount:g.fetchFailureCount,errorUpdateCount:g.errorUpdateCount,isFetched:g.dataUpdateCount>0||g.errorUpdateCount>0,isFetchedAfterMount:g.dataUpdateCount>p.dataUpdateCount||g.errorUpdateCount>p.errorUpdateCount,isFetching:w,isRefetching:w&&b!=="loading",isLoadingError:b==="error"&&g.dataUpdatedAt===0,isPlaceholderData:Q,isPreviousData:P,isRefetchError:b==="error"&&g.dataUpdatedAt!==0,isStale:et(n,s),refetch:this.refetch,remove:this.remove};return he},r.shouldNotifyListeners=function(n,s){if(!s)return!0;var o=this.options,a=o.notifyOnChangeProps,c=o.notifyOnChangePropsExclusions;if(!a&&!c||a==="tracked"&&!this.trackedProps.length)return!0;var l=a==="tracked"?this.trackedProps:a;return Object.keys(n).some(function(d){var f=d,p=n[f]!==s[f],h=l==null?void 0:l.some(function(x){return x===d}),g=c==null?void 0:c.some(function(x){return x===d});return p&&!g&&(!l||h)})},r.updateResult=function(n){var s=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!lr(this.currentResult,s)){var o={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,s)&&(o.listeners=!0),this.notify(y({},o,n))}},r.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var s=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(s==null||s.removeObserver(this),n.addObserver(this))}},r.onQueryUpdate=function(n){var s={};n.type==="success"?s.onSuccess=!0:n.type==="error"&&!Ee(n.error)&&(s.onError=!0),this.updateResult(s),this.hasListeners()&&this.updateTimers()},r.notify=function(n){var s=this;O.batch(function(){n.onSuccess?(s.options.onSuccess==null||s.options.onSuccess(s.currentResult.data),s.options.onSettled==null||s.options.onSettled(s.currentResult.data,null)):n.onError&&(s.options.onError==null||s.options.onError(s.currentResult.error),s.options.onSettled==null||s.options.onSettled(void 0,s.currentResult.error)),n.listeners&&s.listeners.forEach(function(o){o(s.currentResult)}),n.cache&&s.client.getQueryCache().notify({query:s.currentQuery,type:"observerResultsUpdated"})})},e}(te);function jr(i,e){return e.enabled!==!1&&!i.state.dataUpdatedAt&&!(i.state.status==="error"&&e.retryOnMount===!1)}function ut(i,e){return jr(i,e)||i.state.dataUpdatedAt>0&&Ve(i,e,e.refetchOnMount)}function Ve(i,e,r){if(e.enabled!==!1){var t=typeof r=="function"?r(i):r;return t==="always"||t!==!1&&et(i,e)}return!1}function ct(i,e,r,t){return r.enabled!==!1&&(i!==e||t.enabled===!1)&&(!r.suspense||i.state.status!=="error")&&et(i,r)}function et(i,e){return i.isStaleByTime(e.staleTime)}var Pr=function(i){ee(e,i);function e(t,n){var s;return s=i.call(this)||this,s.client=t,s.setOptions(n),s.bindMethods(),s.updateResult(),s}var r=e.prototype;return r.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},r.setOptions=function(n){this.options=this.client.defaultMutationOptions(n)},r.onUnsubscribe=function(){if(!this.listeners.length){var n;(n=this.currentMutation)==null||n.removeObserver(this)}},r.onMutationUpdate=function(n){this.updateResult();var s={listeners:!0};n.type==="success"?s.onSuccess=!0:n.type==="error"&&(s.onError=!0),this.notify(s)},r.getCurrentResult=function(){return this.currentResult},r.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},r.mutate=function(n,s){return this.mutateOptions=s,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,y({},this.options,{variables:typeof n<"u"?n:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},r.updateResult=function(){var n=this.currentMutation?this.currentMutation.state:Mt(),s=y({},n,{isLoading:n.status==="loading",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=s},r.notify=function(n){var s=this;O.batch(function(){s.mutateOptions&&(n.onSuccess?(s.mutateOptions.onSuccess==null||s.mutateOptions.onSuccess(s.currentResult.data,s.currentResult.variables,s.currentResult.context),s.mutateOptions.onSettled==null||s.mutateOptions.onSettled(s.currentResult.data,null,s.currentResult.variables,s.currentResult.context)):n.onError&&(s.mutateOptions.onError==null||s.mutateOptions.onError(s.currentResult.error,s.currentResult.variables,s.currentResult.context),s.mutateOptions.onSettled==null||s.mutateOptions.onSettled(void 0,s.currentResult.error,s.currentResult.variables,s.currentResult.context))),n.listeners&&s.listeners.forEach(function(o){o(s.currentResult)})})},e}(te),Rr=Ht.unstable_batchedUpdates;O.setBatchNotifyFunction(Rr);var _r=console;vr(_r);var lt=j.createContext(void 0),At=j.createContext(!1);function kt(i){return i&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=lt),window.ReactQueryClientContext):lt}var tt=function(){var e=j.useContext(kt(j.useContext(At)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},Mr=function(e){var r=e.client,t=e.contextSharing,n=t===void 0?!1:t,s=e.children;j.useEffect(function(){return r.mount(),function(){r.unmount()}},[r]);var o=kt(n);return j.createElement(At.Provider,{value:n},j.createElement(o.Provider,{value:r},s))};function Ar(){var i=!1;return{clearReset:function(){i=!1},reset:function(){i=!0},isReset:function(){return i}}}var kr=j.createContext(Ar()),Fr=function(){return j.useContext(kr)};function Ft(i,e,r){return typeof e=="function"?e.apply(void 0,r):typeof e=="boolean"?e:!!i}function je(i,e,r){var t=j.useRef(!1),n=j.useState(0),s=n[1],o=ar(i,e,r),a=tt(),c=j.useRef();c.current?c.current.setOptions(o):c.current=new Pr(a,o);var l=c.current.getCurrentResult();j.useEffect(function(){t.current=!0;var f=c.current.subscribe(O.batchCalls(function(){t.current&&s(function(p){return p+1})}));return function(){t.current=!1,f()}},[]);var d=j.useCallback(function(f,p){c.current.mutate(f,p).catch(R)},[]);if(l.error&&Ft(void 0,c.current.options.useErrorBoundary,[l.error]))throw l.error;return y({},l,{mutate:d,mutateAsync:l.mutate})}function Tr(i,e){var r=j.useRef(!1),t=j.useState(0),n=t[1],s=tt(),o=Fr(),a=s.defaultQueryObserverOptions(i);a.optimisticResults=!0,a.onError&&(a.onError=O.batchCalls(a.onError)),a.onSuccess&&(a.onSuccess=O.batchCalls(a.onSuccess)),a.onSettled&&(a.onSettled=O.batchCalls(a.onSettled)),a.suspense&&(typeof a.staleTime!="number"&&(a.staleTime=1e3),a.cacheTime===0&&(a.cacheTime=1)),(a.suspense||a.useErrorBoundary)&&(o.isReset()||(a.retryOnMount=!1));var c=j.useState(function(){return new e(s,a)}),l=c[0],d=l.getOptimisticResult(a);if(j.useEffect(function(){r.current=!0,o.clearReset();var f=l.subscribe(O.batchCalls(function(){r.current&&n(function(p){return p+1})}));return l.updateResult(),function(){r.current=!1,f()}},[o,l]),j.useEffect(function(){l.setOptions(a,{listeners:!1})},[a,l]),a.suspense&&d.isLoading)throw l.fetchOptimistic(a).then(function(f){var p=f.data;a.onSuccess==null||a.onSuccess(p),a.onSettled==null||a.onSettled(p,null)}).catch(function(f){o.clearReset(),a.onError==null||a.onError(f),a.onSettled==null||a.onSettled(void 0,f)});if(d.isError&&!o.isReset()&&!d.isFetching&&Ft(a.suspense,a.useErrorBoundary,[d.error,l.getCurrentQuery()]))throw d.error;return a.notifyOnChangeProps==="tracked"&&(d=l.trackResult(d,a)),d}function Tt(i,e,r){var t=Ce(i,e,r);return Tr(t,Er)}let Dr={data:""},Ur=i=>typeof window=="object"?((i?i.querySelector("#_goober"):window._goober)||Object.assign((i||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:i||Dr,Ir=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,$r=/\/\*[^]*?\*\/|  +/g,dt=/\n+/g,B=(i,e)=>{let r="",t="",n="";for(let s in i){let o=i[s];s[0]=="@"?s[1]=="i"?r=s+" "+o+";":t+=s[1]=="f"?B(o,s):s+"{"+B(o,s[1]=="k"?"":e)+"}":typeof o=="object"?t+=B(o,e?e.replace(/([^,])+/g,a=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,a):a?a+" "+c:c)):s):o!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=B.p?B.p(s,o):s+":"+o+";")}return r+(e&&n?e+"{"+n+"}":n)+t},I={},Dt=i=>{if(typeof i=="object"){let e="";for(let r in i)e+=r+Dt(i[r]);return e}return i},Qr=(i,e,r,t,n)=>{let s=Dt(i),o=I[s]||(I[s]=(c=>{let l=0,d=11;for(;l<c.length;)d=101*d+c.charCodeAt(l++)>>>0;return"go"+d})(s));if(!I[o]){let c=s!==i?i:(l=>{let d,f,p=[{}];for(;d=Ir.exec(l.replace($r,""));)d[4]?p.shift():d[3]?(f=d[3].replace(dt," ").trim(),p.unshift(p[0][f]=p[0][f]||{})):p[0][d[1]]=d[2].replace(dt," ").trim();return p[0]})(i);I[o]=B(n?{["@keyframes "+o]:c}:c,r?"":"."+o)}let a=r&&I.g?I.g:null;return r&&(I.g=I[o]),((c,l,d,f)=>{f?l.data=l.data.replace(f,c):l.data.indexOf(c)===-1&&(l.data=d?c+l.data:l.data+c)})(I[o],e,t,a),o},Lr=(i,e,r)=>i.reduce((t,n,s)=>{let o=e[s];if(o&&o.call){let a=o(r),c=a&&a.props&&a.props.className||/^go/.test(a)&&a;o=c?"."+c:a&&typeof a=="object"?a.props?"":B(a,""):a===!1?"":a}return t+n+(o??"")},"");function $e(i){let e=this||{},r=i.call?i(e.p):i;return Qr(r.unshift?r.raw?Lr(r,[].slice.call(arguments,1),e.p):r.reduce((t,n)=>Object.assign(t,n&&n.call?n(e.p):n),{}):r,Ur(e.target),e.g,e.o,e.k)}let Ut,He,We;$e.bind({g:1});let $=$e.bind({k:1});function zr(i,e,r,t){B.p=e,Ut=i,He=r,We=t}function G(i,e){let r=this||{};return function(){let t=arguments;function n(s,o){let a=Object.assign({},s),c=a.className||n.className;r.p=Object.assign({theme:He&&He()},a),r.o=/ *go\d+/.test(c),a.className=$e.apply(r,t)+(c?" "+c:"");let l=i;return i[0]&&(l=a.as||i,delete a.as),We&&l[0]&&We(a),Ut(l,a)}return n}}var qr=i=>typeof i=="function",Ue=(i,e)=>qr(i)?i(e):i,Br=(()=>{let i=0;return()=>(++i).toString()})(),It=(()=>{let i;return()=>{if(i===void 0&&typeof window<"u"){let e=matchMedia("(prefers-reduced-motion: reduce)");i=!e||e.matches}return i}})(),Gr=20,$t=(i,e)=>{switch(e.type){case 0:return{...i,toasts:[e.toast,...i.toasts].slice(0,Gr)};case 1:return{...i,toasts:i.toasts.map(s=>s.id===e.toast.id?{...s,...e.toast}:s)};case 2:let{toast:r}=e;return $t(i,{type:i.toasts.find(s=>s.id===r.id)?1:0,toast:r});case 3:let{toastId:t}=e;return{...i,toasts:i.toasts.map(s=>s.id===t||t===void 0?{...s,dismissed:!0,visible:!1}:s)};case 4:return e.toastId===void 0?{...i,toasts:[]}:{...i,toasts:i.toasts.filter(s=>s.id!==e.toastId)};case 5:return{...i,pausedAt:e.time};case 6:let n=e.time-(i.pausedAt||0);return{...i,pausedAt:void 0,toasts:i.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+n}))}}},Pe=[],J={toasts:[],pausedAt:void 0},Y=i=>{J=$t(J,i),Pe.forEach(e=>{e(J)})},Nr={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Kr=(i={})=>{let[e,r]=m.useState(J),t=m.useRef(J);m.useEffect(()=>(t.current!==J&&r(J),Pe.push(r),()=>{let s=Pe.indexOf(r);s>-1&&Pe.splice(s,1)}),[]);let n=e.toasts.map(s=>{var o,a,c;return{...i,...i[s.type],...s,removeDelay:s.removeDelay||((o=i[s.type])==null?void 0:o.removeDelay)||(i==null?void 0:i.removeDelay),duration:s.duration||((a=i[s.type])==null?void 0:a.duration)||(i==null?void 0:i.duration)||Nr[s.type],style:{...i.style,...(c=i[s.type])==null?void 0:c.style,...s.style}}});return{...e,toasts:n}},Vr=(i,e="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:e,ariaProps:{role:"status","aria-live":"polite"},message:i,pauseDuration:0,...r,id:(r==null?void 0:r.id)||Br()}),le=i=>(e,r)=>{let t=Vr(e,i,r);return Y({type:2,toast:t}),t.id},M=(i,e)=>le("blank")(i,e);M.error=le("error");M.success=le("success");M.loading=le("loading");M.custom=le("custom");M.dismiss=i=>{Y({type:3,toastId:i})};M.remove=i=>Y({type:4,toastId:i});M.promise=(i,e,r)=>{let t=M.loading(e.loading,{...r,...r==null?void 0:r.loading});return typeof i=="function"&&(i=i()),i.then(n=>{let s=e.success?Ue(e.success,n):void 0;return s?M.success(s,{id:t,...r,...r==null?void 0:r.success}):M.dismiss(t),n}).catch(n=>{let s=e.error?Ue(e.error,n):void 0;s?M.error(s,{id:t,...r,...r==null?void 0:r.error}):M.dismiss(t)}),i};var Hr=(i,e)=>{Y({type:1,toast:{id:i,height:e}})},Wr=()=>{Y({type:5,time:Date.now()})},ae=new Map,Jr=1e3,Yr=(i,e=Jr)=>{if(ae.has(i))return;let r=setTimeout(()=>{ae.delete(i),Y({type:4,toastId:i})},e);ae.set(i,r)},Xr=i=>{let{toasts:e,pausedAt:r}=Kr(i);m.useEffect(()=>{if(r)return;let s=Date.now(),o=e.map(a=>{if(a.duration===1/0)return;let c=(a.duration||0)+a.pauseDuration-(s-a.createdAt);if(c<0){a.visible&&M.dismiss(a.id);return}return setTimeout(()=>M.dismiss(a.id),c)});return()=>{o.forEach(a=>a&&clearTimeout(a))}},[e,r]);let t=m.useCallback(()=>{r&&Y({type:6,time:Date.now()})},[r]),n=m.useCallback((s,o)=>{let{reverseOrder:a=!1,gutter:c=8,defaultPosition:l}=o||{},d=e.filter(h=>(h.position||l)===(s.position||l)&&h.height),f=d.findIndex(h=>h.id===s.id),p=d.filter((h,g)=>g<f&&h.visible).length;return d.filter(h=>h.visible).slice(...a?[p+1]:[0,p]).reduce((h,g)=>h+(g.height||0)+c,0)},[e]);return m.useEffect(()=>{e.forEach(s=>{if(s.dismissed)Yr(s.id,s.removeDelay);else{let o=ae.get(s.id);o&&(clearTimeout(o),ae.delete(s.id))}})},[e]),{toasts:e,handlers:{updateHeight:Hr,startPause:Wr,endPause:t,calculateOffset:n}}},Zr=$`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,en=$`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,tn=$`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,rn=G("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${i=>i.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Zr} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${en} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${i=>i.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${tn} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,nn=$`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,sn=G("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${i=>i.secondary||"#e0e0e0"};
  border-right-color: ${i=>i.primary||"#616161"};
  animation: ${nn} 1s linear infinite;
`,on=$`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,an=$`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,un=G("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${i=>i.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${on} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${an} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${i=>i.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,cn=G("div")`
  position: absolute;
`,ln=G("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,dn=$`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,hn=G("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${dn} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,fn=({toast:i})=>{let{icon:e,type:r,iconTheme:t}=i;return e!==void 0?typeof e=="string"?m.createElement(hn,null,e):e:r==="blank"?null:m.createElement(ln,null,m.createElement(sn,{...t}),r!=="loading"&&m.createElement(cn,null,r==="error"?m.createElement(rn,{...t}):m.createElement(un,{...t})))},pn=i=>`
0% {transform: translate3d(0,${i*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,mn=i=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${i*-150}%,-1px) scale(.6); opacity:0;}
`,vn="0%{opacity:0;} 100%{opacity:1;}",gn="0%{opacity:1;} 100%{opacity:0;}",yn=G("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,bn=G("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,xn=(i,e)=>{let r=i.includes("top")?1:-1,[t,n]=It()?[vn,gn]:[pn(r),mn(r)];return{animation:e?`${$(t)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${$(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},wn=m.memo(({toast:i,position:e,style:r,children:t})=>{let n=i.height?xn(i.position||e||"top-center",i.visible):{opacity:0},s=m.createElement(fn,{toast:i}),o=m.createElement(bn,{...i.ariaProps},Ue(i.message,i));return m.createElement(yn,{className:i.className,style:{...n,...r,...i.style}},typeof t=="function"?t({icon:s,message:o}):m.createElement(m.Fragment,null,s,o))});zr(m.createElement);var Sn=({id:i,className:e,style:r,onHeightUpdate:t,children:n})=>{let s=m.useCallback(o=>{if(o){let a=()=>{let c=o.getBoundingClientRect().height;t(i,c)};a(),new MutationObserver(a).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[i,t]);return m.createElement("div",{ref:s,className:e,style:r},n)},Cn=(i,e)=>{let r=i.includes("top"),t=r?{top:0}:{bottom:0},n=i.includes("center")?{justifyContent:"center"}:i.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:It()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(r?1:-1)}px)`,...t,...n}},On=$e`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,pe=16,En=({reverseOrder:i,position:e="top-center",toastOptions:r,gutter:t,children:n,containerStyle:s,containerClassName:o})=>{let{toasts:a,handlers:c}=Xr(r);return m.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:pe,left:pe,right:pe,bottom:pe,pointerEvents:"none",...s},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},a.map(l=>{let d=l.position||e,f=c.calculateOffset(l,{reverseOrder:i,gutter:t,defaultPosition:e}),p=Cn(d,f);return m.createElement(Sn,{id:l.id,key:l.id,onHeightUpdate:c.updateHeight,className:l.visible?On:"",style:p},l.type==="custom"?Ue(l.message,l):n?n(l):m.createElement(wn,{toast:l,position:d}))}))},_=M;const me={id:"1",username:"测试用户",email:"<EMAIL>",level:5,experience:1250,coins:5e3,gems:100,isActive:!0,isPremium:!1,createdAt:new Date().toISOString(),lastLoginAt:new Date().toISOString()},ve={getCurrentUser:async()=>{const i=localStorage.getItem("token");if(!i)throw new Error("No token found");try{const e=await fetch("/api/auth/me",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!e.ok)throw new Error("Failed to get user info");return e.json()}catch{return console.warn("Backend not available, using mock data"),me}},login:async i=>{try{const e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Login failed")}return e.json()}catch{return console.warn("Backend not available, using mock data"),{user:me,token:"mock-token-"+Date.now()}}},register:async i=>{try{const e=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Registration failed")}return e.json()}catch{return console.warn("Backend not available, using mock data"),{user:{...me,username:i.username,email:i.email},token:"mock-token-"+Date.now()}}},updateProfile:async i=>{const e=localStorage.getItem("token");if(!e)throw new Error("No token found");try{const r=await fetch("/api/users/profile",{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!r.ok){const t=await r.json();throw new Error(t.message||"Update failed")}return r.json()}catch{return console.warn("Backend not available, using mock data"),{...me,...i}}}},Qt=m.createContext(void 0),jn=({children:i})=>{const[e,r]=m.useState(null),t=tt(),{isLoading:n}=Tt("currentUser",ve.getCurrentUser,{enabled:!!localStorage.getItem("token"),retry:!1,onSuccess:h=>{r(h)},onError:()=>{localStorage.removeItem("token"),r(null)}}),s=je(ve.login,{onSuccess:h=>{localStorage.setItem("token",h.token),r(h.user),t.setQueryData("currentUser",h.user),_.success("登录成功！")},onError:h=>{_.error(h.message||"登录失败")}}),o=je(ve.register,{onSuccess:h=>{localStorage.setItem("token",h.token),r(h.user),t.setQueryData("currentUser",h.user),_.success("注册成功！")},onError:h=>{_.error(h.message||"注册失败")}}),a=je(ve.updateProfile,{onSuccess:h=>{r(h),t.setQueryData("currentUser",h),_.success("个人信息更新成功！")},onError:h=>{_.error(h.message||"更新失败")}}),c=async h=>{await s.mutateAsync(h)},l=async h=>{await o.mutateAsync(h)},d=()=>{localStorage.removeItem("token"),r(null),t.clear(),_.success("已退出登录")},f=async h=>{await a.mutateAsync(h)};m.useEffect(()=>{localStorage.getItem("token")||r(null)},[]);const p={user:e,loading:n,login:c,register:l,logout:d,updateProfile:f};return u.jsx(Qt.Provider,{value:p,children:i})},re=()=>{const i=m.useContext(Qt);if(i===void 0)throw new Error("useAuth must be used within an AuthProvider");return i},ht=v.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
`,ft=v.h1`
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`,Pn=v.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 600px;
`,Rn=v.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
`,_n=v.div`
  color: white;
  margin-bottom: 1rem;
  
  h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    opacity: 0.8;
    margin: 0.25rem 0;
  }
`,pt=v.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
`,Je=v(ue)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
`,ze=v(Je)`
  background: linear-gradient(135deg, #4834d4, #686de0);
`,Mn=v.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
  max-width: 600px;
`,ge=v.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  h3 {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
  }
`,An=()=>{const{user:i}=re();return i?u.jsxs(ht,{children:[u.jsx(ft,{children:"星光对对碰"}),u.jsx(Rn,{children:u.jsxs(_n,{children:[u.jsxs("h2",{children:["欢迎回来，",i.username,"！"]}),u.jsxs("p",{children:["等级：",i.level]}),u.jsxs("p",{children:["经验值：",i.experience]}),u.jsxs("p",{children:["金币：",i.coins]}),u.jsxs("p",{children:["宝石：",i.gems]})]})}),u.jsxs(pt,{children:[u.jsx(Je,{to:"/levels",children:"选择关卡"}),u.jsx(ze,{to:"/game",children:"快速开始"}),u.jsx(ze,{to:"/leaderboard",children:"排行榜"}),u.jsx(ze,{to:"/profile",children:"个人资料"})]}),u.jsxs(Mn,{children:[u.jsxs(ge,{children:[u.jsx("h3",{children:i.level}),u.jsx("p",{children:"当前等级"})]}),u.jsxs(ge,{children:[u.jsx("h3",{children:i.experience}),u.jsx("p",{children:"总经验值"})]}),u.jsxs(ge,{children:[u.jsx("h3",{children:i.coins}),u.jsx("p",{children:"金币"})]}),u.jsxs(ge,{children:[u.jsx("h3",{children:i.gems}),u.jsx("p",{children:"宝石"})]})]})]}):u.jsxs(ht,{children:[u.jsx(ft,{children:"星光对对碰"}),u.jsx(Pn,{children:"欢迎来到最有趣的三消游戏！匹配星星，获得高分，挑战无限关卡！"}),u.jsx(pt,{children:u.jsx(Je,{to:"/login",children:"开始游戏"})})]})},kn=v.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`,Fn=v.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`,Tn=v.h1`
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: bold;
`,Dn=v.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`,mt=v.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`,vt=v.label`
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
`,gt=v.input`
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  
  &:focus {
    outline: none;
    border-color: #4834d4;
    box-shadow: 0 0 0 3px rgba(72, 52, 212, 0.3);
  }
`,Un=v.button`
  padding: 1rem;
  background: linear-gradient(135deg, #4834d4, #686de0);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`,In=v.div`
  text-align: center;
  margin-top: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  
  a {
    color: #4834d4;
    text-decoration: none;
    font-weight: 600;
    
    &:hover {
      text-decoration: underline;
    }
  }
`,$n=v.div`
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: #fca5a5;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
`,Qn=()=>{const[i,e]=m.useState(""),[r,t]=m.useState(""),[n,s]=m.useState(!1),[o,a]=m.useState(""),{login:c}=re(),l=async d=>{d.preventDefault(),a(""),s(!0);try{await c({email:i,password:r})}catch(f){a(f instanceof Error?f.message:"登录失败")}finally{s(!1)}};return u.jsx(kn,{children:u.jsxs(Fn,{children:[u.jsx(Tn,{children:"登录"}),o&&u.jsx($n,{children:o}),u.jsxs(Dn,{onSubmit:l,children:[u.jsxs(mt,{children:[u.jsx(vt,{htmlFor:"email",children:"邮箱"}),u.jsx(gt,{id:"email",type:"email",placeholder:"请输入邮箱",value:i,onChange:d=>e(d.target.value),required:!0})]}),u.jsxs(mt,{children:[u.jsx(vt,{htmlFor:"password",children:"密码"}),u.jsx(gt,{id:"password",type:"password",placeholder:"请输入密码",value:r,onChange:d=>t(d.target.value),required:!0})]}),u.jsx(Un,{type:"submit",disabled:n,children:n?"登录中...":"登录"})]}),u.jsxs(In,{children:["还没有账号？ ",u.jsx(ue,{to:"/register",children:"立即注册"})]})]})})},Ln=v.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`,zn=v.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`,qn=v.h1`
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: bold;
`,Bn=v.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`,ye=v.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`,be=v.label`
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
`,xe=v.input`
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  
  &:focus {
    outline: none;
    border-color: #4834d4;
    box-shadow: 0 0 0 3px rgba(72, 52, 212, 0.3);
  }
`,Gn=v.button`
  padding: 1rem;
  background: linear-gradient(135deg, #4834d4, #686de0);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`,Nn=v.div`
  text-align: center;
  margin-top: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  
  a {
    color: #4834d4;
    text-decoration: none;
    font-weight: 600;
    
    &:hover {
      text-decoration: underline;
    }
  }
`,Kn=v.div`
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: #fca5a5;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
`,Vn=()=>{const[i,e]=m.useState(""),[r,t]=m.useState(""),[n,s]=m.useState(""),[o,a]=m.useState(""),[c,l]=m.useState(!1),[d,f]=m.useState(""),{register:p}=re(),h=async g=>{if(g.preventDefault(),f(""),n!==o){f("两次输入的密码不一致");return}if(n.length<6){f("密码长度至少为6位");return}l(!0);try{await p({username:i,email:r,password:n})}catch(x){f(x instanceof Error?x.message:"注册失败")}finally{l(!1)}};return u.jsx(Ln,{children:u.jsxs(zn,{children:[u.jsx(qn,{children:"注册"}),d&&u.jsx(Kn,{children:d}),u.jsxs(Bn,{onSubmit:h,children:[u.jsxs(ye,{children:[u.jsx(be,{htmlFor:"username",children:"用户名"}),u.jsx(xe,{id:"username",type:"text",placeholder:"请输入用户名",value:i,onChange:g=>e(g.target.value),required:!0})]}),u.jsxs(ye,{children:[u.jsx(be,{htmlFor:"email",children:"邮箱"}),u.jsx(xe,{id:"email",type:"email",placeholder:"请输入邮箱",value:r,onChange:g=>t(g.target.value),required:!0})]}),u.jsxs(ye,{children:[u.jsx(be,{htmlFor:"password",children:"密码"}),u.jsx(xe,{id:"password",type:"password",placeholder:"请输入密码（至少6位）",value:n,onChange:g=>s(g.target.value),required:!0})]}),u.jsxs(ye,{children:[u.jsx(be,{htmlFor:"confirmPassword",children:"确认密码"}),u.jsx(xe,{id:"confirmPassword",type:"password",placeholder:"请再次输入密码",value:o,onChange:g=>a(g.target.value),required:!0})]}),u.jsx(Gn,{type:"submit",disabled:c,children:c?"注册中...":"注册"})]}),u.jsxs(Nn,{children:["已有账号？ ",u.jsx(ue,{to:"/login",children:"立即登录"})]})]})})},Hn="modulepreload",Wn=function(i){return"/"+i},yt={},A=function(e,r,t){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));n=Promise.allSettled(r.map(c=>{if(c=Wn(c),c in yt)return;yt[c]=!0;const l=c.endsWith(".css"),d=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const f=document.createElement("link");if(f.rel=l?"stylesheet":Hn,l||(f.as="script"),f.crossOrigin="",f.href=c,a&&f.setAttribute("nonce",a),document.head.appendChild(f),l)return new Promise((p,h)=>{f.addEventListener("load",p),f.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${c}`)))})}))}function s(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return n.then(o=>{for(const a of o||[])a.status==="rejected"&&s(a.reason);return e().catch(s)})},Jn=m.lazy(()=>A(()=>import("./GamePage-h55HzyeI.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]))),Yn=m.lazy(()=>A(()=>import("./LevelsPage-D220u2A8.js"),__vite__mapDeps([13,2,1,3]))),Xn=m.lazy(()=>A(()=>import("./LeaderboardPage-x048hut8.js"),__vite__mapDeps([14,1,2,15,11,10,3]))),Zn=m.lazy(()=>A(()=>import("./AchievementsPage-BeJEZHzx.js"),__vite__mapDeps([16,1,2,17,11,10,3]))),ei=m.lazy(()=>A(()=>import("./ProfilePage-CsLxkWtu.js"),__vite__mapDeps([18,2,1,3])));m.lazy(()=>A(()=>import("./GameBoard-DvXOxAln.js"),__vite__mapDeps([4,1,2,5,6,3])));m.lazy(()=>A(()=>import("./PowerUpPanel-Cy3BrM2g.js"),__vite__mapDeps([8,2,1,9,10,11,3])));m.lazy(()=>A(()=>import("./ShareModal-GIcMGaE5.js"),__vite__mapDeps([19,1,2,15,10,3])));m.lazy(()=>A(()=>import("./GameOverModal-4jLpsjyZ.js"),__vite__mapDeps([7,1,2,3])));const ti=()=>A(()=>import("./animationManager-DTsUvCq5.js"),[]).then(i=>i.AnimationManager),ri=()=>A(()=>import("./specialGemManager-S1dnzDs3.js"),__vite__mapDeps([20,6])).then(i=>i.SpecialGemManager),ni=()=>A(()=>import("./powerUpManager-DAya6dWG.js"),__vite__mapDeps([12,9])).then(i=>i.PowerUpManager),ii=()=>A(()=>import("./achievementManager-Bt3BvN3k.js"),__vite__mapDeps([21,17])).then(i=>i.AchievementManager),Lt=()=>{ti(),ri(),ni()},bt=()=>{Lt(),ii()};class Re{static async preloadComponent(e,r){if(!this.preloadedComponents.has(e))try{await r(),this.preloadedComponents.add(e),console.log(`预加载组件成功: ${e}`)}catch(t){console.error(`预加载组件失败: ${e}`,t)}}static async preloadOnIdle(){if("requestIdleCallback"in window)return new Promise(e=>{requestIdleCallback(()=>{bt(),e()})});setTimeout(()=>{bt()},100)}static async preloadOnUserInteraction(){const e=["mousedown","touchstart","keydown"],r=()=>{Lt(),e.forEach(t=>{document.removeEventListener(t,r)})};e.forEach(t=>{document.addEventListener(t,r,{once:!0,passive:!0})})}static getPreloadedComponents(){return Array.from(this.preloadedComponents)}static clearPreloadedComponents(){this.preloadedComponents.clear()}}F(Re,"preloadedComponents",new Set);class _e{static set(e,r,t=0){for(;this.cacheSize+t>this.MAX_CACHE_SIZE&&this.cache.size>0;){const n=this.cache.keys().next().value;this.delete(n)}this.cache.set(e,r),this.cacheSize+=t}static get(e){return this.cache.get(e)}static delete(e){this.cache.has(e)&&(this.cache.delete(e),this.cacheSize=Math.max(0,this.cacheSize-1024))}static clear(){this.cache.clear(),this.cacheSize=0}static getCacheInfo(){return{size:this.cache.size,memoryUsage:this.cacheSize,maxSize:this.MAX_CACHE_SIZE}}static cleanup(){const e=Date.now(),r=5*60*1e3;for(const[t,n]of this.cache.entries())n.timestamp&&e-n.timestamp>r&&this.delete(t)}}F(_e,"MAX_CACHE_SIZE",50*1024*1024),F(_e,"cache",new Map),F(_e,"cacheSize",0);setInterval(()=>{_e.cleanup()},6e4);v.div`
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 10000;
  transform: translateX(${i=>i.$visible?"0":"100%"});
  transition: transform 0.3s ease-in-out;
  overflow: hidden;
`;v.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
  }
  
  button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
  }
`;v.div`
  padding: 12px;
  max-height: calc(80vh - 50px);
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;v.div`
  margin-bottom: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #ffd700;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 4px;
  }
`;v.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  .label {
    color: #ccc;
  }
  
  .value {
    color: white;
    font-weight: bold;
  }
  
  &.warning .value {
    color: #ff9500;
  }
  
  &.error .value {
    color: #ff3b30;
  }
  
  &.good .value {
    color: #30d158;
  }
`;v.ul`
  margin: 8px 0 0 0;
  padding-left: 16px;
  
  li {
    margin-bottom: 4px;
    color: #ff9500;
    font-size: 11px;
    line-height: 1.3;
  }
`;v.button`
  position: fixed;
  top: 20px;
  right: ${i=>i.$visible?"340px":"20px"};
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  z-index: 10001;
  transition: right 0.3s ease-in-out;
  
  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
`;const si=v.header`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
`,oi=v.nav`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
`,ai=v(ue)`
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    opacity: 0.8;
  }
`,xt=v.div`
  display: flex;
  gap: 2rem;
  align-items: center;
  
  @media (max-width: 768px) {
    gap: 1rem;
  }
`,H=v(ue)`
  color: ${i=>i.$active?"#4834d4":"rgba(255, 255, 255, 0.9)"};
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
`,ui=v.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
  
  @media (max-width: 768px) {
    display: none;
  }
`,ci=v.div`
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  
  span {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
  }
`,li=v.button`
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: #fca5a5;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(239, 68, 68, 0.3);
  }
`,di=v.div`
  display: none;
  
  @media (max-width: 768px) {
    display: block;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
  }
`,hi=()=>{const{user:i,logout:e}=re(),r=Wt(),t=n=>r.pathname===n;return u.jsx(si,{children:u.jsxs(oi,{children:[u.jsx(ai,{to:"/",children:"⭐ 星光对对碰"}),i?u.jsxs(u.Fragment,{children:[u.jsxs(xt,{children:[u.jsx(H,{to:"/",$active:t("/"),children:"首页"}),u.jsx(H,{to:"/game",$active:t("/game"),children:"游戏"}),u.jsx(H,{to:"/leaderboard",$active:t("/leaderboard"),children:"排行榜"}),u.jsx(H,{to:"/achievements",$active:t("/achievements"),children:"成就"}),u.jsx(H,{to:"/profile",$active:t("/profile"),children:"个人资料"})]}),u.jsxs(ui,{children:[u.jsxs(ci,{children:[u.jsxs("span",{children:["Lv.",i.level]}),u.jsxs("span",{children:["💰",i.coins]}),u.jsxs("span",{children:["💎",i.gems]})]}),u.jsx(li,{onClick:e,children:"退出"})]})]}):u.jsxs(xt,{children:[u.jsx(H,{to:"/login",$active:t("/login"),children:"登录"}),u.jsx(H,{to:"/register",$active:t("/register"),children:"注册"})]}),u.jsx(di,{children:"☰"})]})})},fi=v.footer`
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
  margin-top: auto;
`,pi=v.div`
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
`,mi=v.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.6;
`,vi=v.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`,we=v.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  
  &:hover {
    color: white;
  }
`,gi=()=>u.jsx(fi,{children:u.jsxs(pi,{children:[u.jsxs(vi,{children:[u.jsx(we,{href:"#about",children:"关于我们"}),u.jsx(we,{href:"#privacy",children:"隐私政策"}),u.jsx(we,{href:"#terms",children:"服务条款"}),u.jsx(we,{href:"#contact",children:"联系我们"})]}),u.jsx(mi,{children:"© 2024 星光对对碰. 保留所有权利. | 一款有趣的三消游戏"})]})});class yi{constructor(e,r=6){F(this,"board");F(this,"size");this.board=e.map(t=>[...t]),this.size=r}isValidPosition(e,r){return e>=0&&e<this.size&&r>=0&&r<this.size}areAdjacent(e,r){const t=Math.abs(e.row-r.row),n=Math.abs(e.col-r.col);return t===1&&n===0||t===0&&n===1}getBoard(){return this.board.map(e=>[...e])}swapGems(e,r){const t=this.board[e.row][e.col];this.board[e.row][e.col]=this.board[r.row][r.col],this.board[r.row][r.col]=t}findHorizontalMatches(){const e=[];for(let r=0;r<this.size;r++){let t=1,n=this.board[r][0];for(let s=1;s<this.size;s++)if(this.board[r][s]===n&&n!==0)t++;else{if(t>=3){const o=[];for(let a=s-t;a<s;a++)o.push({row:r,col:a});e.push({positions:o,type:"horizontal",score:t*10})}t=1,n=this.board[r][s]}if(t>=3){const s=[];for(let o=this.size-t;o<this.size;o++)s.push({row:r,col:o});e.push({positions:s,type:"horizontal",score:t*10})}}return e}findVerticalMatches(){const e=[];for(let r=0;r<this.size;r++){let t=1,n=this.board[0][r];for(let s=1;s<this.size;s++)if(this.board[s][r]===n&&n!==0)t++;else{if(t>=3){const o=[];for(let a=s-t;a<s;a++)o.push({row:a,col:r});e.push({positions:o,type:"vertical",score:t*10})}t=1,n=this.board[s][r]}if(t>=3){const s=[];for(let o=this.size-t;o<this.size;o++)s.push({row:o,col:r});e.push({positions:s,type:"vertical",score:t*10})}}return e}generateBoard(e=6){const r=[];for(let t=0;t<e;t++){r[t]=[];for(let n=0;n<e;n++)r[t][n]=Math.floor(Math.random()*6)+1}return r}isValidMove(e,r,t){if(!this.isValidPosition(r.row,r.col)||!this.isValidPosition(t.row,t.col)||!this.areAdjacent(r,t))return!1;const n=e.map(c=>[...c]),s=n[r.row][r.col];n[r.row][r.col]=n[t.row][t.col],n[t.row][t.col]=s;const o=this.board;this.board=n;const a=this.findMatches();return this.board=o,a.length>0}findMatches(){const e=this.findHorizontalMatches(),r=this.findVerticalMatches();return[...e,...r]}removeMatches(e){let r=0;const t=new Set;return e.forEach(n=>{r+=n.score,n.positions.forEach(s=>{t.add(`${s.row},${s.col}`)})}),t.forEach(n=>{const[s,o]=n.split(",").map(Number);this.board[s][o]=0}),r}applyGravity(){let e=!1;for(let r=0;r<this.size;r++){let t=this.size-1;for(let n=this.size-1;n>=0;n--)this.board[n][r]!==0&&(n!==t&&(this.board[t][r]=this.board[n][r],this.board[n][r]=0,e=!0),t--)}return e}fillEmptySpaces(){for(let e=0;e<this.size;e++)for(let r=0;r<this.size;r++)this.board[r][e]===0&&(this.board[r][e]=Math.floor(Math.random()*6)+1)}makeMove(e,r){if(!this.isValidPosition(e.row,e.col)||!this.isValidPosition(r.row,r.col))return{isValid:!1,matches:[],newBoard:this.board,score:0,cascade:!1};if(!this.areAdjacent(e,r))return{isValid:!1,matches:[],newBoard:this.board,score:0,cascade:!1};this.swapGems(e,r);const t=this.findMatches();if(t.length===0)return this.swapGems(e,r),{isValid:!1,matches:[],newBoard:this.board,score:0,cascade:!1};let n=0,s=[],o=!1;for(;t.length>0;){s.push(...t),n+=this.removeMatches(t),this.applyGravity(),this.fillEmptySpaces();const a=this.findMatches();if(a.length>0)o=!0,t.splice(0,t.length,...a);else break}return{isValid:!0,matches:s,newBoard:this.board.map(a=>[...a]),score:n,cascade:o}}hasPossibleMoves(){for(let e=0;e<this.size;e++)for(let r=0;r<this.size;r++){if(r<this.size-1){if(this.swapGems({row:e,col:r},{row:e,col:r+1}),this.findMatches().length>0)return this.swapGems({row:e,col:r},{row:e,col:r+1}),!0;this.swapGems({row:e,col:r},{row:e,col:r+1})}if(e<this.size-1){if(this.swapGems({row:e,col:r},{row:e+1,col:r}),this.findMatches().length>0)return this.swapGems({row:e,col:r},{row:e+1,col:r}),!0;this.swapGems({row:e,col:r},{row:e+1,col:r})}}return!1}getHint(){for(let e=0;e<this.size;e++)for(let r=0;r<this.size;r++){if(r<this.size-1){const t={row:e,col:r},n={row:e,col:r+1};if(this.swapGems(t,n),this.findMatches().length>0)return this.swapGems(t,n),{from:t,to:n};this.swapGems(t,n)}if(e<this.size-1){const t={row:e,col:r},n={row:e+1,col:r};if(this.swapGems(t,n),this.findMatches().length>0)return this.swapGems(t,n),{from:t,to:n};this.swapGems(t,n)}}return null}calculateScore(e,r=1){let t=0;return e.forEach(n=>{const s=n.positions.length*10,o=Math.max(0,(n.positions.length-3)*5);t+=(s+o)*r}),t}shuffle(){const e=[];for(let t=0;t<this.size;t++)for(let n=0;n<this.size;n++)this.board[t][n]!==0&&e.push(this.board[t][n]);for(let t=e.length-1;t>0;t--){const n=Math.floor(Math.random()*(t+1));[e[t],e[n]]=[e[n],e[t]]}let r=0;for(let t=0;t<this.size;t++)for(let n=0;n<this.size;n++)this.board[t][n]!==0&&(this.board[t][n]=e[r++])}shuffleBoard(e){const r=e.map(s=>[...s]),t=this.board;this.board=r,this.shuffle();const n=this.board.map(s=>[...s]);return this.board=t,n}applyGravityPublic(){let e=!1;for(let r=0;r<this.size;r++){let t=this.size-1;for(let n=this.size-1;n>=0;n--)this.board[n][r]!==0&&(t!==n&&(this.board[t][r]=this.board[n][r],this.board[n][r]=0,e=!0),t--)}return e}getHintWithBoard(e){if(e){const r=this.board;this.board=e;const t=this.getHint();return this.board=r,t}return this.getHint()}}const qe={id:1,name:"第一关",difficulty:"easy",targetScore:1e3,maxMoves:20,boardSize:6,objectives:[{type:"score",target:1e3,description:"达到目标分数"},{type:"collect_items",target:10,description:"收集星星"}]},bi=(i=6)=>{const e=[];for(let r=0;r<i;r++){e[r]=[];for(let t=0;t<i;t++)e[r][t]=Math.floor(Math.random()*6)+1}return e},Ye={board:[],score:0,moves:0,maxMoves:30,movesLeft:30,level:1,currentLevel:1,targetScore:1e3,status:"idle",gameStatus:"playing",selectedCell:null,combo:0,powerUps:{hammer:3,bomb:2,shuffle:1,extraMoves:1}},xi=(i,e)=>{switch(e.type){case"INIT_GAME":return{...Ye,board:e.payload.board,level:e.payload.level,currentLevel:e.payload.level,maxMoves:e.payload.maxMoves,movesLeft:e.payload.maxMoves,targetScore:e.payload.targetScore||1e3,status:"playing",gameStatus:"playing"};case"SELECT_CELL":return{...i,selectedCell:e.payload};case"CLEAR_SELECTION":return{...i,selectedCell:null};case"MAKE_MOVE":return{...i,moves:i.moves+1,movesLeft:i.movesLeft-1,selectedCell:null};case"UPDATE_BOARD":return{...i,board:e.payload.board,score:i.score+e.payload.score,combo:e.payload.combo};case"SET_GAME_STATUS":return{...i,gameStatus:e.payload,status:e.payload==="playing"?"playing":e.payload==="won"?"won":e.payload==="lost"?"lost":"paused"};case"SET_STATUS":return{...i,status:e.payload,gameStatus:e.payload==="playing"?"playing":e.payload==="won"?"won":e.payload==="lost"?"lost":"playing"};case"USE_POWERUP":const r=e.payload.type;return i.powerUps[r]>0?{...i,powerUps:{...i.powerUps,[r]:i.powerUps[r]-1}}:i;case"UPDATE_TIME":return{...i,timeLeft:e.payload};case"RESET_GAME":return Ye;default:return i}},Se={getLevel:async i=>{try{const e=localStorage.getItem("token"),r=await fetch(`/api/levels/${i}`,{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!r.ok)throw new Error("Failed to get level info");return r.json()}catch{return console.warn("Backend not available, using mock level data"),{...qe,id:i,description:`关卡 ${i} - 挑战你的技巧`,powerUps:["hammer","bomb","shuffle"],boardSize:{rows:qe.boardSize,cols:qe.boardSize}}}},startGame:async i=>{try{const e=localStorage.getItem("token"),r=await fetch("/api/game/start",{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({levelId:i})});if(!r.ok)throw new Error("Failed to start game");return r.json()}catch{return console.warn("Backend not available, using mock game data"),{board:bi(6),maxMoves:20}}},makeMove:async(i,e,r)=>{const t=localStorage.getItem("token"),n=await fetch("/api/game/move",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({gameId:i,from:e,to:r})});if(!n.ok)throw new Error("Invalid move");return n.json()},usePowerUp:async(i,e,r)=>{const t=localStorage.getItem("token"),n=await fetch("/api/game/powerup",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({gameId:i,type:e,position:r})});if(!n.ok)throw new Error("Failed to use power-up");return n.json()},submitScore:async(i,e,r)=>{const t=localStorage.getItem("token"),n=await fetch("/api/game/submit",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({gameId:i,score:e,moves:r})});if(!n.ok)throw new Error("Failed to submit score");return n.json()}},zt=m.createContext(void 0),wi=({children:i})=>{const[e,r]=m.useReducer(xi,Ye),[t,n]=m.useState(null),{data:s,isLoading:o}=Tt(["level",e.level],()=>Se.getLevel(e.level),{enabled:e.level>0,retry:!1}),a=je(Se.startGame,{onSuccess:(S,C)=>{r({type:"INIT_GAME",payload:{level:C,board:S.board,maxMoves:S.maxMoves}}),n("mock-game-id"),_.success("游戏开始！")},onError:S=>{_.error(S.message||"游戏启动失败")}}),x={gameState:e,currentLevel:s||null,loading:o,dispatch:r,startGame:async S=>{await a.mutateAsync(S)},makeMove:async(S,C,w)=>{if(!e.board)return;const P=new yi(e.board,6).makeMove(S,C);P.isValid?(w&&P.matches&&P.matches.length>0&&w(P.matches),r({type:"UPDATE_BOARD",payload:{board:P.newBoard,score:P.score,combo:P.cascade?1:0}}),r({type:"MAKE_MOVE",payload:{from:S,to:C,score:P.score}}),e.movesLeft<=1&&(e.score>=(e.targetScore||0)?(r({type:"SET_GAME_STATUS",payload:"won"}),_.success("恭喜过关！")):(r({type:"SET_GAME_STATUS",payload:"lost"}),_.error("游戏结束"))),P.cascade&&_.success("连击！")):_.error("无效移动")},usePowerUp:async(S,C)=>{if(t)try{const w=await Se.usePowerUp(t,S,C);r({type:"USE_POWERUP",payload:{type:S,position:C}}),r({type:"UPDATE_BOARD",payload:{board:w.board,score:w.scoreGained||0,combo:0}}),_.success("道具使用成功！")}catch{_.error("道具使用失败")}},pauseGame:()=>{r({type:"SET_GAME_STATUS",payload:"paused"})},resumeGame:()=>{r({type:"SET_GAME_STATUS",payload:"playing"})},resetGame:()=>{r({type:"RESET_GAME"}),n(null)},submitScore:async()=>{if(t)try{await Se.submitScore(t,e.score,e.moves),_.success("分数提交成功！")}catch{_.error("分数提交失败")}}};return u.jsx(zt.Provider,{value:x,children:i})},Di=()=>{const i=m.useContext(zt);if(i===void 0)throw new Error("useGame must be used within a GameProvider");return i};class Si{constructor(){F(this,"audioContext",null);F(this,"musicAudio",null);F(this,"soundEffects",new Map);F(this,"settings",{masterVolume:.7,musicVolume:.5,sfxVolume:.8,muted:!1});this.initializeAudio(),this.loadSettings()}initializeAudio(){try{this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.musicAudio=new Audio,this.musicAudio.loop=!0,this.musicAudio.preload="auto",this.preloadSoundEffects()}catch(e){console.warn("Audio initialization failed:",e)}}preloadSoundEffects(){const e={gem_select:this.generateTone(440,.1),gem_swap:this.generateTone(523,.2),gem_match:this.generateChord([523,659,784],.3),gem_cascade:this.generateArpeggio([523,659,784,1047],.5),level_complete:this.generateMelody([523,659,784,1047,1319],.8),level_failed:this.generateTone(220,.5),button_click:this.generateTone(800,.1),power_up:this.generateSweep(400,800,.3)};Object.entries(e).forEach(([r,t])=>{const n=new Audio(t);n.preload="auto",this.soundEffects.set(r,n)})}generateTone(e,r){if(!this.audioContext)return"";const t=this.audioContext.sampleRate,n=t*r,s=this.audioContext.createBuffer(1,n,t),o=s.getChannelData(0);for(let a=0;a<n;a++){const c=a/t;o[a]=Math.sin(2*Math.PI*e*c)*Math.exp(-c*3)}return this.bufferToDataUrl(s)}generateChord(e,r){if(!this.audioContext)return"";const t=this.audioContext.sampleRate,n=t*r,s=this.audioContext.createBuffer(1,n,t),o=s.getChannelData(0);for(let a=0;a<n;a++){const c=a/t;let l=0;e.forEach(d=>{l+=Math.sin(2*Math.PI*d*c)/e.length}),o[a]=l*Math.exp(-c*2)}return this.bufferToDataUrl(s)}generateArpeggio(e,r){if(!this.audioContext)return"";const t=this.audioContext.sampleRate,n=t*r,s=this.audioContext.createBuffer(1,n,t),o=s.getChannelData(0),a=r/e.length;for(let c=0;c<n;c++){const l=c/t,d=Math.floor(l/a),f=l-d*a;if(d<e.length){const p=e[d];o[c]=Math.sin(2*Math.PI*p*f)*Math.exp(-f*4)}}return this.bufferToDataUrl(s)}generateMelody(e,r){if(!this.audioContext)return"";const t=this.audioContext.sampleRate,n=t*r,s=this.audioContext.createBuffer(1,n,t),o=s.getChannelData(0),a=r/e.length;for(let c=0;c<n;c++){const l=c/t,d=Math.floor(l/a),f=l-d*a;if(d<e.length){const p=e[d],h=Math.exp(-f*3);o[c]=Math.sin(2*Math.PI*p*f)*h}}return this.bufferToDataUrl(s)}generateSweep(e,r,t){if(!this.audioContext)return"";const n=this.audioContext.sampleRate,s=n*t,o=this.audioContext.createBuffer(1,s,n),a=o.getChannelData(0);for(let c=0;c<s;c++){const l=c/n,d=l/t,f=e+(r-e)*d;a[c]=Math.sin(2*Math.PI*f*l)*(1-d)}return this.bufferToDataUrl(o)}bufferToDataUrl(e){const r=e.length,t=e.getChannelData(0),n=new Int16Array(r);for(let d=0;d<r;d++)n[d]=t[d]*32767;const s=new ArrayBuffer(44),o=new DataView(s),a=(d,f)=>{for(let p=0;p<f.length;p++)o.setUint8(d+p,f.charCodeAt(p))};a(0,"RIFF"),o.setUint32(4,36+n.length*2,!0),a(8,"WAVE"),a(12,"fmt "),o.setUint32(16,16,!0),o.setUint16(20,1,!0),o.setUint16(22,1,!0),o.setUint32(24,e.sampleRate,!0),o.setUint32(28,e.sampleRate*2,!0),o.setUint16(32,2,!0),o.setUint16(34,16,!0),a(36,"data"),o.setUint32(40,n.length*2,!0);const c=new Uint8Array(44+n.length*2);c.set(new Uint8Array(s),0),c.set(new Uint8Array(n.buffer),44);const l=new Blob([c],{type:"audio/wav"});return URL.createObjectURL(l)}playSoundEffect(e){if(this.settings.muted)return;const r=this.soundEffects.get(e);r&&(r.volume=this.settings.sfxVolume*this.settings.masterVolume,r.currentTime=0,r.play().catch(t=>{console.warn(`Failed to play sound effect ${e}:`,t)}))}playBackgroundMusic(e){this.settings.muted||!this.musicAudio||(e&&this.musicAudio.src!==e&&(this.musicAudio.src=e),this.musicAudio.volume=this.settings.musicVolume*this.settings.masterVolume,this.musicAudio.play().catch(r=>{console.warn("Failed to play background music:",r)}))}stopBackgroundMusic(){this.musicAudio&&(this.musicAudio.pause(),this.musicAudio.currentTime=0)}setVolume(e,r){this.settings[e]=Math.max(0,Math.min(1,r)),this.saveSettings(),this.musicAudio&&e==="musicVolume"&&(this.musicAudio.volume=this.settings.musicVolume*this.settings.masterVolume)}toggleMute(){this.settings.muted=!this.settings.muted,this.saveSettings(),this.settings.muted&&this.stopBackgroundMusic()}getSettings(){return{...this.settings}}saveSettings(){localStorage.setItem("audioSettings",JSON.stringify(this.settings))}loadSettings(){const e=localStorage.getItem("audioSettings");if(e)try{this.settings={...this.settings,...JSON.parse(e)}}catch(r){console.warn("Failed to load audio settings:",r)}}dispose(){this.stopBackgroundMusic(),this.soundEffects.forEach(e=>{e.src.startsWith("blob:")&&URL.revokeObjectURL(e.src)}),this.soundEffects.clear(),this.audioContext&&this.audioContext.state!=="closed"&&this.audioContext.close()}}const qt=m.createContext(null),Ci=()=>{const i=m.useContext(qt);if(!i)throw new Error("useAudio must be used within an AudioProvider");return i},Oi=({children:i})=>{const e=m.useRef(null);m.useEffect(()=>(e.current=new Si,()=>{e.current&&e.current.dispose()}),[]);const r=o=>{e.current&&e.current.playSoundEffect(o)},t=o=>{e.current&&e.current.playBackgroundMusic(o)},n=()=>{e.current&&e.current.stopBackgroundMusic()},s={audioManager:e.current,playSound:r,playBackgroundMusic:t,stopBackgroundMusic:n};return u.jsx(qt.Provider,{value:s,children:i})},Ui=()=>{const{playSound:i}=Ci();return{onGemSelect:()=>i("gem_select"),onGemSwap:()=>i("gem_swap"),onGemMatch:()=>i("gem_match"),onGemCascade:()=>i("gem_cascade"),onLevelComplete:()=>i("level_complete"),onLevelFailed:()=>i("level_failed"),onButtonClick:()=>i("button_click"),onPowerUp:()=>i("power_up")}},Ei=v.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`,ji=v.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`,Bt=v.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
`,se=v.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`,Z=({children:i})=>{const{user:e,loading:r}=re();return r?u.jsx(Bt,{children:u.jsx("div",{children:"加载中..."})}):e?u.jsx(u.Fragment,{children:i}):u.jsx(Xe,{to:"/login",replace:!0})},wt=({children:i})=>{const{user:e,loading:r}=re();return r?u.jsx(Bt,{children:u.jsx("div",{children:"加载中..."})}):e?u.jsx(Xe,{to:"/",replace:!0}):u.jsx(u.Fragment,{children:i})},Pi=()=>{const[i,e]=m.useState(!1);return u.jsxs(Ei,{children:[u.jsx(hi,{}),u.jsx(ji,{children:u.jsxs(Jt,{children:[u.jsx(U,{path:"/login",element:u.jsx(wt,{children:u.jsx(Qn,{})})}),u.jsx(U,{path:"/register",element:u.jsx(wt,{children:u.jsx(Vn,{})})}),u.jsx(U,{path:"/",element:u.jsx(Z,{children:u.jsx(An,{})})}),u.jsx(U,{path:"/levels",element:u.jsx(Z,{children:u.jsx(m.Suspense,{fallback:u.jsxs(se,{children:[u.jsx("div",{className:"spinner"}),u.jsx("div",{children:"加载关卡..."})]}),children:u.jsx(Yn,{})})})}),u.jsx(U,{path:"/game",element:u.jsx(Z,{children:u.jsx(m.Suspense,{fallback:u.jsxs(se,{children:[u.jsx("div",{className:"spinner"}),u.jsx("div",{children:"加载游戏..."})]}),children:u.jsx(Jn,{})})})}),u.jsx(U,{path:"/profile",element:u.jsx(Z,{children:u.jsx(m.Suspense,{fallback:u.jsxs(se,{children:[u.jsx("div",{className:"spinner"}),u.jsx("div",{children:"加载个人资料..."})]}),children:u.jsx(ei,{})})})}),u.jsx(U,{path:"/leaderboard",element:u.jsx(Z,{children:u.jsx(m.Suspense,{fallback:u.jsxs(se,{children:[u.jsx("div",{className:"spinner"}),u.jsx("div",{children:"加载排行榜..."})]}),children:u.jsx(Xn,{})})})}),u.jsx(U,{path:"/achievements",element:u.jsx(Z,{children:u.jsx(m.Suspense,{fallback:u.jsxs(se,{children:[u.jsx("div",{className:"spinner"}),u.jsx("div",{children:"加载成就..."})]}),children:u.jsx(Zn,{})})})}),u.jsx(U,{path:"*",element:u.jsx(Xe,{to:"/",replace:!0})})]})}),u.jsx(gi,{}),!1]})},Ri=()=>(m.useEffect(()=>(Re.preloadOnIdle(),Re.preloadOnUserInteraction(),()=>{Re.clearPreloadedComponents()}),[]),u.jsx(jn,{children:u.jsx(Oi,{children:u.jsx(wi,{children:u.jsx(Pi,{})})})})),D={colors:{primary:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81"},secondary:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12"},gems:{red:"#ef4444",blue:"#3b82f6",green:"#10b981",yellow:"#f59e0b",purple:"#8b5cf6",orange:"#f97316"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6",background:{primary:"linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)",secondary:"linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",card:"rgba(255, 255, 255, 0.1)",cardHover:"rgba(255, 255, 255, 0.15)",overlay:"rgba(0, 0, 0, 0.8)",glass:"rgba(255, 255, 255, 0.05)"},text:{primary:"#f9fafb",secondary:"#d1d5db",muted:"#9ca3af",inverse:"#111827"}},fonts:{primary:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif",mono:"'JetBrains Mono', 'Fira Code', monospace"},fontSizes:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem"},fontWeights:{light:300,normal:400,medium:500,semibold:600,bold:700},spacing:{0:"0",1:"0.25rem",2:"0.5rem",3:"0.75rem",4:"1rem",5:"1.25rem",6:"1.5rem",8:"2rem",10:"2.5rem",12:"3rem",16:"4rem",20:"5rem",24:"6rem",32:"8rem",40:"10rem",48:"12rem",56:"14rem",64:"16rem"},borderRadius:{none:"0",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",base:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",glow:"0 0 20px rgba(99, 102, 241, 0.5)",glowGold:"0 0 20px rgba(251, 191, 36, 0.4)",glowPurple:"0 0 20px rgba(139, 92, 246, 0.4)",gemGlow:"0 0 15px rgba(251, 191, 36, 0.6)",inner:"inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",text:"0 1px 2px rgba(0, 0, 0, 0.5)"},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},transitions:{fast:"150ms",base:"300ms",slow:"500ms"},zIndex:{dropdown:1e3,sticky:1020,fixed:1030,modal:1040,popover:1050,tooltip:1060}},Ii={sm:`@media (min-width: ${D.breakpoints.sm})`,md:`@media (min-width: ${D.breakpoints.md})`,lg:`@media (min-width: ${D.breakpoints.lg})`,xl:`@media (min-width: ${D.breakpoints.xl})`,"2xl":`@media (min-width: ${D.breakpoints["2xl"]})`,maxSm:"@media (max-width: 639px)",maxMd:"@media (max-width: 767px)",maxLg:"@media (max-width: 1023px)"},$i={glassmorphism:`
    background: ${D.colors.background.card};
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  `,buttonBase:`
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: ${D.borderRadius.lg};
    font-weight: ${D.fontWeights.semibold};
    cursor: pointer;
    transition: all ${D.transitions.base} ease-in-out;
    user-select: none;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  `},_i=Xt`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    font-family: ${({theme:i})=>i.fonts.primary};
    background: ${({theme:i})=>i.colors.background.primary};
    color: ${({theme:i})=>i.colors.text.primary};
    line-height: 1.5;
    overflow-x: hidden;
    min-height: 100vh;
    
    /* 禁用移动端的选择和缩放 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({theme:i})=>i.colors.gray[800]};
    border-radius: ${({theme:i})=>i.borderRadius.base};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({theme:i})=>i.colors.gray[600]};
    border-radius: ${({theme:i})=>i.borderRadius.base};
    
    &:hover {
      background: ${({theme:i})=>i.colors.gray[500]};
    }
  }

  /* 按钮和输入框重置 */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    
    &:focus {
      outline: 2px solid ${({theme:i})=>i.colors.primary[500]};
      outline-offset: 2px;
    }
    
    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    background: none;
    
    &:focus {
      outline: 2px solid ${({theme:i})=>i.colors.primary[500]};
      outline-offset: 2px;
    }
  }

  /* 链接样式 */
  a {
    color: ${({theme:i})=>i.colors.primary[400]};
    text-decoration: none;
    
    &:hover {
      color: ${({theme:i})=>i.colors.primary[300]};
      text-decoration: underline;
    }
  }

  /* 图片响应式 */
  img {
    max-width: 100%;
    height: auto;
  }

  /* 隐藏元素 */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* 动画类 */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .bounce {
    animation: bounce 0.6s ease-in-out;
  }

  .pulse {
    animation: pulse 2s infinite;
  }

  .glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* 动画定义 */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      transform: translate3d(0, -10px, 0);
    }
    70% {
      transform: translate3d(0, -5px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
    }
    to {
      box-shadow: 0 0 30px rgba(99, 102, 241, 0.8);
    }
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    html {
      font-size: 14px;
    }
    
    body {
      /* 防止移动端页面弹跳 */
      position: fixed;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    
    #root {
      height: 100vh;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    body {
      background: #000;
      color: #fff;
    }
  }

  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
`,Mi=new Or({defaultOptions:{queries:{retry:2,staleTime:5*60*1e3,cacheTime:10*60*1e3}}});Be.createRoot(document.getElementById("root")).render(u.jsx(j.StrictMode,{children:u.jsx(Yt,{children:u.jsx(Mr,{client:Mi,children:u.jsxs(Zt,{theme:D,children:[u.jsx(_i,{}),u.jsx(Ri,{}),u.jsx(En,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151",borderRadius:"12px",fontSize:"14px"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})})}));export{Tt as a,re as b,Ui as c,$i as d,u as j,Ii as m,D as t,Di as u};
//# sourceMappingURL=index-CNCEp3EQ.js.map
