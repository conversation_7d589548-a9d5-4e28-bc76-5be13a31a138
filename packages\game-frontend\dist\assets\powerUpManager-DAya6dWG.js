var h=Object.defineProperty;var m=(i,e,t)=>e in i?h(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var u=(i,e,t)=>m(i,typeof e!="symbol"?e+"":e,t);import{P as l}from"./powerups-DNw9s1Qv.js";class v{constructor(e){u(this,"activeEffects",new Map);u(this,"onEffectChange");this.onEffectChange=e}usePowerUp(e){const{type:t,position:c,board:s}=e;switch(t){case"bomb":return this.useBomb(c,s);case"rainbow":return this.useRainbow(c,s);case"hammer":return this.useHammer(c,s);case"shuffle":return this.useShuffle(s);case"time_extend":return this.useTimeExtend();case"score_boost":return this.useScoreBoost();case"hint":return this.useHint(s);case"freeze":return this.useFreeze();default:return{success:!1,message:"未知的道具类型"}}}useBomb(e,t){const{row:c,col:s}=e,o=[];let n=0;for(let r=Math.max(0,c-1);r<=Math.min(t.length-1,c+1);r++)for(let f=Math.max(0,s-1);f<=Math.min(t[0].length-1,s+1);f++)t[r][f]!==0&&(o.push({row:r,col:f}),n+=10);return{success:!0,message:`炸弹消除了 ${o.length} 个宝石！`,effects:{gemsRemoved:o,scoreGained:n}}}useRainbow(e,t){const{row:c,col:s}=e,o=t[c][s];if(o===0)return{success:!1,message:"请选择一个有效的宝石"};const n=[];let r=0;for(let f=0;f<t.length;f++)for(let a=0;a<t[0].length;a++)t[f][a]===o&&(n.push({row:f,col:a}),r+=15);return{success:!0,message:`彩虹宝石消除了 ${n.length} 个同色宝石！`,effects:{gemsRemoved:n,scoreGained:r}}}useHammer(e,t){const{row:c,col:s}=e;return t[c][s]===0?{success:!1,message:"请选择一个有效的宝石"}:{success:!0,message:"锤子消除了选中的宝石！",effects:{gemsRemoved:[{row:c,col:s}],scoreGained:5}}}useShuffle(e){return{success:!0,message:"棋盘已重新洗牌！",effects:{boardShuffled:!0}}}useTimeExtend(){return{success:!0,message:"时间延长了30秒！",effects:{timeAdded:30}}}useScoreBoost(){const e={type:"score_boost",startTime:Date.now(),duration:6e4,multiplier:2,active:!0};return this.activeEffects.set("score_boost",e),this.notifyEffectChange(),setTimeout(()=>{this.removeEffect("score_boost")},6e4),{success:!0,message:"分数加成已激活！接下来60秒内分数翻倍！",effects:{effectApplied:e}}}useHint(e){return{success:!0,message:"已显示可能的移动提示！"}}useFreeze(){const e={type:"freeze",startTime:Date.now(),duration:15e3,active:!0};return this.activeEffects.set("freeze",e),this.notifyEffectChange(),setTimeout(()=>{this.removeEffect("freeze")},15e3),{success:!0,message:"时间已冰冻15秒！",effects:{effectApplied:e}}}getActiveEffects(){return Array.from(this.activeEffects.values())}hasEffect(e){return this.activeEffects.has(e)}getEffect(e){return this.activeEffects.get(e)}removeEffect(e){this.activeEffects.delete(e),this.notifyEffectChange()}notifyEffectChange(){this.onEffectChange&&this.onEffectChange(this.getActiveEffects())}clearAllEffects(){this.activeEffects.clear(),this.notifyEffectChange()}getScoreMultiplier(){const e=this.getEffect("score_boost");return e!=null&&e.active&&e.multiplier||1}isTimeFrozen(){const e=this.getEffect("freeze");return(e==null?void 0:e.active)||!1}update(){const e=Date.now();let t=!1;for(const[c,s]of this.activeEffects.entries())s.duration&&e-s.startTime>=s.duration&&(this.activeEffects.delete(c),t=!0);t&&this.notifyEffectChange()}getPowerUpInfo(e){return l[e]}}export{v as PowerUpManager};
//# sourceMappingURL=powerUpManager-DAya6dWG.js.map
