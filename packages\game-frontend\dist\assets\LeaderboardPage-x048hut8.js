import{j as s,t as e,m as n}from"./index-CNCEp3EQ.js";import{r as o}from"./vendor-Dneogk0_.js";import{d as a}from"./ui-ldAE8JkK.js";import{g as b,a as y,L as c}from"./leaderboard-Or32oZ16.js";import{C as l}from"./Card-B65VmGcU.js";import{B as j}from"./Button-BlkTGlvm.js";import"./router-DMCr7QLp.js";const v=a.div`
  min-height: 100vh;
  background: ${e.colors.background.primary};
  padding: ${e.spacing[6]} ${e.spacing[4]};

  ${n.maxMd} {
    padding: ${e.spacing[4]} ${e.spacing[3]};
  }

  ${n.maxSm} {
    padding: ${e.spacing[3]} ${e.spacing[2]};
  }
`,S=a.div`
  text-align: center;
  margin-bottom: ${e.spacing[8]};

  ${n.maxMd} {
    margin-bottom: ${e.spacing[6]};
  }
`,z=a.h1`
  color: ${e.colors.text.primary};
  font-size: ${e.fontSizes["4xl"]};
  font-weight: ${e.fontWeights.bold};
  margin-bottom: ${e.spacing[4]};
  text-shadow: ${e.shadows.text};

  ${n.maxMd} {
    font-size: ${e.fontSizes["3xl"]};
  }

  ${n.maxSm} {
    font-size: ${e.fontSizes["2xl"]};
  }
`,k=a.div`
  display: flex;
  justify-content: center;
  gap: ${e.spacing[2]};
  margin-bottom: ${e.spacing[6]};
  flex-wrap: wrap;

  ${n.maxMd} {
    margin-bottom: ${e.spacing[4]};
  }
`,w=a(j)`
  ${t=>t.$active&&`
    background: ${e.colors.secondary[500]};
    color: white;

    &:hover {
      background: ${e.colors.secondary[600]};
    }
  `}

  ${n.maxSm} {
    font-size: ${e.fontSizes.xs};
    padding: ${e.spacing[1]} ${e.spacing[2]};
  }
`,C=a.div`
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: ${e.spacing[6]};
  max-width: 1200px;
  margin: 0 auto;

  ${n.maxLg} {
    grid-template-columns: 1fr;
    gap: ${e.spacing[4]};
  }
`,L=a(l)`
  padding: ${e.spacing[6]};

  ${n.maxMd} {
    padding: ${e.spacing[4]};
  }

  ${n.maxSm} {
    padding: ${e.spacing[3]};
  }
`,R=a.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${e.spacing[4]};

  h2 {
    color: ${e.colors.text.primary};
    font-size: ${e.fontSizes.xl};
    margin: 0;
    display: flex;
    align-items: center;
    gap: ${e.spacing[2]};
  }

  .last-updated {
    color: ${e.colors.text.secondary};
    font-size: ${e.fontSizes.sm};
  }
`,W=a.div`
  display: flex;
  flex-direction: column;
  gap: ${e.spacing[2]};
`,U=a.div`
  display: flex;
  align-items: center;
  gap: ${e.spacing[3]};
  padding: ${e.spacing[3]};
  background: ${t=>t.$isCurrentUser?"rgba(251, 191, 36, 0.1)":"rgba(255, 255, 255, 0.05)"};
  border: 1px solid ${t=>t.$isCurrentUser?"rgba(251, 191, 36, 0.3)":"rgba(255, 255, 255, 0.1)"};
  border-radius: ${e.borderRadius.lg};
  transition: all ${e.transitions.base} ease-in-out;

  &:hover {
    background: ${t=>t.$isCurrentUser?"rgba(251, 191, 36, 0.15)":"rgba(255, 255, 255, 0.08)"};
    transform: translateY(-1px);
  }

  ${n.maxSm} {
    padding: ${e.spacing[2]};
    gap: ${e.spacing[2]};
  }
`,E=a.div`
  font-size: ${e.fontSizes.lg};
  font-weight: ${e.fontWeights.bold};
  color: ${t=>t.$rank===1?"#ffd700":t.$rank===2?"#c0c0c0":t.$rank===3?"#cd7f32":e.colors.text.secondary};
  min-width: 40px;
  text-align: center;

  ${n.maxSm} {
    font-size: ${e.fontSizes.base};
    min-width: 30px;
  }
`,D=()=>{const[t,d]=o.useState("global_score"),[r,x]=o.useState(null),[m,f]=o.useState([]),[h,g]=o.useState(!1);o.useEffect(()=>{g(!0),setTimeout(()=>{x(b(t)),g(!1)},500)},[t]),o.useEffect(()=>{f(y())},[]);const $=Object.entries(c).map(([i,u])=>({key:i,...u}));return s.jsxs(v,{children:[s.jsxs(S,{children:[s.jsx(z,{children:"🏆 排行榜"}),s.jsx(k,{children:$.map(i=>s.jsxs(w,{variant:"ghost",size:"sm",$active:t===i.key,onClick:()=>d(i.key),children:[i.icon," ",i.name]},i.key))})]}),s.jsxs(C,{children:[s.jsx(L,{children:r&&s.jsxs(s.Fragment,{children:[s.jsxs(R,{children:[s.jsxs("h2",{children:[c[t].icon," ",c[t].name]}),s.jsxs("div",{className:"last-updated",children:["最后更新: ",r.lastUpdated.toLocaleTimeString()]})]}),h?s.jsx("div",{style:{textAlign:"center",padding:"2rem",color:e.colors.text.secondary},children:"加载中..."}):s.jsx(W,{children:r.entries.map(i=>s.jsxs(U,{$isCurrentUser:i.isCurrentUser,children:[s.jsx(E,{$rank:i.rank,children:i.rank<=3?i.rank===1?"🥇":i.rank===2?"🥈":"🥉":i.rank}),s.jsx("img",{src:i.avatar||`https://api.dicebear.com/7.x/avataaars/svg?seed=${i.userId}`,alt:i.username,style:{width:"48px",height:"48px",borderRadius:"50%",border:"2px solid rgba(255, 255, 255, 0.2)"}}),s.jsxs("div",{style:{flex:1,minWidth:0},children:[s.jsxs("div",{style:{color:e.colors.text.primary,fontWeight:e.fontWeights.semibold,fontSize:e.fontSizes.base,marginBottom:"2px"},children:[i.username,i.isCurrentUser&&" (你)"]}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:e.spacing[1],fontSize:e.fontSizes.xs,color:e.colors.text.secondary},children:[s.jsx("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:i.isOnline?"#10b981":"#6b7280"}}),i.isOnline?"在线":"离线",i.isFriend&&" • 好友"]})]}),s.jsxs("div",{style:{textAlign:"right"},children:[s.jsx("div",{style:{color:e.colors.secondary[400],fontWeight:e.fontWeights.bold,fontSize:e.fontSizes.lg},children:p(i.value,t)}),s.jsx("div",{style:{color:e.colors.text.secondary,fontSize:e.fontSizes.xs},children:c[t].valueLabel})]})]},i.id))})]})}),s.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:e.spacing[4]},children:[s.jsxs(l,{style:{padding:e.spacing[4]},children:[s.jsx("h3",{style:{color:e.colors.text.primary,margin:"0 0 1rem 0",display:"flex",alignItems:"center",gap:e.spacing[2]},children:"👥 好友动态"}),s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:e.spacing[2]},children:m.slice(0,5).map(i=>s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:e.spacing[2],padding:e.spacing[2],background:"rgba(255, 255, 255, 0.05)",borderRadius:e.borderRadius.base},children:[s.jsx("img",{src:i.avatar,alt:i.username,style:{width:"32px",height:"32px",borderRadius:"50%"}}),s.jsxs("div",{style:{flex:1,minWidth:0},children:[s.jsx("div",{style:{color:e.colors.text.primary,fontSize:e.fontSizes.sm,fontWeight:e.fontWeights.medium},children:i.username}),s.jsxs("div",{style:{color:e.colors.text.secondary,fontSize:e.fontSizes.xs},children:["等级 ",i.level," • ",i.totalScore.toLocaleString()," 分"]})]}),s.jsx("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:i.isOnline?"#10b981":"#6b7280"}})]},i.id))})]}),s.jsxs(l,{style:{padding:e.spacing[4]},children:[s.jsx("h3",{style:{color:e.colors.text.primary,margin:"0 0 1rem 0",display:"flex",alignItems:"center",gap:e.spacing[2]},children:"📊 我的排名"}),(r==null?void 0:r.currentUserEntry)&&s.jsxs("div",{style:{background:"rgba(251, 191, 36, 0.1)",border:"1px solid rgba(251, 191, 36, 0.3)",borderRadius:e.borderRadius.lg,padding:e.spacing[3]},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:e.spacing[2]},children:[s.jsx("span",{style:{color:e.colors.text.primary},children:"当前排名"}),s.jsxs("span",{style:{color:e.colors.secondary[400],fontWeight:e.fontWeights.bold,fontSize:e.fontSizes.lg},children:["#",r.currentUserRank]})]}),s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx("span",{style:{color:e.colors.text.secondary,fontSize:e.fontSizes.sm},children:c[t].valueLabel}),s.jsx("span",{style:{color:e.colors.secondary[400],fontWeight:e.fontWeights.semibold},children:p(r.currentUserEntry.value,t)})]})]})]})]})]})]})};function p(t,d){switch(d){case"speed_run":return`${t}秒`;case"combo_record":return`${t}连击`;case"level_completion":return`${t}关`;case"achievement_count":return`${t}个`;default:return t.toLocaleString()}}export{D as default};
//# sourceMappingURL=LeaderboardPage-x048hut8.js.map
