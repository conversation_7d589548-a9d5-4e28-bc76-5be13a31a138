"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./database/database");
const errorHandler_1 = require("./middleware/errorHandler");
const auth_1 = require("./routes/auth");
const user_1 = require("./routes/user");
const levels_1 = require("./routes/levels");
const game_1 = require("./routes/game");
const inventory_1 = require("./routes/inventory");
const quests_1 = require("./routes/quests");
const admin_1 = require("./routes/admin");
const handlers_1 = require("./socket/handlers");
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.SOCKET_CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
    }
});
const PORT = process.env.PORT || 3001;
app.use((0, helmet_1.default)());
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined'));
const corsOptions = {
    origin: (process.env.CORS_ORIGIN || 'http://localhost:3000').split(','),
    credentials: true,
    optionsSuccessStatus: 200
};
app.use((0, cors_1.default)(corsOptions));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: '请求过于频繁，请稍后再试'
        }
    }
});
app.use('/api/', limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        }
    });
});
app.use('/api/auth', auth_1.authRoutes);
app.use('/api/user', user_1.userRoutes);
app.use('/api/levels', levels_1.levelRoutes);
app.use('/api/game', game_1.gameRoutes);
app.use('/api/inventory', inventory_1.inventoryRoutes);
app.use('/api/quests', quests_1.questRoutes);
app.use('/api/admin', admin_1.adminRoutes);
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: '请求的资源不存在'
        }
    });
});
app.use(errorHandler_1.errorHandler);
(0, handlers_1.setupSocketHandlers)(io);
async function startServer() {
    try {
        await database_1.Database.initialize();
        console.log('✅ 数据库初始化成功');
        server.listen(PORT, () => {
            console.log(`🚀 服务器启动成功`);
            console.log(`📍 HTTP服务: http://localhost:${PORT}`);
            console.log(`🔌 Socket.IO服务: ws://localhost:${PORT}`);
            console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
        });
    }
    catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}
process.on('SIGTERM', () => {
    console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        database_1.Database.close();
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🛑 收到SIGINT信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        database_1.Database.close();
        process.exit(0);
    });
});
startServer();
//# sourceMappingURL=index.js.map