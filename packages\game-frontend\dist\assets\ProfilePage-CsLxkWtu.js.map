{"version": 3, "file": "ProfilePage-CsLxkWtu.js", "sources": ["../../src/pages/ProfilePage.tsx"], "sourcesContent": ["import React from 'react'\nimport styled from 'styled-components'\nimport { useAuth } from '../contexts/AuthContext'\n\nconst Container = styled.div`\n  flex: 1;\n  padding: 2rem;\n  max-width: 800px;\n  margin: 0 auto;\n`\n\nconst Title = styled.h1`\n  color: white;\n  font-size: 2.5rem;\n  margin-bottom: 2rem;\n  text-align: center;\n`\n\nconst ProfileCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  margin-bottom: 2rem;\n`\n\nconst UserInfo = styled.div`\n  color: white;\n  text-align: center;\n  \n  h2 {\n    font-size: 2rem;\n    margin-bottom: 1rem;\n  }\n  \n  p {\n    margin: 0.5rem 0;\n    opacity: 0.9;\n  }\n`\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-top: 2rem;\n`\n\nconst StatCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  \n  h3 {\n    color: white;\n    font-size: 2rem;\n    font-weight: bold;\n    margin-bottom: 0.5rem;\n  }\n  \n  p {\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 0.9rem;\n    margin: 0;\n  }\n`\n\nconst Button = styled.button`\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #4834d4, #686de0);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 2rem;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n`\n\nconst ProfilePage: React.FC = () => {\n  const { user, logout } = useAuth()\n\n  if (!user) {\n    return (\n      <Container>\n        <Title>个人资料</Title>\n        <ProfileCard>\n          <UserInfo>\n            <p>请先登录</p>\n          </UserInfo>\n        </ProfileCard>\n      </Container>\n    )\n  }\n\n  return (\n    <Container>\n      <Title>个人资料</Title>\n      \n      <ProfileCard>\n        <UserInfo>\n          <h2>{user.username}</h2>\n          <p>邮箱：{user.email}</p>\n          <p>注册时间：{new Date(user.createdAt).toLocaleDateString()}</p>\n          <p>最后登录：{new Date(user.lastLoginAt).toLocaleDateString()}</p>\n        </UserInfo>\n        \n        <StatsGrid>\n          <StatCard>\n            <h3>{user.level}</h3>\n            <p>当前等级</p>\n          </StatCard>\n          <StatCard>\n            <h3>{user.experience}</h3>\n            <p>总经验值</p>\n          </StatCard>\n          <StatCard>\n            <h3>{user.coins}</h3>\n            <p>金币</p>\n          </StatCard>\n          <StatCard>\n            <h3>{user.gems}</h3>\n            <p>宝石</p>\n          </StatCard>\n        </StatsGrid>\n        \n        <div style={{ textAlign: 'center' }}>\n          <Button onClick={logout}>\n            退出登录\n          </Button>\n        </div>\n      </ProfileCard>\n    </Container>\n  )\n}\n\nexport default ProfilePage\n"], "names": ["Container", "styled", "Title", "ProfileCard", "UserInfo", "StatsGrid", "StatCard", "<PERSON><PERSON>", "ProfilePage", "user", "logout", "useAuth", "jsx", "jsxs"], "mappings": "8IAIA,MAAMA,EAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnBC,EAAQD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOfE,EAAcF,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrBG,EAAWH,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAelBI,EAAYJ,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnBK,EAAWL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBlBM,EAASN,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBhBO,EAAwB,IAAM,CAClC,KAAM,CAAE,KAAAC,EAAM,OAAAC,CAAA,EAAWC,EAAA,EAEzB,OAAKF,SAcFT,EAAA,CACC,SAAA,CAAAY,EAAAA,IAACV,GAAM,SAAA,MAAA,CAAI,SAEVC,EAAA,CACC,SAAA,CAAAU,OAACT,EAAA,CACC,SAAA,CAAAQ,EAAAA,IAAC,KAAA,CAAI,WAAK,QAAA,CAAS,SAClB,IAAA,CAAE,SAAA,CAAA,MAAIH,EAAK,KAAA,EAAM,SACjB,IAAA,CAAE,SAAA,CAAA,QAAM,IAAI,KAAKA,EAAK,SAAS,EAAE,mBAAA,CAAmB,EAAE,SACtD,IAAA,CAAE,SAAA,CAAA,QAAM,IAAI,KAAKA,EAAK,WAAW,EAAE,mBAAA,CAAmB,CAAA,CAAE,CAAA,EAC3D,SAECJ,EAAA,CACC,SAAA,CAAAQ,OAACP,EAAA,CACC,SAAA,CAAAM,EAAAA,IAAC,KAAA,CAAI,WAAK,KAAA,CAAM,EAChBA,EAAAA,IAAC,KAAE,SAAA,MAAA,CAAI,CAAA,EACT,SACCN,EAAA,CACC,SAAA,CAAAM,EAAAA,IAAC,KAAA,CAAI,WAAK,UAAA,CAAW,EACrBA,EAAAA,IAAC,KAAE,SAAA,MAAA,CAAI,CAAA,EACT,SACCN,EAAA,CACC,SAAA,CAAAM,EAAAA,IAAC,KAAA,CAAI,WAAK,KAAA,CAAM,EAChBA,EAAAA,IAAC,KAAE,SAAA,IAAA,CAAE,CAAA,EACP,SACCN,EAAA,CACC,SAAA,CAAAM,EAAAA,IAAC,KAAA,CAAI,WAAK,IAAA,CAAK,EACfA,EAAAA,IAAC,KAAE,SAAA,IAAA,CAAE,CAAA,CAAA,CACP,CAAA,EACF,EAEAA,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,UAAW,QAAA,EACvB,SAAAA,EAAAA,IAACL,EAAA,CAAO,QAASG,EAAQ,SAAA,MAAA,CAEzB,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EACF,SAhDGV,EAAA,CACC,SAAA,CAAAY,EAAAA,IAACV,GAAM,SAAA,MAAA,CAAI,EACXU,EAAAA,IAACT,GACC,SAAAS,EAAAA,IAACR,EAAA,CACC,eAAC,IAAA,CAAE,SAAA,MAAA,CAAI,EACT,CAAA,CACF,CAAA,EACF,CA2CN"}