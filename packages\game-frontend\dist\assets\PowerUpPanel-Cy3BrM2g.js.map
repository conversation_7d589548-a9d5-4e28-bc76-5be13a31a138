{"version": 3, "file": "PowerUpPanel-Cy3BrM2g.js", "sources": ["../../src/components/Game/PowerUpPanel.tsx"], "sourcesContent": ["import React from 'react'\nimport styled from 'styled-components'\nimport { PowerUpType, PowerUpInstance, POWER_UPS, RARITY_CONFIG } from '../../types/powerups'\nimport { theme, media } from '../../styles/theme'\nimport Button from '../UI/Button'\nimport Card from '../UI/Card'\n\ninterface PowerUpPanelProps {\n  powerUps: PowerUpInstance[]\n  onUsePowerUp: (type: PowerUpType) => void\n  selectedPowerUp: PowerUpType | null\n  onSelectPowerUp: (type: PowerUpType | null) => void\n  disabled?: boolean\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${theme.spacing[3]};\n  padding: ${theme.spacing[4]};\n  background: ${theme.colors.background.card};\n  border-radius: ${theme.borderRadius.xl};\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  \n  ${media.maxMd} {\n    padding: ${theme.spacing[3]};\n    gap: ${theme.spacing[2]};\n  }\n`\n\nconst Title = styled.h3`\n  color: ${theme.colors.text.primary};\n  font-size: ${theme.fontSizes.lg};\n  font-weight: ${theme.fontWeights.semibold};\n  margin: 0;\n  text-align: center;\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.base};\n  }\n`\n\nconst PowerUpGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\n  gap: ${theme.spacing[2]};\n  \n  ${media.maxMd} {\n    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));\n    gap: ${theme.spacing[1]};\n  }\n`\n\nconst PowerUpCard = styled(Card)<{ \n  $selected: boolean\n  $disabled: boolean\n  $rarity: string\n}>`\n  padding: ${theme.spacing[3]};\n  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};\n  text-align: center;\n  transition: all ${theme.transitions.base} ease-in-out;\n  position: relative;\n  min-height: 80px;\n  \n  ${props => {\n    const rarityConfig = RARITY_CONFIG[props.$rarity as keyof typeof RARITY_CONFIG]\n    return `\n      border: 2px solid ${props.$selected ? rarityConfig.color : rarityConfig.borderColor};\n      background: ${props.$selected ? rarityConfig.bgColor : 'rgba(255, 255, 255, 0.05)'};\n    `\n  }}\n  \n  ${props => props.$disabled && `\n    opacity: 0.5;\n    filter: grayscale(50%);\n  `}\n  \n  ${props => !props.$disabled && `\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: ${theme.shadows.lg};\n      border-color: ${RARITY_CONFIG[props.$rarity as keyof typeof RARITY_CONFIG].color};\n    }\n  `}\n  \n  ${media.maxMd} {\n    padding: ${theme.spacing[2]};\n    min-height: 70px;\n  }\n`\n\nconst PowerUpIcon = styled.div`\n  font-size: 24px;\n  margin-bottom: ${theme.spacing[1]};\n  \n  ${media.maxMd} {\n    font-size: 20px;\n  }\n`\n\nconst PowerUpName = styled.div`\n  font-size: ${theme.fontSizes.xs};\n  color: ${theme.colors.text.secondary};\n  font-weight: ${theme.fontWeights.medium};\n  line-height: 1.2;\n  \n  ${media.maxMd} {\n    font-size: 10px;\n  }\n`\n\nconst PowerUpQuantity = styled.div`\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background: ${theme.colors.secondary[500]};\n  color: ${theme.colors.gray[900]};\n  border-radius: ${theme.borderRadius.full};\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: ${theme.fontSizes.xs};\n  font-weight: ${theme.fontWeights.bold};\n  box-shadow: ${theme.shadows.md};\n  \n  ${media.maxMd} {\n    width: 18px;\n    height: 18px;\n    font-size: 10px;\n  }\n`\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${theme.spacing[2]};\n  margin-top: ${theme.spacing[2]};\n  \n  ${media.maxMd} {\n    flex-direction: column;\n    gap: ${theme.spacing[1]};\n  }\n`\n\nconst PowerUpDescription = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  color: ${theme.colors.text.primary};\n  padding: ${theme.spacing[3]};\n  border-radius: ${theme.borderRadius.lg};\n  font-size: ${theme.fontSizes.sm};\n  line-height: 1.4;\n  margin-top: ${theme.spacing[2]};\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  \n  ${media.maxMd} {\n    padding: ${theme.spacing[2]};\n    font-size: ${theme.fontSizes.xs};\n  }\n`\n\nconst PowerUpPanel: React.FC<PowerUpPanelProps> = ({\n  powerUps,\n  onUsePowerUp,\n  selectedPowerUp,\n  onSelectPowerUp,\n  disabled = false\n}) => {\n  // const [showDescription, setShowDescription] = useState<PowerUpType | null>(null)\n\n  const handlePowerUpClick = (type: PowerUpType) => {\n    if (disabled) return\n    \n    if (selectedPowerUp === type) {\n      onSelectPowerUp(null)\n    } else {\n      onSelectPowerUp(type)\n      // setShowDescription(type)\n    }\n  }\n\n  const handleUsePowerUp = () => {\n    if (selectedPowerUp && !disabled) {\n      onUsePowerUp(selectedPowerUp)\n      onSelectPowerUp(null)\n      // setShowDescription(null)\n    }\n  }\n\n  const handleCancel = () => {\n    onSelectPowerUp(null)\n    // setShowDescription(null)\n  }\n\n  const getAvailablePowerUps = () => {\n    return powerUps.filter(p => p.quantity > 0)\n  }\n\n  const selectedPowerUpInfo = selectedPowerUp ? POWER_UPS[selectedPowerUp] : null\n\n  return (\n    <Container>\n      <Title>道具</Title>\n      \n      <PowerUpGrid>\n        {getAvailablePowerUps().map((powerUpInstance) => {\n          const powerUp = POWER_UPS[powerUpInstance.type]\n          const isSelected = selectedPowerUp === powerUpInstance.type\n          const isDisabled = disabled || powerUpInstance.quantity === 0\n          \n          return (\n            <PowerUpCard\n              key={powerUpInstance.type}\n              $selected={isSelected}\n              $disabled={isDisabled}\n              $rarity={powerUp.rarity}\n              onClick={() => handlePowerUpClick(powerUpInstance.type)}\n              hoverable={!isDisabled}\n            >\n              <PowerUpIcon>{powerUp.icon}</PowerUpIcon>\n              <PowerUpName>{powerUp.name.split(' ')[1]}</PowerUpName>\n              <PowerUpQuantity>{powerUpInstance.quantity}</PowerUpQuantity>\n            </PowerUpCard>\n          )\n        })}\n      </PowerUpGrid>\n\n      {selectedPowerUpInfo && (\n        <>\n          <PowerUpDescription>\n            <strong>{selectedPowerUpInfo.name}</strong>\n            <br />\n            {selectedPowerUpInfo.description}\n          </PowerUpDescription>\n          \n          <ActionButtons>\n            <Button\n              variant=\"primary\"\n              size=\"sm\"\n              onClick={handleUsePowerUp}\n              disabled={disabled}\n              fullWidth\n            >\n              使用道具\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleCancel}\n              fullWidth\n            >\n              取消\n            </Button>\n          </ActionButtons>\n        </>\n      )}\n      \n      {getAvailablePowerUps().length === 0 && (\n        <PowerUpDescription>\n          暂无可用道具\n        </PowerUpDescription>\n      )}\n    </Container>\n  )\n}\n\nexport default PowerUpPanel\n"], "names": ["Container", "styled", "theme", "media", "Title", "PowerUpGrid", "PowerUpCard", "Card", "props", "rarityConfig", "RARITY_CONFIG", "PowerUpIcon", "PowerUpName", "PowerUpQuantity", "ActionButtons", "PowerUpDescription", "PowerUpPanel", "powerUps", "onUsePowerUp", "selectedPowerUp", "onSelectPowerUp", "disabled", "handlePowerUpClick", "type", "handleUsePowerUp", "handleCancel", "getAvailablePowerUps", "p", "selectedPowerUpInfo", "POWER_UPS", "jsx", "powerUpInstance", "powerUp", "isSelected", "isDisabled", "jsxs", "Fragment", "<PERSON><PERSON>"], "mappings": "uRAeA,MAAMA,EAAYC,EAAO;AAAA;AAAA;AAAA,SAGhBC,EAAM,QAAQ,CAAC,CAAC;AAAA,aACZA,EAAM,QAAQ,CAAC,CAAC;AAAA,gBACbA,EAAM,OAAO,WAAW,IAAI;AAAA,mBACzBA,EAAM,aAAa,EAAE;AAAA;AAAA;AAAA;AAAA,IAIpCC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA,WACpBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBE,EAAQH,EAAO;AAAA,WACVC,EAAM,OAAO,KAAK,OAAO;AAAA,eACrBA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,QAAQ;AAAA;AAAA;AAAA;AAAA,IAIvCC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,IAAI;AAAA;AAAA,EAI/BG,EAAcJ,EAAO;AAAA;AAAA;AAAA,SAGlBC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAErBC,EAAM,KAAK;AAAA;AAAA,WAEJD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBI,EAAcL,EAAOM,CAAI;AAAA,aAKlBL,EAAM,QAAQ,CAAC,CAAC;AAAA,YACjBM,GAASA,EAAM,UAAY,cAAgB,SAAS;AAAA;AAAA,oBAE5CN,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA;AAAA,IAItCM,GAAS,CACT,MAAMC,EAAeC,EAAcF,EAAM,OAAqC,EAC9E,MAAO;AAAA,0BACeA,EAAM,UAAYC,EAAa,MAAQA,EAAa,WAAW;AAAA,oBACrED,EAAM,UAAYC,EAAa,QAAU,2BAA2B;AAAA,KAEtF,CAAC;AAAA;AAAA,IAECD,GAASA,EAAM,WAAa;AAAA;AAAA;AAAA,GAG7B;AAAA;AAAA,IAECA,GAAS,CAACA,EAAM,WAAa;AAAA;AAAA;AAAA,oBAGbN,EAAM,QAAQ,EAAE;AAAA,sBACdQ,EAAcF,EAAM,OAAqC,EAAE,KAAK;AAAA;AAAA,GAEnF;AAAA;AAAA,IAECL,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,EAKzBS,EAAcV,EAAO;AAAA;AAAA,mBAERC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE/BC,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTS,EAAcX,EAAO;AAAA,eACZC,EAAM,UAAU,EAAE;AAAA,WACtBA,EAAM,OAAO,KAAK,SAAS;AAAA,iBACrBA,EAAM,YAAY,MAAM;AAAA;AAAA;AAAA,IAGrCC,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTU,EAAkBZ,EAAO;AAAA;AAAA;AAAA;AAAA,gBAIfC,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA,WAChCA,EAAM,OAAO,KAAK,GAAG,CAAC;AAAA,mBACdA,EAAM,aAAa,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAM3BA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,IAAI;AAAA,gBACvBA,EAAM,QAAQ,EAAE;AAAA;AAAA,IAE5BC,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOTW,EAAgBb,EAAO;AAAA;AAAA,SAEpBC,EAAM,QAAQ,CAAC,CAAC;AAAA,gBACTA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE5BC,EAAM,KAAK;AAAA;AAAA,WAEJD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBa,EAAqBd,EAAO;AAAA;AAAA,WAEvBC,EAAM,OAAO,KAAK,OAAO;AAAA,aACvBA,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACVA,EAAM,aAAa,EAAE;AAAA,eACzBA,EAAM,UAAU,EAAE;AAAA;AAAA,gBAEjBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG5BC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA,iBACdA,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7Bc,EAA4C,CAAC,CACjD,SAAAC,EACA,aAAAC,EACA,gBAAAC,EACA,gBAAAC,EACA,SAAAC,EAAW,EACb,IAAM,CAGJ,MAAMC,EAAsBC,GAAsB,CAC5CF,GAGFD,EADED,IAAoBI,EACN,KAEAA,CAFI,CAKxB,EAEMC,EAAmB,IAAM,CACzBL,GAAmB,CAACE,IACtBH,EAAaC,CAAe,EAC5BC,EAAgB,IAAI,EAGxB,EAEMK,EAAe,IAAM,CACzBL,EAAgB,IAAI,CAEtB,EAEMM,EAAuB,IACpBT,EAAS,OAAOU,GAAKA,EAAE,SAAW,CAAC,EAGtCC,EAAsBT,EAAkBU,EAAUV,CAAe,EAAI,KAE3E,cACGnB,EAAA,CACC,SAAA,CAAA8B,EAAAA,IAAC1B,GAAM,SAAA,IAAA,CAAE,QAERC,EAAA,CACE,SAAAqB,EAAA,EAAuB,IAAKK,GAAoB,CAC/C,MAAMC,EAAUH,EAAUE,EAAgB,IAAI,EACxCE,EAAad,IAAoBY,EAAgB,KACjDG,EAAab,GAAYU,EAAgB,WAAa,EAE5D,OACEI,EAAAA,KAAC7B,EAAA,CAEC,UAAW2B,EACX,UAAWC,EACX,QAASF,EAAQ,OACjB,QAAS,IAAMV,EAAmBS,EAAgB,IAAI,EACtD,UAAW,CAACG,EAEZ,SAAA,CAAAJ,EAAAA,IAACnB,EAAA,CAAa,WAAQ,IAAA,CAAK,EAC3BmB,MAAClB,GAAa,SAAAoB,EAAQ,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,EACzCF,EAAAA,IAACjB,EAAA,CAAiB,SAAAkB,EAAgB,QAAA,CAAS,CAAA,CAAA,EATtCA,EAAgB,IAAA,CAY3B,CAAC,CAAA,CACH,EAECH,GACCO,EAAAA,KAAAC,WAAA,CACE,SAAA,CAAAD,OAACpB,EAAA,CACC,SAAA,CAAAe,EAAAA,IAAC,SAAA,CAAQ,WAAoB,IAAA,CAAK,QACjC,KAAA,EAAG,EACHF,EAAoB,WAAA,EACvB,SAECd,EAAA,CACC,SAAA,CAAAgB,EAAAA,IAACO,EAAA,CACC,QAAQ,UACR,KAAK,KACL,QAASb,EACT,SAAAH,EACA,UAAS,GACV,SAAA,MAAA,CAAA,EAGDS,EAAAA,IAACO,EAAA,CACC,QAAQ,QACR,KAAK,KACL,QAASZ,EACT,UAAS,GACV,SAAA,IAAA,CAAA,CAED,CAAA,CACF,CAAA,EACF,EAGDC,IAAuB,SAAW,GACjCI,EAAAA,IAACf,GAAmB,SAAA,QAAA,CAEpB,CAAA,EAEJ,CAEJ"}