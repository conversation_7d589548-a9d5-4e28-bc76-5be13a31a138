import{b as l,j as r}from"./index-CNCEp3EQ.js";import{d as i}from"./ui-ldAE8JkK.js";import"./vendor-Dneogk0_.js";import"./router-DMCr7QLp.js";const n=i.div`
  flex: 1;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
`,o=i.h1`
  color: white;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
`,s=i.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
`,d=i.div`
  color: white;
  text-align: center;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  p {
    margin: 0.5rem 0;
    opacity: 0.9;
  }
`,c=i.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
`,t=i.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  h3 {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
  }
`,x=i.button`
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #4834d4, #686de0);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 2rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
`,j=()=>{const{user:e,logout:a}=l();return e?r.jsxs(n,{children:[r.jsx(o,{children:"个人资料"}),r.jsxs(s,{children:[r.jsxs(d,{children:[r.jsx("h2",{children:e.username}),r.jsxs("p",{children:["邮箱：",e.email]}),r.jsxs("p",{children:["注册时间：",new Date(e.createdAt).toLocaleDateString()]}),r.jsxs("p",{children:["最后登录：",new Date(e.lastLoginAt).toLocaleDateString()]})]}),r.jsxs(c,{children:[r.jsxs(t,{children:[r.jsx("h3",{children:e.level}),r.jsx("p",{children:"当前等级"})]}),r.jsxs(t,{children:[r.jsx("h3",{children:e.experience}),r.jsx("p",{children:"总经验值"})]}),r.jsxs(t,{children:[r.jsx("h3",{children:e.coins}),r.jsx("p",{children:"金币"})]}),r.jsxs(t,{children:[r.jsx("h3",{children:e.gems}),r.jsx("p",{children:"宝石"})]})]}),r.jsx("div",{style:{textAlign:"center"},children:r.jsx(x,{onClick:a,children:"退出登录"})})]})]}):r.jsxs(n,{children:[r.jsx(o,{children:"个人资料"}),r.jsx(s,{children:r.jsx(d,{children:r.jsx("p",{children:"请先登录"})})})]})};export{j as default};
//# sourceMappingURL=ProfilePage-CsLxkWtu.js.map
