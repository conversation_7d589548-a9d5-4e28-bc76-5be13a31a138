"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gameRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const uuid_1 = require("uuid");
const database_1 = require("../database/database");
const gameEngine_1 = require("../game/gameEngine");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.gameRoutes = router;
const db = database_1.Database.getInstance();
const gameSessions = new Map();
router.use(auth_1.authenticateToken);
const startGameSchema = joi_1.default.object({
    levelNumber: joi_1.default.number().integer().min(1).required()
});
router.post('/start', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = startGameSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const userId = req.user.id;
    const { levelNumber } = value;
    const level = await db.get('SELECT * FROM levels WHERE level_number = ? AND is_active = 1', [levelNumber]);
    if (!level) {
        throw (0, errorHandler_1.createError)(404, 'LEVEL_001', '关卡不存在');
    }
    if (levelNumber > 1) {
        const prevProgress = await db.get('SELECT is_completed FROM user_progress WHERE user_id = ? AND level_number = ?', [userId, levelNumber - 1]);
        if (!prevProgress?.is_completed) {
            throw (0, errorHandler_1.createError)(403, 'LEVEL_002', '关卡未解锁');
        }
    }
    const engine = new gameEngine_1.GameEngine(level.board_size);
    const sessionId = (0, uuid_1.v4)();
    gameSessions.set(sessionId, {
        userId,
        levelNumber,
        engine,
        score: 0,
        movesLeft: level.max_moves,
        targetScore: level.target_score,
        targetType: level.target_type,
        startTime: Date.now()
    });
    await db.run(`INSERT OR REPLACE INTO game_sessions 
     (id, user_id, level_number, session_data, moves_made, current_score, started_at, is_active)
     VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)`, [sessionId, userId, levelNumber, JSON.stringify({
            board: engine.getBoard(),
            movesLeft: level.max_moves,
            targetScore: level.target_score,
            targetType: level.target_type
        }), 0, 0]);
    res.json({
        success: true,
        data: {
            sessionId,
            board: engine.getBoard(),
            level: {
                levelNumber,
                boardSize: level.board_size,
                maxMoves: level.max_moves,
                targetScore: level.target_score,
                targetType: level.target_type,
                targetData: level.target_data ? JSON.parse(level.target_data) : null
            },
            gameState: {
                score: 0,
                movesLeft: level.max_moves,
                isGameOver: false,
                isWin: false
            }
        },
        message: '游戏开始'
    });
}));
const moveSchema = joi_1.default.object({
    sessionId: joi_1.default.string().uuid().required(),
    from: joi_1.default.object({
        x: joi_1.default.number().integer().min(0).required(),
        y: joi_1.default.number().integer().min(0).required()
    }).required(),
    to: joi_1.default.object({
        x: joi_1.default.number().integer().min(0).required(),
        y: joi_1.default.number().integer().min(0).required()
    }).required()
});
router.post('/move', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = moveSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const userId = req.user.id;
    const { sessionId, from, to } = value;
    const session = gameSessions.get(sessionId);
    if (!session || session.userId !== userId) {
        throw (0, errorHandler_1.createError)(404, 'GAME_001', '无效的游戏会话');
    }
    if (session.movesLeft <= 0) {
        throw (0, errorHandler_1.createError)(400, 'GAME_002', '没有剩余步数');
    }
    const moveSuccess = session.engine.swapGems(from, to);
    if (!moveSuccess) {
        throw (0, errorHandler_1.createError)(400, 'GAME_003', '无效的移动');
    }
    const cascadeResult = session.engine.processCascade();
    session.score += cascadeResult.totalScore;
    session.movesLeft--;
    let isGameOver = false;
    let isWin = false;
    if (session.targetType === 'score' && session.score >= session.targetScore) {
        isWin = true;
        isGameOver = true;
    }
    else if (session.movesLeft <= 0) {
        isGameOver = true;
        isWin = session.score >= session.targetScore;
    }
    await db.run(`UPDATE game_sessions 
     SET session_data = ?, moves_made = ?, current_score = ?, updated_at = CURRENT_TIMESTAMP
     WHERE id = ?`, [JSON.stringify({
            board: session.engine.getBoard(),
            movesLeft: session.movesLeft,
            score: session.score
        }), session.levelNumber - session.movesLeft, session.score, sessionId]);
    res.json({
        success: true,
        data: {
            board: session.engine.getBoard(),
            score: session.score,
            movesLeft: session.movesLeft,
            matches: cascadeResult.matches,
            scoreGained: cascadeResult.totalScore,
            isGameOver,
            isWin,
            gameState: {
                targetScore: session.targetScore,
                targetType: session.targetType
            }
        }
    });
}));
const useItemSchema = joi_1.default.object({
    sessionId: joi_1.default.string().uuid().required(),
    itemCode: joi_1.default.string().required(),
    position: joi_1.default.object({
        x: joi_1.default.number().integer().min(0).required(),
        y: joi_1.default.number().integer().min(0).required()
    }).optional()
});
router.post('/use-item', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = useItemSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const userId = req.user.id;
    const { sessionId, itemCode, position } = value;
    const session = gameSessions.get(sessionId);
    if (!session || session.userId !== userId) {
        throw (0, errorHandler_1.createError)(404, 'GAME_001', '无效的游戏会话');
    }
    const inventory = await db.get('SELECT quantity FROM user_inventory WHERE user_id = ? AND item_code = ?', [userId, itemCode]);
    if (!inventory || inventory.quantity <= 0) {
        throw (0, errorHandler_1.createError)(400, 'ITEM_001', '道具不足');
    }
    const item = await db.get('SELECT * FROM items WHERE item_code = ? AND is_active = 1', [itemCode]);
    if (!item) {
        throw (0, errorHandler_1.createError)(404, 'ITEM_002', '道具不存在');
    }
    const effectData = JSON.parse(item.effect_data);
    let scoreGained = 0;
    switch (effectData.type) {
        case 'single_destroy':
            if (position) {
                session.engine.getBoard()[position.y][position.x] = 0;
                scoreGained = 50;
            }
            break;
        case 'add_moves':
            session.movesLeft += effectData.value || 5;
            break;
        case 'color_clear':
            if (position) {
                const targetColor = session.engine.getBoard()[position.y][position.x];
                const board = session.engine.getBoard();
                let cleared = 0;
                for (let y = 0; y < board.length; y++) {
                    for (let x = 0; x < board[y].length; x++) {
                        if (board[y][x] === targetColor) {
                            board[y][x] = 0;
                            cleared++;
                        }
                    }
                }
                session.engine.setBoard(board);
                scoreGained = cleared * 20;
            }
            break;
    }
    await db.run('UPDATE user_inventory SET quantity = quantity - 1 WHERE user_id = ? AND item_code = ?', [userId, itemCode]);
    session.engine.applyGravity();
    session.engine.fillEmpty();
    const cascadeResult = session.engine.processCascade();
    session.score += cascadeResult.totalScore + scoreGained;
    res.json({
        success: true,
        data: {
            board: session.engine.getBoard(),
            score: session.score,
            movesLeft: session.movesLeft,
            scoreGained: cascadeResult.totalScore + scoreGained,
            matches: cascadeResult.matches
        },
        message: '道具使用成功'
    });
}));
//# sourceMappingURL=game.js.map