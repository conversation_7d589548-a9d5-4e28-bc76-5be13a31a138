// 前端游戏引擎工具类
export enum GemType {
  EMPTY = 0,
  RED = 1,
  BLUE = 2,
  GREEN = 3,
  YELLOW = 4,
  PURPLE = 5,
  ORANGE = 6,
}

export interface Position {
  row: number
  col: number
}

export interface Match {
  positions: Position[]
  type: 'horizontal' | 'vertical' | 'L' | 'T'
  score: number
}

export interface MoveResult {
  isValid: boolean
  matches: Match[]
  newBoard: number[][]
  score: number
  cascade: boolean
}

export class GameEngine {
  private board: number[][]
  private size: number

  constructor(board: number[][], size: number = 6) {
    this.board = board.map(row => [...row]) // 深拷贝
    this.size = size
  }

  // 检查位置是否有效
  private isValidPosition(row: number, col: number): boolean {
    return row >= 0 && row < this.size && col >= 0 && col < this.size
  }

  // 检查两个位置是否相邻
  public areAdjacent(pos1: Position, pos2: Position): boolean {
    const rowDiff = Math.abs(pos1.row - pos2.row)
    const colDiff = Math.abs(pos1.col - pos2.col)
    return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)
  }

  // 获取当前棋盘状态
  public getBoard(): number[][] {
    return this.board.map(row => [...row])
  }

  // 交换两个位置的宝石
  private swapGems(pos1: Position, pos2: Position): void {
    const temp = this.board[pos1.row][pos1.col]
    this.board[pos1.row][pos1.col] = this.board[pos2.row][pos2.col]
    this.board[pos2.row][pos2.col] = temp
  }

  // 检查水平匹配
  private findHorizontalMatches(): Match[] {
    const matches: Match[] = []
    
    for (let row = 0; row < this.size; row++) {
      let count = 1
      let currentGem = this.board[row][0]
      
      for (let col = 1; col < this.size; col++) {
        if (this.board[row][col] === currentGem && currentGem !== GemType.EMPTY) {
          count++
        } else {
          if (count >= 3) {
            const positions: Position[] = []
            for (let i = col - count; i < col; i++) {
              positions.push({ row, col: i })
            }
            matches.push({
              positions,
              type: 'horizontal',
              score: count * 10
            })
          }
          count = 1
          currentGem = this.board[row][col]
        }
      }
      
      // 检查行末尾的匹配
      if (count >= 3) {
        const positions: Position[] = []
        for (let i = this.size - count; i < this.size; i++) {
          positions.push({ row, col: i })
        }
        matches.push({
          positions,
          type: 'horizontal',
          score: count * 10
        })
      }
    }
    
    return matches
  }

  // 检查垂直匹配
  private findVerticalMatches(): Match[] {
    const matches: Match[] = []
    
    for (let col = 0; col < this.size; col++) {
      let count = 1
      let currentGem = this.board[0][col]
      
      for (let row = 1; row < this.size; row++) {
        if (this.board[row][col] === currentGem && currentGem !== GemType.EMPTY) {
          count++
        } else {
          if (count >= 3) {
            const positions: Position[] = []
            for (let i = row - count; i < row; i++) {
              positions.push({ row: i, col })
            }
            matches.push({
              positions,
              type: 'vertical',
              score: count * 10
            })
          }
          count = 1
          currentGem = this.board[row][col]
        }
      }
      
      // 检查列末尾的匹配
      if (count >= 3) {
        const positions: Position[] = []
        for (let i = this.size - count; i < this.size; i++) {
          positions.push({ row: i, col })
        }
        matches.push({
          positions,
          type: 'vertical',
          score: count * 10
        })
      }
    }
    
    return matches
  }

  // 生成新的游戏板
  public generateBoard(size: number = 6): number[][] {
    const board: number[][] = []
    for (let row = 0; row < size; row++) {
      board[row] = []
      for (let col = 0; col < size; col++) {
        board[row][col] = Math.floor(Math.random() * 6) + 1
      }
    }
    return board
  }

  // 检查移动是否有效
  public isValidMove(board: number[][], from: Position, to: Position): boolean {
    // 检查位置是否有效
    if (!this.isValidPosition(from.row, from.col) || !this.isValidPosition(to.row, to.col)) {
      return false
    }

    // 检查是否相邻
    if (!this.areAdjacent(from, to)) {
      return false
    }

    // 模拟交换并检查是否产生匹配
    const tempBoard = board.map(row => [...row])
    const temp = tempBoard[from.row][from.col]
    tempBoard[from.row][from.col] = tempBoard[to.row][to.col]
    tempBoard[to.row][to.col] = temp

    // 临时设置board来检查匹配
    const originalBoard = this.board
    this.board = tempBoard
    const matches = this.findMatches()
    this.board = originalBoard

    return matches.length > 0
  }

  // 查找所有匹配
  public findMatches(): Match[] {
    const horizontalMatches = this.findHorizontalMatches()
    const verticalMatches = this.findVerticalMatches()
    return [...horizontalMatches, ...verticalMatches]
  }

  // 移除匹配的宝石
  private removeMatches(matches: Match[]): number {
    let totalScore = 0
    const toRemove = new Set<string>()
    
    matches.forEach(match => {
      totalScore += match.score
      match.positions.forEach(pos => {
        toRemove.add(`${pos.row},${pos.col}`)
      })
    })
    
    toRemove.forEach(posStr => {
      const [row, col] = posStr.split(',').map(Number)
      this.board[row][col] = GemType.EMPTY
    })
    
    return totalScore
  }

  // 应用重力，让宝石下落
  private applyGravity(): boolean {
    let hasChanges = false
    
    for (let col = 0; col < this.size; col++) {
      // 从底部开始，将非空宝石向下移动
      let writePos = this.size - 1
      
      for (let row = this.size - 1; row >= 0; row--) {
        if (this.board[row][col] !== GemType.EMPTY) {
          if (row !== writePos) {
            this.board[writePos][col] = this.board[row][col]
            this.board[row][col] = GemType.EMPTY
            hasChanges = true
          }
          writePos--
        }
      }
    }
    
    return hasChanges
  }

  // 填充空位置
  private fillEmptySpaces(): void {
    for (let col = 0; col < this.size; col++) {
      for (let row = 0; row < this.size; row++) {
        if (this.board[row][col] === GemType.EMPTY) {
          // 生成随机宝石类型 (1-6)
          this.board[row][col] = Math.floor(Math.random() * 6) + 1
        }
      }
    }
  }

  // 执行移动
  public makeMove(from: Position, to: Position): MoveResult {
    // 检查位置有效性
    if (!this.isValidPosition(from.row, from.col) || !this.isValidPosition(to.row, to.col)) {
      return {
        isValid: false,
        matches: [],
        newBoard: this.board,
        score: 0,
        cascade: false
      }
    }

    // 检查是否相邻
    if (!this.areAdjacent(from, to)) {
      return {
        isValid: false,
        matches: [],
        newBoard: this.board,
        score: 0,
        cascade: false
      }
    }

    // 临时交换
    this.swapGems(from, to)
    
    // 检查是否产生匹配
    const matches = this.findMatches()
    
    if (matches.length === 0) {
      // 没有匹配，撤销交换
      this.swapGems(from, to)
      return {
        isValid: false,
        matches: [],
        newBoard: this.board,
        score: 0,
        cascade: false
      }
    }

    // 处理连锁反应
    let totalScore = 0
    let allMatches: Match[] = []
    let hasCascade = false

    while (matches.length > 0) {
      allMatches.push(...matches)
      totalScore += this.removeMatches(matches)
      
      // 应用重力
      this.applyGravity()
      
      // 填充空位
      this.fillEmptySpaces()
      
      // 检查新的匹配
      const newMatches = this.findMatches()
      if (newMatches.length > 0) {
        hasCascade = true
        matches.splice(0, matches.length, ...newMatches)
      } else {
        break
      }
    }

    return {
      isValid: true,
      matches: allMatches,
      newBoard: this.board.map(row => [...row]),
      score: totalScore,
      cascade: hasCascade
    }
  }



  // 检查是否有可能的移动
  public hasPossibleMoves(): boolean {
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        // 检查右边的交换
        if (col < this.size - 1) {
          this.swapGems({ row, col }, { row, col: col + 1 })
          if (this.findMatches().length > 0) {
            this.swapGems({ row, col }, { row, col: col + 1 }) // 撤销
            return true
          }
          this.swapGems({ row, col }, { row, col: col + 1 }) // 撤销
        }

        // 检查下面的交换
        if (row < this.size - 1) {
          this.swapGems({ row, col }, { row: row + 1, col })
          if (this.findMatches().length > 0) {
            this.swapGems({ row, col }, { row: row + 1, col }) // 撤销
            return true
          }
          this.swapGems({ row, col }, { row: row + 1, col }) // 撤销
        }
      }
    }
    return false
  }

  // 生成提示
  public getHint(): { from: Position; to: Position } | null {
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        // 检查右边的交换
        if (col < this.size - 1) {
          const from = { row, col }
          const to = { row, col: col + 1 }
          this.swapGems(from, to)
          if (this.findMatches().length > 0) {
            this.swapGems(from, to) // 撤销
            return { from, to }
          }
          this.swapGems(from, to) // 撤销
        }

        // 检查下面的交换
        if (row < this.size - 1) {
          const from = { row, col }
          const to = { row: row + 1, col }
          this.swapGems(from, to)
          if (this.findMatches().length > 0) {
            this.swapGems(from, to) // 撤销
            return { from, to }
          }
          this.swapGems(from, to) // 撤销
        }
      }
    }
    return null
  }

  // 计算匹配分数（考虑连击和匹配长度）
  public calculateScore(matches: Match[], comboMultiplier: number = 1): number {
    let totalScore = 0
    matches.forEach(match => {
      const baseScore = match.positions.length * 10
      const lengthBonus = Math.max(0, (match.positions.length - 3) * 5)
      totalScore += (baseScore + lengthBonus) * comboMultiplier
    })
    return totalScore
  }

  // 重新洗牌（当没有可能移动时）
  public shuffle(): void {
    const gems: number[] = []

    // 收集所有非空宝石
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        if (this.board[row][col] !== GemType.EMPTY) {
          gems.push(this.board[row][col])
        }
      }
    }

    // 洗牌
    for (let i = gems.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[gems[i], gems[j]] = [gems[j], gems[i]]
    }

    // 重新分配到棋盘
    let gemIndex = 0
    for (let row = 0; row < this.size; row++) {
      for (let col = 0; col < this.size; col++) {
        if (this.board[row][col] !== GemType.EMPTY) {
          this.board[row][col] = gems[gemIndex++]
        }
      }
    }
  }

  // 洗牌棋盘（返回新的棋盘）
  public shuffleBoard(board: number[][]): number[][] {
    const newBoard = board.map(row => [...row])
    const originalBoard = this.board
    this.board = newBoard
    this.shuffle()
    const result = this.board.map(row => [...row])
    this.board = originalBoard
    return result
  }

  // 应用重力（公共方法用于测试）
  public applyGravityPublic(): boolean {
    let changed = false
    for (let col = 0; col < this.size; col++) {
      let writeIndex = this.size - 1
      for (let row = this.size - 1; row >= 0; row--) {
        if (this.board[row][col] !== GemType.EMPTY) {
          if (writeIndex !== row) {
            this.board[writeIndex][col] = this.board[row][col]
            this.board[row][col] = GemType.EMPTY
            changed = true
          }
          writeIndex--
        }
      }
    }
    return changed
  }

  // 获取提示（带参数版本用于测试）
  public getHintWithBoard(board?: number[][]): { from: Position; to: Position } | null {
    if (board) {
      const originalBoard = this.board
      this.board = board
      const result = this.getHint()
      this.board = originalBoard
      return result
    }
    return this.getHint()
  }
}
