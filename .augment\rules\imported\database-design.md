---
type: "agent_requested"
description: "Example description"
---
# 数据库设计文档

## 数据库表结构

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(50),
    avatar_url VARCHAR(255),
    coins INTEGER DEFAULT 0,
    gems INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    total_score INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME
);
```

### 2. 关卡配置表 (levels)
```sql
CREATE TABLE levels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level_number INTEGER UNIQUE NOT NULL,
    board_size INTEGER DEFAULT 6, -- 6x6 or 8x8
    max_moves INTEGER NOT NULL,
    target_score INTEGER,
    target_type VARCHAR(20) NOT NULL, -- 'score', 'collect', 'clear'
    target_data TEXT, -- JSON格式的目标数据
    obstacles TEXT, -- JSON格式的障碍物布局
    special_gems TEXT, -- JSON格式的特殊宝石配置
    difficulty_level INTEGER DEFAULT 1,
    reward_coins INTEGER DEFAULT 10,
    reward_items TEXT, -- JSON格式的奖励道具
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 道具定义表 (items)
```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- 'tool', 'booster', 'decoration'
    rarity VARCHAR(20) DEFAULT 'common', -- 'common', 'rare', 'epic', 'legendary'
    effect_data TEXT, -- JSON格式的效果数据
    price_coins INTEGER DEFAULT 0,
    price_gems INTEGER DEFAULT 0,
    max_stack INTEGER DEFAULT 99,
    icon_url VARCHAR(255),
    is_purchasable BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 用户进度表 (user_progress)
```sql
CREATE TABLE user_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    level_number INTEGER NOT NULL,
    best_score INTEGER DEFAULT 0,
    stars INTEGER DEFAULT 0, -- 1-3星评级
    moves_used INTEGER,
    completion_time INTEGER, -- 完成时间(秒)
    is_completed BOOLEAN DEFAULT 0,
    first_completed_at DATETIME,
    last_played_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    play_count INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(user_id, level_number)
);
```

### 5. 用户背包表 (user_inventory)
```sql
CREATE TABLE user_inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    item_code VARCHAR(50) NOT NULL,
    quantity INTEGER DEFAULT 0,
    acquired_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (item_code) REFERENCES items(item_code),
    UNIQUE(user_id, item_code)
);
```

### 6. 游戏会话表 (game_sessions)
```sql
CREATE TABLE game_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    level_number INTEGER NOT NULL,
    session_data TEXT, -- JSON格式的游戏状态
    moves_made INTEGER DEFAULT 0,
    current_score INTEGER DEFAULT 0,
    items_used TEXT, -- JSON格式的已使用道具
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 7. 任务系统表 (quests)
```sql
CREATE TABLE quests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quest_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'achievement'
    target_type VARCHAR(50) NOT NULL, -- 'complete_levels', 'collect_gems', etc.
    target_value INTEGER NOT NULL,
    reward_coins INTEGER DEFAULT 0,
    reward_gems INTEGER DEFAULT 0,
    reward_items TEXT, -- JSON格式的奖励道具
    duration_hours INTEGER, -- 任务持续时间(小时)，NULL表示永久
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 8. 用户任务进度表 (user_quests)
```sql
CREATE TABLE user_quests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    quest_code VARCHAR(50) NOT NULL,
    current_progress INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT 0,
    is_claimed BOOLEAN DEFAULT 0,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    expires_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (quest_code) REFERENCES quests(quest_code),
    UNIQUE(user_id, quest_code)
);
```

## 索引设计

```sql
-- 用户相关索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

-- 进度相关索引
CREATE INDEX idx_user_progress_user_level ON user_progress(user_id, level_number);
CREATE INDEX idx_user_progress_level ON user_progress(level_number);

-- 背包相关索引
CREATE INDEX idx_user_inventory_user ON user_inventory(user_id);
CREATE INDEX idx_user_inventory_item ON user_inventory(item_code);

-- 会话相关索引
CREATE INDEX idx_game_sessions_user_active ON game_sessions(user_id, is_active);

-- 任务相关索引
CREATE INDEX idx_user_quests_user ON user_quests(user_id);
CREATE INDEX idx_user_quests_active ON user_quests(user_id, is_completed, expires_at);
```

## 初始数据

### 基础道具数据
```sql
INSERT INTO items (item_code, name, description, type, rarity, effect_data, price_coins, price_gems) VALUES
('hammer', '魔法锤', '消除单个宝石', 'tool', 'common', '{"type":"single_destroy"}', 100, 0),
('color_bomb', '颜色炸弹', '清除所有同色宝石', 'tool', 'rare', '{"type":"color_clear"}', 200, 1),
('rainbow_star', '彩虹星', '消除整行或整列', 'tool', 'epic', '{"type":"line_clear"}', 0, 3),
('extra_moves', '额外步数', '增加5步移动机会', 'booster', 'common', '{"type":"add_moves","value":5}', 150, 0),
('star_burst', '星爆冲击', '清空所有障碍物', 'tool', 'legendary', '{"type":"clear_obstacles"}', 0, 5);
```

### 基础关卡数据
```sql
INSERT INTO levels (level_number, board_size, max_moves, target_score, target_type, difficulty_level) VALUES
(1, 6, 20, 1000, 'score', 1),
(2, 6, 18, 1500, 'score', 1),
(3, 6, 16, 2000, 'score', 1),
(4, 6, 15, 2500, 'collect', 2),
(5, 6, 14, 3000, 'score', 2);
```
