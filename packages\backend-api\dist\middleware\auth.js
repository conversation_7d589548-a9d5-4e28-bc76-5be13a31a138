"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireAdmin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const errorHandler_1 = require("./errorHandler");
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return next((0, errorHandler_1.createError)(401, 'AUTH_001', '缺少认证令牌'));
    }
    try {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET未配置');
        }
        const decoded = jsonwebtoken_1.default.verify(token, secret);
        req.user = {
            id: decoded.id,
            username: decoded.username,
            isAdmin: decoded.isAdmin || false
        };
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            return next((0, errorHandler_1.createError)(401, 'AUTH_002', '认证令牌已过期'));
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            return next((0, errorHandler_1.createError)(401, 'AUTH_001', '无效的认证令牌'));
        }
        else {
            return next((0, errorHandler_1.createError)(500, 'INTERNAL_SERVER_ERROR', '认证服务错误'));
        }
    }
};
exports.authenticateToken = authenticateToken;
const requireAdmin = (req, res, next) => {
    if (!req.user?.isAdmin) {
        return next((0, errorHandler_1.createError)(403, 'FORBIDDEN', '需要管理员权限'));
    }
    next();
};
exports.requireAdmin = requireAdmin;
const optionalAuth = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return next();
    }
    try {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            return next();
        }
        const decoded = jsonwebtoken_1.default.verify(token, secret);
        req.user = {
            id: decoded.id,
            username: decoded.username,
            isAdmin: decoded.isAdmin || false
        };
    }
    catch (error) {
    }
    next();
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map