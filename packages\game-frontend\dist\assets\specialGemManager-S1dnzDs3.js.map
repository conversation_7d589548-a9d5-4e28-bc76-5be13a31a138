{"version": 3, "file": "specialGemManager-S1dnzDs3.js", "sources": ["../../src/utils/specialGemManager.ts"], "sourcesContent": ["import {\n  SpecialGemType,\n  SpecialGem,\n  // <PERSON>mbo<PERSON>hain,\n  ComboEffect,\n  // SPECIAL_GEM_CONDITIONS,\n  COMBO_MULTIPLIERS,\n  // SPECIAL_GEM_COMBOS,\n  COMBO_REWARDS\n} from '../types/specialGems'\n\nexport interface MatchResult {\n  positions: Array<{ row: number; col: number }>\n  type: 'horizontal' | 'vertical' | 'L' | 'T' | 'square' | 'cross'\n  color: number\n  length: number\n}\n\nexport interface SpecialGemCreationResult {\n  specialGem?: SpecialGem\n  shouldCreate: boolean\n  type?: SpecialGemType\n  position: { row: number; col: number }\n}\n\nexport class SpecialGemManager {\n  // private comboChains: Map<string, ComboChain> = new Map()\n  private currentComboCount = 0\n  private lastComboTime = 0\n  private comboTimeout = 2000 // 2秒内的连续消除算作连击\n\n  // 检查是否应该生成特殊宝石\n  public checkSpecialGemCreation(matches: MatchResult[]): SpecialGemCreationResult[] {\n    const results: SpecialGemCreationResult[] = []\n\n    for (const match of matches) {\n      const result = this.analyzeMatch(match)\n      if (result.shouldCreate) {\n        results.push(result)\n      }\n    }\n\n    return results\n  }\n\n  // 分析匹配模式\n  private analyzeMatch(match: MatchResult): SpecialGemCreationResult {\n    const { positions, type, length } = match\n    const centerPos = this.getCenterPosition(positions)\n\n    // 6个或更多宝石 - 彩虹宝石\n    if (length >= 6) {\n      return {\n        shouldCreate: true,\n        type: 'rainbow',\n        position: centerPos,\n        specialGem: this.createSpecialGem('rainbow', match.color, centerPos)\n      }\n    }\n\n    // 5个宝石的不同形状\n    if (length === 5) {\n      switch (type) {\n        case 'square':\n          return {\n            shouldCreate: true,\n            type: 'color_bomb',\n            position: centerPos,\n            specialGem: this.createSpecialGem('color_bomb', match.color, centerPos)\n          }\n        case 'cross':\n          return {\n            shouldCreate: true,\n            type: 'lightning',\n            position: centerPos,\n            specialGem: this.createSpecialGem('lightning', match.color, centerPos)\n          }\n        case 'L':\n        case 'T':\n          return {\n            shouldCreate: true,\n            type: 'bomb',\n            position: centerPos,\n            specialGem: this.createSpecialGem('bomb', match.color, centerPos)\n          }\n        default:\n          // 直线5个 - 根据方向决定\n          const isHorizontal = this.isHorizontalLine(positions)\n          return {\n            shouldCreate: true,\n            type: isHorizontal ? 'line_horizontal' : 'line_vertical',\n            position: centerPos,\n            specialGem: this.createSpecialGem(\n              isHorizontal ? 'line_horizontal' : 'line_vertical',\n              match.color,\n              centerPos\n            )\n          }\n      }\n    }\n\n    // 4个宝石 - 直线消除宝石\n    if (length === 4) {\n      const isHorizontal = this.isHorizontalLine(positions)\n      return {\n        shouldCreate: true,\n        type: isHorizontal ? 'line_horizontal' : 'line_vertical',\n        position: centerPos,\n        specialGem: this.createSpecialGem(\n          isHorizontal ? 'line_horizontal' : 'line_vertical',\n          match.color,\n          centerPos\n        )\n      }\n    }\n\n    return {\n      shouldCreate: false,\n      position: centerPos\n    }\n  }\n\n  // 创建特殊宝石\n  private createSpecialGem(type: SpecialGemType, color: number, position: { row: number; col: number }): SpecialGem {\n    return {\n      type,\n      color,\n      row: position.row,\n      col: position.col,\n      id: `special_${type}_${Date.now()}_${Math.random()}`\n    }\n  }\n\n  // 获取中心位置\n  private getCenterPosition(positions: Array<{ row: number; col: number }>): { row: number; col: number } {\n    const avgRow = Math.round(positions.reduce((sum, pos) => sum + pos.row, 0) / positions.length)\n    const avgCol = Math.round(positions.reduce((sum, pos) => sum + pos.col, 0) / positions.length)\n    return { row: avgRow, col: avgCol }\n  }\n\n  // 检查是否为横向直线\n  private isHorizontalLine(positions: Array<{ row: number; col: number }>): boolean {\n    const rows = new Set(positions.map(p => p.row))\n    return rows.size === 1\n  }\n\n  // 处理特殊宝石激活\n  public activateSpecialGem(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    switch (specialGem.type) {\n      case 'line_horizontal':\n        return this.activateHorizontalLine(specialGem, board)\n      case 'line_vertical':\n        return this.activateVerticalLine(specialGem, board)\n      case 'bomb':\n        return this.activateBomb(specialGem, board)\n      case 'color_bomb':\n        return this.activateColorBomb(specialGem, board)\n      case 'lightning':\n        return this.activateLightning(specialGem, board)\n      case 'rainbow':\n        return this.activateRainbow(specialGem, board)\n      default:\n        return []\n    }\n  }\n\n  // 横向消除\n  private activateHorizontalLine(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    const positions: Array<{ row: number; col: number }> = []\n    for (let col = 0; col < board[0].length; col++) {\n      positions.push({ row: specialGem.row, col })\n    }\n    return positions\n  }\n\n  // 纵向消除\n  private activateVerticalLine(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    const positions: Array<{ row: number; col: number }> = []\n    for (let row = 0; row < board.length; row++) {\n      positions.push({ row, col: specialGem.col })\n    }\n    return positions\n  }\n\n  // 炸弹消除 (3x3)\n  private activateBomb(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    const positions: Array<{ row: number; col: number }> = []\n    const { row, col } = specialGem\n    \n    for (let r = Math.max(0, row - 1); r <= Math.min(board.length - 1, row + 1); r++) {\n      for (let c = Math.max(0, col - 1); c <= Math.min(board[0].length - 1, col + 1); c++) {\n        positions.push({ row: r, col: c })\n      }\n    }\n    return positions\n  }\n\n  // 彩色炸弹 - 消除所有同色\n  private activateColorBomb(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    const positions: Array<{ row: number; col: number }> = []\n    const targetColor = specialGem.color\n    \n    for (let row = 0; row < board.length; row++) {\n      for (let col = 0; col < board[0].length; col++) {\n        if (board[row][col] === targetColor) {\n          positions.push({ row, col })\n        }\n      }\n    }\n    return positions\n  }\n\n  // 闪电 - 十字消除\n  private activateLightning(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {\n    const positions: Array<{ row: number; col: number }> = []\n    const { row, col } = specialGem\n    \n    // 横向\n    for (let c = 0; c < board[0].length; c++) {\n      positions.push({ row, col: c })\n    }\n    \n    // 纵向\n    for (let r = 0; r < board.length; r++) {\n      positions.push({ row: r, col })\n    }\n    \n    return positions\n  }\n\n  // 彩虹宝石 - 与目标宝石交换时的效果\n  private activateRainbow(_specialGem: SpecialGem, _board: number[][]): Array<{ row: number; col: number }> {\n    // 彩虹宝石的效果需要根据交换的目标宝石来决定\n    // 这里返回空数组，实际效果在交换时处理\n    return []\n  }\n\n  // 处理连击\n  public processCombo(eliminatedCount: number): ComboEffect | null {\n    const now = Date.now()\n    \n    // 检查是否在连击时间窗口内\n    if (now - this.lastComboTime <= this.comboTimeout) {\n      this.currentComboCount++\n    } else {\n      this.currentComboCount = 1\n    }\n    \n    this.lastComboTime = now\n    \n    if (this.currentComboCount >= 2) {\n      const multiplier = this.getComboMultiplier(this.currentComboCount)\n      const scoreBonus = eliminatedCount * 10 * multiplier\n      \n      return {\n        type: 'cascade_combo',\n        multiplier,\n        description: `${this.currentComboCount}连击！`,\n        scoreBonus\n      }\n    }\n    \n    return null\n  }\n\n  // 获取连击倍数\n  private getComboMultiplier(comboCount: number): number {\n    if (comboCount in COMBO_MULTIPLIERS) {\n      return COMBO_MULTIPLIERS[comboCount as keyof typeof COMBO_MULTIPLIERS]\n    }\n    return COMBO_MULTIPLIERS.max\n  }\n\n  // 重置连击\n  public resetCombo(): void {\n    this.currentComboCount = 0\n    this.lastComboTime = 0\n  }\n\n  // 获取当前连击数\n  public getCurrentCombo(): number {\n    return this.currentComboCount\n  }\n\n  // 检查连击奖励\n  public checkComboRewards(comboCount: number): any {\n    const milestones = COMBO_REWARDS.milestones\n    if (comboCount in milestones) {\n      return milestones[comboCount as keyof typeof milestones]\n    }\n    return null\n  }\n}\n"], "names": ["SpecialGemManager", "__publicField", "matches", "results", "match", "result", "positions", "type", "length", "centerPos", "isHorizontal", "color", "position", "avgRow", "sum", "pos", "avgCol", "p", "specialGem", "board", "col", "row", "r", "targetColor", "c", "_specialGem", "_board", "eliminatedCount", "now", "multiplier", "scoreBonus", "comboCount", "COMBO_MULTIPLIERS", "milestones", "COMBO_REWARDS"], "mappings": "oNAyBO,MAAMA,CAAkB,CAAxB,cAEGC,EAAA,yBAAoB,GACpBA,EAAA,qBAAgB,GAChBA,EAAA,oBAAe,KAGhB,wBAAwBC,EAAoD,CACjF,MAAMC,EAAsC,CAAA,EAE5C,UAAWC,KAASF,EAAS,CAC3B,MAAMG,EAAS,KAAK,aAAaD,CAAK,EAClCC,EAAO,cACTF,EAAQ,KAAKE,CAAM,CAEvB,CAEA,OAAOF,CACT,CAGQ,aAAaC,EAA8C,CACjE,KAAM,CAAE,UAAAE,EAAW,KAAAC,EAAM,OAAAC,CAAA,EAAWJ,EAC9BK,EAAY,KAAK,kBAAkBH,CAAS,EAGlD,GAAIE,GAAU,EACZ,MAAO,CACL,aAAc,GACd,KAAM,UACN,SAAUC,EACV,WAAY,KAAK,iBAAiB,UAAWL,EAAM,MAAOK,CAAS,CAAA,EAKvE,GAAID,IAAW,EACb,OAAQD,EAAA,CACN,IAAK,SACH,MAAO,CACL,aAAc,GACd,KAAM,aACN,SAAUE,EACV,WAAY,KAAK,iBAAiB,aAAcL,EAAM,MAAOK,CAAS,CAAA,EAE1E,IAAK,QACH,MAAO,CACL,aAAc,GACd,KAAM,YACN,SAAUA,EACV,WAAY,KAAK,iBAAiB,YAAaL,EAAM,MAAOK,CAAS,CAAA,EAEzE,IAAK,IACL,IAAK,IACH,MAAO,CACL,aAAc,GACd,KAAM,OACN,SAAUA,EACV,WAAY,KAAK,iBAAiB,OAAQL,EAAM,MAAOK,CAAS,CAAA,EAEpE,QAEE,MAAMC,EAAe,KAAK,iBAAiBJ,CAAS,EACpD,MAAO,CACL,aAAc,GACd,KAAMI,EAAe,kBAAoB,gBACzC,SAAUD,EACV,WAAY,KAAK,iBACfC,EAAe,kBAAoB,gBACnCN,EAAM,MACNK,CAAA,CACF,CACF,CAKN,GAAID,IAAW,EAAG,CAChB,MAAME,EAAe,KAAK,iBAAiBJ,CAAS,EACpD,MAAO,CACL,aAAc,GACd,KAAMI,EAAe,kBAAoB,gBACzC,SAAUD,EACV,WAAY,KAAK,iBACfC,EAAe,kBAAoB,gBACnCN,EAAM,MACNK,CAAA,CACF,CAEJ,CAEA,MAAO,CACL,aAAc,GACd,SAAUA,CAAA,CAEd,CAGQ,iBAAiBF,EAAsBI,EAAeC,EAAoD,CAChH,MAAO,CACL,KAAAL,EACA,MAAAI,EACA,IAAKC,EAAS,IACd,IAAKA,EAAS,IACd,GAAI,WAAWL,CAAI,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,EAAA,CAEtD,CAGQ,kBAAkBD,EAA8E,CACtG,MAAMO,EAAS,KAAK,MAAMP,EAAU,OAAO,CAACQ,EAAKC,IAAQD,EAAMC,EAAI,IAAK,CAAC,EAAIT,EAAU,MAAM,EACvFU,EAAS,KAAK,MAAMV,EAAU,OAAO,CAACQ,EAAKC,IAAQD,EAAMC,EAAI,IAAK,CAAC,EAAIT,EAAU,MAAM,EAC7F,MAAO,CAAE,IAAKO,EAAQ,IAAKG,CAAA,CAC7B,CAGQ,iBAAiBV,EAAyD,CAEhF,OADa,IAAI,IAAIA,EAAU,IAAIW,GAAKA,EAAE,GAAG,CAAC,EAClC,OAAS,CACvB,CAGO,mBAAmBC,EAAwBC,EAAwD,CACxG,OAAQD,EAAW,KAAA,CACjB,IAAK,kBACH,OAAO,KAAK,uBAAuBA,EAAYC,CAAK,EACtD,IAAK,gBACH,OAAO,KAAK,qBAAqBD,EAAYC,CAAK,EACpD,IAAK,OACH,OAAO,KAAK,aAAaD,EAAYC,CAAK,EAC5C,IAAK,aACH,OAAO,KAAK,kBAAkBD,EAAYC,CAAK,EACjD,IAAK,YACH,OAAO,KAAK,kBAAkBD,EAAYC,CAAK,EACjD,IAAK,UACH,OAAO,KAAK,gBAAgBD,EAAYC,CAAK,EAC/C,QACE,MAAO,CAAA,CAAC,CAEd,CAGQ,uBAAuBD,EAAwBC,EAAwD,CAC7G,MAAMb,EAAiD,CAAA,EACvD,QAASc,EAAM,EAAGA,EAAMD,EAAM,CAAC,EAAE,OAAQC,IACvCd,EAAU,KAAK,CAAE,IAAKY,EAAW,IAAK,IAAAE,EAAK,EAE7C,OAAOd,CACT,CAGQ,qBAAqBY,EAAwBC,EAAwD,CAC3G,MAAMb,EAAiD,CAAA,EACvD,QAASe,EAAM,EAAGA,EAAMF,EAAM,OAAQE,IACpCf,EAAU,KAAK,CAAE,IAAAe,EAAK,IAAKH,EAAW,IAAK,EAE7C,OAAOZ,CACT,CAGQ,aAAaY,EAAwBC,EAAwD,CACnG,MAAMb,EAAiD,CAAA,EACjD,CAAE,IAAAe,EAAK,IAAAD,CAAA,EAAQF,EAErB,QAASI,EAAI,KAAK,IAAI,EAAGD,EAAM,CAAC,EAAGC,GAAK,KAAK,IAAIH,EAAM,OAAS,EAAGE,EAAM,CAAC,EAAGC,IAC3E,QAAS,EAAI,KAAK,IAAI,EAAGF,EAAM,CAAC,EAAG,GAAK,KAAK,IAAID,EAAM,CAAC,EAAE,OAAS,EAAGC,EAAM,CAAC,EAAG,IAC9Ed,EAAU,KAAK,CAAE,IAAKgB,EAAG,IAAK,EAAG,EAGrC,OAAOhB,CACT,CAGQ,kBAAkBY,EAAwBC,EAAwD,CACxG,MAAMb,EAAiD,CAAA,EACjDiB,EAAcL,EAAW,MAE/B,QAASG,EAAM,EAAGA,EAAMF,EAAM,OAAQE,IACpC,QAASD,EAAM,EAAGA,EAAMD,EAAM,CAAC,EAAE,OAAQC,IACnCD,EAAME,CAAG,EAAED,CAAG,IAAMG,GACtBjB,EAAU,KAAK,CAAE,IAAAe,EAAK,IAAAD,CAAA,CAAK,EAIjC,OAAOd,CACT,CAGQ,kBAAkBY,EAAwBC,EAAwD,CACxG,MAAMb,EAAiD,CAAA,EACjD,CAAE,IAAAe,EAAK,IAAAD,CAAA,EAAQF,EAGrB,QAASM,EAAI,EAAGA,EAAIL,EAAM,CAAC,EAAE,OAAQK,IACnClB,EAAU,KAAK,CAAE,IAAAe,EAAK,IAAKG,EAAG,EAIhC,QAASF,EAAI,EAAGA,EAAIH,EAAM,OAAQG,IAChChB,EAAU,KAAK,CAAE,IAAKgB,EAAG,IAAAF,EAAK,EAGhC,OAAOd,CACT,CAGQ,gBAAgBmB,EAAyBC,EAAyD,CAGxG,MAAO,CAAA,CACT,CAGO,aAAaC,EAA6C,CAC/D,MAAMC,EAAM,KAAK,IAAA,EAWjB,GARIA,EAAM,KAAK,eAAiB,KAAK,aACnC,KAAK,oBAEL,KAAK,kBAAoB,EAG3B,KAAK,cAAgBA,EAEjB,KAAK,mBAAqB,EAAG,CAC/B,MAAMC,EAAa,KAAK,mBAAmB,KAAK,iBAAiB,EAC3DC,EAAaH,EAAkB,GAAKE,EAE1C,MAAO,CACL,KAAM,gBACN,WAAAA,EACA,YAAa,GAAG,KAAK,iBAAiB,MACtC,WAAAC,CAAA,CAEJ,CAEA,OAAO,IACT,CAGQ,mBAAmBC,EAA4B,CACrD,OAAIA,KAAcC,EACTA,EAAkBD,CAA4C,EAEhEC,EAAkB,GAC3B,CAGO,YAAmB,CACxB,KAAK,kBAAoB,EACzB,KAAK,cAAgB,CACvB,CAGO,iBAA0B,CAC/B,OAAO,KAAK,iBACd,CAGO,kBAAkBD,EAAyB,CAChD,MAAME,EAAaC,EAAc,WACjC,OAAIH,KAAcE,EACTA,EAAWF,CAAqC,EAElD,IACT,CACF"}