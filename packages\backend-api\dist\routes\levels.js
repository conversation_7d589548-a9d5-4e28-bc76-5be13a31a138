"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.levelRoutes = void 0;
const express_1 = require("express");
const database_1 = require("../database/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.levelRoutes = router;
const db = database_1.Database.getInstance();
router.use(auth_1.authenticateToken);
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;
    const totalResult = await db.get('SELECT COUNT(*) as count FROM levels WHERE is_active = 1');
    const total = totalResult?.count || 0;
    const levels = await db.all(`SELECT 
       l.id, l.level_number, l.board_size, l.max_moves, l.target_score, 
       l.target_type, l.target_data, l.difficulty_level, l.reward_coins,
       up.best_score, up.stars, up.is_completed, up.play_count
     FROM levels l
     LEFT JOIN user_progress up ON l.level_number = up.level_number AND up.user_id = ?
     WHERE l.is_active = 1
     ORDER BY l.level_number
     LIMIT ? OFFSET ?`, [userId, limit, offset]);
    const formattedLevels = levels.map(level => ({
        id: level.id,
        levelNumber: level.level_number,
        boardSize: level.board_size,
        maxMoves: level.max_moves,
        targetScore: level.target_score,
        targetType: level.target_type,
        targetData: level.target_data ? JSON.parse(level.target_data) : null,
        difficultyLevel: level.difficulty_level,
        rewardCoins: level.reward_coins,
        userProgress: {
            bestScore: level.best_score || 0,
            stars: level.stars || 0,
            isCompleted: Boolean(level.is_completed),
            playCount: level.play_count || 0
        }
    }));
    res.json({
        success: true,
        data: {
            levels: formattedLevels,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        }
    });
}));
router.get('/:levelNumber', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const levelNumber = parseInt(req.params.levelNumber);
    if (isNaN(levelNumber) || levelNumber < 1) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', '无效的关卡编号');
    }
    const level = await db.get(`SELECT * FROM levels WHERE level_number = ? AND is_active = 1`, [levelNumber]);
    if (!level) {
        throw (0, errorHandler_1.createError)(404, 'LEVEL_001', '关卡不存在');
    }
    const userProgress = await db.get(`SELECT * FROM user_progress WHERE user_id = ? AND level_number = ?`, [userId, levelNumber]);
    let isUnlocked = true;
    if (levelNumber > 1) {
        const prevProgress = await db.get(`SELECT is_completed FROM user_progress WHERE user_id = ? AND level_number = ?`, [userId, levelNumber - 1]);
        isUnlocked = Boolean(prevProgress?.is_completed);
    }
    res.json({
        success: true,
        data: {
            level: {
                id: level.id,
                levelNumber: level.level_number,
                boardSize: level.board_size,
                maxMoves: level.max_moves,
                targetScore: level.target_score,
                targetType: level.target_type,
                targetData: level.target_data ? JSON.parse(level.target_data) : null,
                obstacles: level.obstacles ? JSON.parse(level.obstacles) : [],
                specialGems: level.special_gems ? JSON.parse(level.special_gems) : [],
                difficultyLevel: level.difficulty_level,
                rewardCoins: level.reward_coins,
                rewardItems: level.reward_items ? JSON.parse(level.reward_items) : []
            },
            userProgress: {
                bestScore: userProgress?.best_score || 0,
                stars: userProgress?.stars || 0,
                movesUsed: userProgress?.moves_used || 0,
                completionTime: userProgress?.completion_time || 0,
                isCompleted: Boolean(userProgress?.is_completed),
                firstCompletedAt: userProgress?.first_completed_at,
                lastPlayedAt: userProgress?.last_played_at,
                playCount: userProgress?.play_count || 0
            },
            isUnlocked
        }
    });
}));
router.get('/:levelNumber/leaderboard', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const levelNumber = parseInt(req.params.levelNumber);
    const limit = Math.min(parseInt(req.query.limit) || 10, 50);
    if (isNaN(levelNumber) || levelNumber < 1) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', '无效的关卡编号');
    }
    const level = await db.get('SELECT id FROM levels WHERE level_number = ? AND is_active = 1', [levelNumber]);
    if (!level) {
        throw (0, errorHandler_1.createError)(404, 'LEVEL_001', '关卡不存在');
    }
    const leaderboard = await db.all(`SELECT 
       up.best_score, up.stars, up.moves_used, up.completion_time,
       u.username, u.display_name, u.avatar_url
     FROM user_progress up
     JOIN users u ON up.user_id = u.id
     WHERE up.level_number = ? AND up.is_completed = 1
     ORDER BY up.best_score DESC, up.completion_time ASC
     LIMIT ?`, [levelNumber, limit]);
    const formattedLeaderboard = leaderboard.map((entry, index) => ({
        rank: index + 1,
        username: entry.username,
        displayName: entry.display_name,
        avatarUrl: entry.avatar_url,
        bestScore: entry.best_score,
        stars: entry.stars,
        movesUsed: entry.moves_used,
        completionTime: entry.completion_time
    }));
    res.json({
        success: true,
        data: {
            levelNumber,
            leaderboard: formattedLeaderboard
        }
    });
}));
//# sourceMappingURL=levels.js.map