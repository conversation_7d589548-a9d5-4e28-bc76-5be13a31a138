var ne=Object.defineProperty;var oe=(t,r,s)=>r in t?ne(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s;var A=(t,r,s)=>oe(t,typeof r!="symbol"?r+"":r,s);import{j as f,t as b,m as U,u as ie,c as le}from"./index-CNCEp3EQ.js";import{r as i}from"./vendor-Dneogk0_.js";import{l as q,d as C,m as O}from"./ui-ldAE8JkK.js";import{AnimationManager as ce}from"./animationManager-DTsUvCq5.js";import{C as me}from"./specialGems-xbtj_zef.js";import"./router-DMCr7QLp.js";class fe{constructor(r){A(this,"metrics",[]);A(this,"lastFrameTime",0);A(this,"frameCount",0);A(this,"fpsUpdateInterval",1e3);A(this,"lastFpsUpdate",0);A(this,"currentFps",0);A(this,"isMonitoring",!1);A(this,"thresholds",{minFps:30,maxFrameTime:33.33,maxMemoryUsage:100*1024*1024,maxRenderTime:16.67});A(this,"onPerformanceIssue");this.onPerformanceIssue=r}startMonitoring(){this.isMonitoring=!0,this.lastFrameTime=performance.now(),this.lastFpsUpdate=performance.now(),this.frameCount=0}stopMonitoring(){this.isMonitoring=!1}recordFrame(r=0,s=0){if(!this.isMonitoring)return;const d=performance.now(),u=d-this.lastFrameTime;this.lastFrameTime=d,this.frameCount++,d-this.lastFpsUpdate>=this.fpsUpdateInterval&&(this.currentFps=this.frameCount*1e3/(d-this.lastFpsUpdate),this.frameCount=0,this.lastFpsUpdate=d);const o=this.getMemoryUsage(),m={fps:this.currentFps,frameTime:u,memoryUsage:o,renderTime:r,updateTime:s,totalTime:r+s};this.metrics.push({timestamp:d,metrics:m}),this.metrics.length>1e3&&this.metrics.shift(),this.checkPerformanceIssues(m)}getMemoryUsage(){return"memory"in performance&&performance.memory.usedJSHeapSize||0}checkPerformanceIssues(r){const s=[];r.fps<this.thresholds.minFps&&s.push({type:"low_fps",severity:"warning",message:`FPS过低: ${r.fps.toFixed(1)}`,value:r.fps,threshold:this.thresholds.minFps}),r.frameTime>this.thresholds.maxFrameTime&&s.push({type:"high_frame_time",severity:"warning",message:`帧时间过长: ${r.frameTime.toFixed(2)}ms`,value:r.frameTime,threshold:this.thresholds.maxFrameTime}),r.memoryUsage>this.thresholds.maxMemoryUsage&&s.push({type:"high_memory_usage",severity:"error",message:`内存使用过高: ${(r.memoryUsage/1024/1024).toFixed(1)}MB`,value:r.memoryUsage,threshold:this.thresholds.maxMemoryUsage}),r.renderTime>this.thresholds.maxRenderTime&&s.push({type:"high_render_time",severity:"warning",message:`渲染时间过长: ${r.renderTime.toFixed(2)}ms`,value:r.renderTime,threshold:this.thresholds.maxRenderTime}),s.forEach(d=>{this.onPerformanceIssue&&this.onPerformanceIssue(d)})}getAverageMetrics(r=5e3){const s=performance.now(),d=this.metrics.filter(m=>s-m.timestamp<=r);if(d.length===0)return null;const u=d.reduce((m,S)=>({fps:m.fps+S.metrics.fps,frameTime:m.frameTime+S.metrics.frameTime,memoryUsage:m.memoryUsage+S.metrics.memoryUsage,renderTime:m.renderTime+S.metrics.renderTime,updateTime:m.updateTime+S.metrics.updateTime,totalTime:m.totalTime+S.metrics.totalTime}),{fps:0,frameTime:0,memoryUsage:0,renderTime:0,updateTime:0,totalTime:0}),o=d.length;return{fps:u.fps/o,frameTime:u.frameTime/o,memoryUsage:u.memoryUsage/o,renderTime:u.renderTime/o,updateTime:u.updateTime/o,totalTime:u.totalTime/o}}getCurrentMetrics(){return this.metrics.length===0?null:this.metrics[this.metrics.length-1].metrics}getPerformanceReport(){const r=this.getAverageMetrics(5e3),s=this.getAverageMetrics(6e4);return{current:this.getCurrentMetrics(),recent:r,overall:s,recommendations:this.generateRecommendations(r)}}generateRecommendations(r){if(!r)return[];const s=[];return r.fps<30&&s.push("考虑降低游戏质量设置或优化渲染逻辑"),r.renderTime>16&&s.push("优化Canvas渲染，考虑使用离屏Canvas或减少绘制调用"),r.memoryUsage>50*1024*1024&&s.push("检查内存泄漏，及时清理不需要的对象"),r.frameTime>20&&s.push("优化游戏逻辑，考虑将复杂计算分帧处理"),s}clearMetrics(){this.metrics=[]}}const L=new fe(t=>{}),de=O`
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
`,ue=O`
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
`,he=O`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
`,pe=O`
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
`,ge=C.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: ${b.zIndex.modal};
  pointer-events: none;
  
  ${t=>t.$visible?q`
    animation: ${de} 0.6s ease-out;
  `:q`
    animation: ${ue} 0.3s ease-in;
    opacity: 0;
  `}
`,xe=C.div`
  font-size: ${t=>t.$size}px;
  font-weight: ${b.fontWeights.bold};
  color: ${t=>t.$color};
  text-align: center;
  text-shadow: 
    0 0 10px ${t=>t.$color}80,
    0 0 20px ${t=>t.$color}60,
    0 0 30px ${t=>t.$color}40;
  font-family: ${b.fonts.primary};
  letter-spacing: 2px;
  
  ${t=>t.$comboCount>=5&&q`
    animation: ${he} 0.5s ease-in-out infinite;
  `}
  
  ${U.maxMd} {
    font-size: ${t=>Math.max(20,t.$size*.7)}px;
  }
  
  ${U.maxSm} {
    font-size: ${t=>Math.max(16,t.$size*.5)}px;
  }
`,we=C.div`
  font-size: ${b.fontSizes.lg};
  color: ${b.colors.secondary[400]};
  text-align: center;
  margin-top: ${b.spacing[2]};
  font-weight: ${b.fontWeights.semibold};
  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  
  ${U.maxMd} {
    font-size: ${b.fontSizes.base};
  }
  
  ${U.maxSm} {
    font-size: ${b.fontSizes.sm};
  }
`,ve=C.div`
  font-size: ${b.fontSizes.xl};
  color: ${b.colors.success};
  text-align: center;
  margin-top: ${b.spacing[1]};
  font-weight: ${b.fontWeights.bold};
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  
  ${U.maxMd} {
    font-size: ${b.fontSizes.lg};
  }
  
  ${U.maxSm} {
    font-size: ${b.fontSizes.base};
  }
`,be=C.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  pointer-events: none;
`,ye=C.div`
  position: absolute;
  width: 8px;
  height: 8px;
  background: ${t=>t.$color};
  border-radius: 50%;
  left: ${t=>t.$x}%;
  top: ${t=>t.$y}%;
  animation: ${pe} 1s ease-in-out ${t=>t.$delay}s infinite;
  box-shadow: 0 0 10px ${t=>t.$color};
`,$e=({combo:t,comboCount:r,onComboEnd:s})=>{const[d,u]=i.useState(!1),[o,m]=i.useState([]);i.useEffect(()=>{if(t&&r>=2){u(!0);const h=Array.from({length:Math.min(r*2,20)},(B,R)=>({id:R,x:Math.random()*100,y:Math.random()*100,delay:Math.random()*.5,color:k(r)}));m(h);const F=setTimeout(()=>{u(!1),setTimeout(()=>{s==null||s()},300)},2e3);return()=>clearTimeout(F)}else u(!1)},[t,r,s]);const S=h=>{const F=me.text,B=Math.max(...Object.keys(F).map(Number));return h in F?F[h]:F[B]},k=h=>h>=10?"#ffd700":h>=8?"#ff6b6b":h>=6?"#9c27b0":h>=4?"#ff9800":h>=3?"#2196f3":"#4caf50";if(!t||r<2)return null;const E=S(r);return f.jsxs(ge,{$visible:d,children:[f.jsx(be,{children:o.map(h=>f.jsx(ye,{$delay:h.delay,$x:h.x,$y:h.y,$color:h.color},h.id))}),f.jsx(xe,{$comboCount:r,$color:E.color,$size:E.size,children:E.text}),f.jsxs(we,{children:[t.multiplier.toFixed(1),"x 倍数"]}),f.jsxs(ve,{children:["+",Math.round(t.scoreBonus)]})]})},Te=C.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 10px;
    gap: 15px;
  }

  @media (max-width: 480px) {
    padding: 5px;
    gap: 10px;
  }
`,Se=C.div`
  position: relative;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 10px;
  width: 100%;
  max-width: 480px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    padding: 8px;
    border-radius: 8px;
  }

  @media (max-width: 480px) {
    padding: 5px;
    border-radius: 6px;
    max-width: 90vw;
  }
`,Ce=C.canvas`
  border-radius: 8px;
  cursor: pointer;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  width: 100% !important;
  height: 100% !important;
  max-width: 100%;
  max-height: 100%;
  touch-action: none; /* 防止触摸时的默认行为 */
  user-select: none; /* 防止选择 */

  @media (max-width: 768px) {
    border-radius: 6px;
  }

  @media (max-width: 480px) {
    border-radius: 4px;
  }
`,Me=C.div`
  display: flex;
  gap: 30px;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 20px;
    font-size: 16px;
  }

  @media (max-width: 480px) {
    gap: 15px;
    font-size: 14px;
    justify-content: space-around;
  }
`,X=C.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-width: 80px;

  .label {
    font-size: 14px;
    opacity: 0.8;
  }

  .value {
    font-size: 24px;
    color: #fbbf24;
  }

  @media (max-width: 768px) {
    min-width: 70px;
    gap: 3px;

    .label {
      font-size: 12px;
    }

    .value {
      font-size: 20px;
    }
  }

  @media (max-width: 480px) {
    min-width: 60px;
    gap: 2px;

    .label {
      font-size: 11px;
    }

    .value {
      font-size: 18px;
    }
  }
`,Ae={1:"#ef4444",2:"#3b82f6",3:"#10b981",4:"#fbbf24",5:"#8b5cf6",6:"#f97316"},Ee=({size:t=6})=>{var J;const r=i.useRef(null),{gameState:s,makeMove:d}=ie(),u=le(),[o,m]=i.useState(null),[S,k]=i.useState(!1),[E,h]=i.useState(null),[F,B]=i.useState(0),[R,D]=i.useState(!0),_=i.useRef(null),[M]=i.useState(()=>window.devicePixelRatio||1),p=i.useRef(null),[l,Q]=i.useState(60),g=t*l;i.useEffect(()=>{const e=()=>{const n=window.innerWidth;let a=60;n<=480?a=Math.min(50,n*.9/t):n<=768&&(a=Math.min(55,n*.8/t)),Q(Math.max(30,a))};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[t]),i.useEffect(()=>(p.current||(p.current=new ce(()=>{})),()=>{var e;(e=p.current)==null||e.clear()}),[]);const I=i.useCallback((e,n,a,x,y=!1)=>{const $=a+l/2,w=x+l/2,v=l*.35;y&&(e.save(),e.shadowColor="#fbbf24",e.shadowBlur=20,e.beginPath(),e.arc($,w,v+5,0,Math.PI*2),e.fillStyle="rgba(251, 191, 36, 0.3)",e.fill(),e.restore()),e.save(),e.beginPath(),e.arc($,w,v,0,Math.PI*2);const P=e.createRadialGradient($-v*.3,w-v*.3,0,$,w,v),T=Ae[n]||"#666666";P.addColorStop(0,T),P.addColorStop(.7,T),P.addColorStop(1,"#000000"),e.fillStyle=P,e.fill(),e.beginPath(),e.arc($-v*.3,w-v*.3,v*.3,0,Math.PI*2),e.fillStyle="rgba(255, 255, 255, 0.4)",e.fill(),e.restore()},[l]),H=i.useCallback(()=>{if(!_.current){const e=document.createElement("canvas");e.width=g*M,e.height=g*M;const n=e.getContext("2d");n&&n.scale(M,M),_.current=e}return _.current},[g,M]),W=i.useCallback(e=>{e.strokeStyle="rgba(255, 255, 255, 0.1)",e.lineWidth=1,e.beginPath();for(let n=0;n<=t;n++)e.moveTo(n*l,0),e.lineTo(n*l,g),e.moveTo(0,n*l),e.lineTo(g,n*l);if(e.stroke(),s.board)for(let n=0;n<t;n++)for(let a=0;a<t;a++){const x=s.board[n][a];if(x>0){const y=(o==null?void 0:o.row)===n&&(o==null?void 0:o.col)===a;I(e,x,a*l,n*l,y)}}},[t,l,g,s.board,o,I]),Y=i.useCallback(()=>{var w,v,P;const e=performance.now(),n=r.current;if(!n)return;const a=n.getContext("2d");if(!a||!R&&!((w=p.current)!=null&&w.hasActiveAnimations()))return;a.clearRect(0,0,g,g);const x=(v=p.current)==null?void 0:v.hasActiveAnimations();if(!x&&R){const c=H().getContext("2d");c&&(c.clearRect(0,0,g,g),W(c)),D(!1)}if(_.current&&!x)a.drawImage(_.current,0,0);else if(W(a),x&&s.board)for(let T=0;T<t;T++)for(let c=0;c<t;c++){const j=s.board[T][c];if(j>0&&!((P=p.current)==null?void 0:P.getActiveAnimations().some(z=>z.from.row===T&&z.from.col===c))){const z=(o==null?void 0:o.row)===T&&(o==null?void 0:o.col)===c;I(a,j,c*l,T*l,z)}}p.current&&p.current.getActiveAnimations().forEach(c=>{const j=p.current.getInterpolatedPosition(c),V=j.col*l,z=j.row*l;if(c.type==="eliminate"){const Z=p.current.getAnimationProgress(c.id),K=1-Z,ae=1-Z;a.save(),a.globalAlpha=ae,a.translate(V+l/2,z+l/2),a.scale(K,K),a.translate(-l/2,-l/2),I(a,c.gemType,0,0),a.restore()}else I(a,c.gemType,V,z)}),p.current&&p.current.getParticles().forEach(c=>{const j=c.life/c.maxLife;a.save(),a.globalAlpha=j,a.fillStyle=c.color,a.beginPath(),a.arc(c.x,c.y,c.size,0,Math.PI*2),a.fill(),a.restore()});const $=performance.now()-e;L.recordFrame($,0)},[s.board,o,t,l,g,I,R,H,W]),G=i.useCallback((e,n)=>{const a=r.current;if(!a)return null;const x=a.getBoundingClientRect(),y=e-x.left,$=n-x.top,w=Math.floor(y/l),v=Math.floor($/l);return v<0||v>=t||w<0||w>=t?null:{row:v,col:w}},[l,t]),N=i.useCallback(e=>{if(!(S||s.status!=="playing"))if(!o)s.board&&s.board[e.row][e.col]>0&&(m(e),u.onGemSelect());else if(o.row===e.row&&o.col===e.col)m(null);else if(Math.abs(o.row-e.row)===1&&o.col===e.col||Math.abs(o.col-e.col)===1&&o.row===e.row)if(k(!0),p.current&&s.board){const a={pos:o,type:s.board[o.row][o.col]},x={pos:e,type:s.board[e.row][e.col]};u.onGemSwap(),p.current.addSwapAnimation(a,x,300,()=>{d(o,e,y=>{if(y&&y.length>0&&p.current){const $=y.flatMap(w=>w.positions);p.current.addEliminateAnimation($,500),u.onGemMatch(),y.some(w=>w.cascade)&&u.onGemCascade()}}).finally(()=>{k(!1),m(null)})})}else d(o,e).finally(()=>{k(!1),m(null)});else s.board&&s.board[e.row][e.col]>0?(m(e),u.onGemSelect()):m(null)},[o,S,s.status,s.board,t,l,d]),ee=i.useCallback(e=>{const n=G(e.clientX,e.clientY);n&&N(n)},[G,N]),te=i.useCallback(e=>{e.preventDefault();const n=e.touches[0];if(n){const a=G(n.clientX,n.clientY);a&&N(a)}},[G,N]),re=i.useCallback(e=>{e.preventDefault()},[]),se=i.useCallback(e=>{e.preventDefault()},[]);return i.useEffect(()=>{D(!0)},[s.board,o,t]),i.useEffect(()=>{L.startMonitoring();let e;const n=()=>{const a=performance.now();if(p.current){const x=performance.now();p.current.update();const y=performance.now();Y();const $=performance.now();L.recordFrame($-y,y-x)}else{Y();const x=performance.now();L.recordFrame(x-a,0)}e=requestAnimationFrame(n)};return e=requestAnimationFrame(n),()=>{L.stopMonitoring(),e&&cancelAnimationFrame(e)}},[Y]),i.useEffect(()=>{const e=r.current;if(e){e.width=g*M,e.height=g*M,e.style.width=`${g}px`,e.style.height=`${g}px`;const n=e.getContext("2d");n&&n.scale(M,M),D(!0)}},[g,M]),f.jsxs(Te,{children:[f.jsxs(Me,{children:[f.jsxs(X,{children:[f.jsx("div",{className:"label",children:"分数"}),f.jsx("div",{className:"value",children:s.score.toLocaleString()})]}),f.jsxs(X,{children:[f.jsx("div",{className:"label",children:"剩余步数"}),f.jsx("div",{className:"value",children:s.movesLeft})]}),f.jsxs(X,{children:[f.jsx("div",{className:"label",children:"目标"}),f.jsx("div",{className:"value",children:((J=s.targetScore)==null?void 0:J.toLocaleString())||0})]})]}),f.jsx(Se,{children:f.jsx(Ce,{ref:r,onClick:ee,onTouchStart:te,onTouchMove:re,onTouchEnd:se,width:g,height:g})}),f.jsx($e,{combo:E,comboCount:F,onComboEnd:()=>{h(null),B(0)}})]})};export{Ee as default};
//# sourceMappingURL=GameBoard-DvXOxAln.js.map
