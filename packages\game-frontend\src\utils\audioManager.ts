export interface AudioSettings {
  masterVolume: number
  musicVolume: number
  sfxVolume: number
  muted: boolean
}

export type SoundEffect = 
  | 'gem_select'
  | 'gem_swap'
  | 'gem_match'
  | 'gem_cascade'
  | 'level_complete'
  | 'level_failed'
  | 'button_click'
  | 'power_up'

export class AudioManager {
  private audioContext: AudioContext | null = null
  private musicAudio: HTMLAudioElement | null = null
  private soundEffects: Map<SoundEffect, HTMLAudioElement> = new Map()
  private settings: AudioSettings = {
    masterVolume: 0.7,
    musicVolume: 0.5,
    sfxVolume: 0.8,
    muted: false
  }

  constructor() {
    this.initializeAudio()
    this.loadSettings()
  }

  // 初始化音频系统
  private initializeAudio(): void {
    try {
      // 创建音频上下文（用于Web Audio API）
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // 创建背景音乐音频元素
      this.musicAudio = new Audio()
      this.musicAudio.loop = true
      this.musicAudio.preload = 'auto'
      
      // 预加载音效
      this.preloadSoundEffects()
      
    } catch (error) {
      console.warn('Audio initialization failed:', error)
    }
  }

  // 预加载音效
  private preloadSoundEffects(): void {
    const soundEffectUrls: Record<SoundEffect, string> = {
      gem_select: this.generateTone(440, 0.1), // A4 音符
      gem_swap: this.generateTone(523, 0.2), // C5 音符
      gem_match: this.generateChord([523, 659, 784], 0.3), // C大调和弦
      gem_cascade: this.generateArpeggio([523, 659, 784, 1047], 0.5), // 琶音
      level_complete: this.generateMelody([523, 659, 784, 1047, 1319], 0.8), // 胜利旋律
      level_failed: this.generateTone(220, 0.5), // 低音A
      button_click: this.generateTone(800, 0.1), // 高音点击
      power_up: this.generateSweep(400, 800, 0.3) // 频率扫描
    }

    Object.entries(soundEffectUrls).forEach(([effect, url]) => {
      const audio = new Audio(url)
      audio.preload = 'auto'
      this.soundEffects.set(effect as SoundEffect, audio)
    })
  }

  // 生成单音调
  private generateTone(frequency: number, duration: number): string {
    if (!this.audioContext) return ''
    
    const sampleRate = this.audioContext.sampleRate
    const numSamples = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      channelData[i] = Math.sin(2 * Math.PI * frequency * t) * Math.exp(-t * 3)
    }

    return this.bufferToDataUrl(buffer)
  }

  // 生成和弦
  private generateChord(frequencies: number[], duration: number): string {
    if (!this.audioContext) return ''
    
    const sampleRate = this.audioContext.sampleRate
    const numSamples = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      let sample = 0
      frequencies.forEach(freq => {
        sample += Math.sin(2 * Math.PI * freq * t) / frequencies.length
      })
      channelData[i] = sample * Math.exp(-t * 2)
    }

    return this.bufferToDataUrl(buffer)
  }

  // 生成琶音
  private generateArpeggio(frequencies: number[], duration: number): string {
    if (!this.audioContext) return ''
    
    const sampleRate = this.audioContext.sampleRate
    const numSamples = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)
    const noteLength = duration / frequencies.length

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      const noteIndex = Math.floor(t / noteLength)
      const noteTime = t - noteIndex * noteLength
      
      if (noteIndex < frequencies.length) {
        const freq = frequencies[noteIndex]
        channelData[i] = Math.sin(2 * Math.PI * freq * noteTime) * Math.exp(-noteTime * 4)
      }
    }

    return this.bufferToDataUrl(buffer)
  }

  // 生成旋律
  private generateMelody(frequencies: number[], duration: number): string {
    if (!this.audioContext) return ''
    
    const sampleRate = this.audioContext.sampleRate
    const numSamples = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)
    const noteLength = duration / frequencies.length

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      const noteIndex = Math.floor(t / noteLength)
      const noteTime = t - noteIndex * noteLength
      
      if (noteIndex < frequencies.length) {
        const freq = frequencies[noteIndex]
        const envelope = Math.exp(-noteTime * 3)
        channelData[i] = Math.sin(2 * Math.PI * freq * noteTime) * envelope
      }
    }

    return this.bufferToDataUrl(buffer)
  }

  // 生成频率扫描
  private generateSweep(startFreq: number, endFreq: number, duration: number): string {
    if (!this.audioContext) return ''
    
    const sampleRate = this.audioContext.sampleRate
    const numSamples = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      const progress = t / duration
      const freq = startFreq + (endFreq - startFreq) * progress
      channelData[i] = Math.sin(2 * Math.PI * freq * t) * (1 - progress)
    }

    return this.bufferToDataUrl(buffer)
  }

  // 将音频缓冲区转换为Data URL
  private bufferToDataUrl(buffer: AudioBuffer): string {
    const length = buffer.length
    const channelData = buffer.getChannelData(0)
    const samples = new Int16Array(length)
    
    for (let i = 0; i < length; i++) {
      samples[i] = channelData[i] * 32767
    }
    
    // 创建WAV文件头
    const wavHeader = new ArrayBuffer(44)
    const view = new DataView(wavHeader)
    
    // WAV文件格式头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }
    
    writeString(0, 'RIFF')
    view.setUint32(4, 36 + samples.length * 2, true)
    writeString(8, 'WAVE')
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true)
    view.setUint16(20, 1, true)
    view.setUint16(22, 1, true)
    view.setUint32(24, buffer.sampleRate, true)
    view.setUint32(28, buffer.sampleRate * 2, true)
    view.setUint16(32, 2, true)
    view.setUint16(34, 16, true)
    writeString(36, 'data')
    view.setUint32(40, samples.length * 2, true)
    
    // 合并头部和数据
    const wavFile = new Uint8Array(44 + samples.length * 2)
    wavFile.set(new Uint8Array(wavHeader), 0)
    wavFile.set(new Uint8Array(samples.buffer), 44)
    
    const blob = new Blob([wavFile], { type: 'audio/wav' })
    return URL.createObjectURL(blob)
  }

  // 播放音效
  public playSoundEffect(effect: SoundEffect): void {
    if (this.settings.muted) return
    
    const audio = this.soundEffects.get(effect)
    if (audio) {
      audio.volume = this.settings.sfxVolume * this.settings.masterVolume
      audio.currentTime = 0
      audio.play().catch(error => {
        console.warn(`Failed to play sound effect ${effect}:`, error)
      })
    }
  }

  // 播放背景音乐
  public playBackgroundMusic(url?: string): void {
    if (this.settings.muted || !this.musicAudio) return
    
    if (url && this.musicAudio.src !== url) {
      this.musicAudio.src = url
    }
    
    this.musicAudio.volume = this.settings.musicVolume * this.settings.masterVolume
    this.musicAudio.play().catch(error => {
      console.warn('Failed to play background music:', error)
    })
  }

  // 停止背景音乐
  public stopBackgroundMusic(): void {
    if (this.musicAudio) {
      this.musicAudio.pause()
      this.musicAudio.currentTime = 0
    }
  }

  // 设置音量
  public setVolume(type: keyof AudioSettings, volume: number): void {
    (this.settings as any)[type] = Math.max(0, Math.min(1, volume))
    this.saveSettings()
    
    // 更新当前播放的音频音量
    if (this.musicAudio && type === 'musicVolume') {
      this.musicAudio.volume = this.settings.musicVolume * this.settings.masterVolume
    }
  }

  // 切换静音
  public toggleMute(): void {
    this.settings.muted = !this.settings.muted
    this.saveSettings()
    
    if (this.settings.muted) {
      this.stopBackgroundMusic()
    }
  }

  // 获取设置
  public getSettings(): AudioSettings {
    return { ...this.settings }
  }

  // 保存设置到本地存储
  private saveSettings(): void {
    localStorage.setItem('audioSettings', JSON.stringify(this.settings))
  }

  // 从本地存储加载设置
  private loadSettings(): void {
    const saved = localStorage.getItem('audioSettings')
    if (saved) {
      try {
        this.settings = { ...this.settings, ...JSON.parse(saved) }
      } catch (error) {
        console.warn('Failed to load audio settings:', error)
      }
    }
  }

  // 清理资源
  public dispose(): void {
    this.stopBackgroundMusic()
    
    // 清理音效
    this.soundEffects.forEach(audio => {
      if (audio.src.startsWith('blob:')) {
        URL.revokeObjectURL(audio.src)
      }
    })
    this.soundEffects.clear()
    
    // 关闭音频上下文
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
    }
  }
}
