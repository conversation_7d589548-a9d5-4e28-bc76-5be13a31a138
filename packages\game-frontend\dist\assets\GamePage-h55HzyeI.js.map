{"version": 3, "file": "GamePage-h55HzyeI.js", "sources": ["../../src/components/Game/PowerUpEffects.tsx", "../../src/pages/GamePage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\nimport styled, { keyframes, css } from 'styled-components'\nimport { PowerUpEffect, POWER_UPS } from '../../types/powerups'\nimport { theme, media } from '../../styles/theme'\n\ninterface PowerUpEffectsProps {\n  effects: PowerUpEffect[]\n}\n\nconst pulse = keyframes`\n  0%, 100% { opacity: 1; transform: scale(1); }\n  50% { opacity: 0.8; transform: scale(1.05); }\n`\n\nconst slideIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(-100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`\n\n// const slideOut = keyframes`\n  // from {\n  //   opacity: 1;\n  //   transform: translateX(0);\n  // }\n  // to {\n  //   opacity: 0;\n  //   transform: translateX(-100%);\n  // }\n// `\n\nconst Container = styled.div`\n  position: fixed;\n  top: 20px;\n  left: 20px;\n  z-index: ${theme.zIndex.fixed};\n  display: flex;\n  flex-direction: column;\n  gap: ${theme.spacing[2]};\n  pointer-events: none;\n  \n  ${media.maxMd} {\n    top: 10px;\n    left: 10px;\n  }\n`\n\nconst EffectCard = styled.div<{ $isExpiring: boolean }>`\n  background: rgba(0, 0, 0, 0.9);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: ${theme.borderRadius.lg};\n  padding: ${theme.spacing[3]};\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[2]};\n  min-width: 200px;\n  box-shadow: ${theme.shadows.lg};\n  animation: ${slideIn} 0.3s ease-out;\n  \n  ${props => props.$isExpiring && css`\n    animation: ${pulse} 0.5s ease-in-out infinite;\n  `}\n  \n  ${media.maxMd} {\n    padding: ${theme.spacing[2]};\n    min-width: 160px;\n  }\n`\n\nconst EffectIcon = styled.div`\n  font-size: 24px;\n  flex-shrink: 0;\n  \n  ${media.maxMd} {\n    font-size: 20px;\n  }\n`\n\nconst EffectContent = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: ${theme.spacing[1]};\n`\n\nconst EffectName = styled.div`\n  color: ${theme.colors.text.primary};\n  font-size: ${theme.fontSizes.sm};\n  font-weight: ${theme.fontWeights.semibold};\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.xs};\n  }\n`\n\nconst EffectTimer = styled.div`\n  color: ${theme.colors.text.secondary};\n  font-size: ${theme.fontSizes.xs};\n  font-family: ${theme.fonts.mono};\n  \n  ${media.maxMd} {\n    font-size: 10px;\n  }\n`\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 3px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: ${theme.borderRadius.full};\n  overflow: hidden;\n`\n\nconst ProgressFill = styled.div<{ $progress: number; $color: string }>`\n  height: 100%;\n  background: ${props => props.$color};\n  width: ${props => props.$progress}%;\n  transition: width 0.1s linear;\n  border-radius: ${theme.borderRadius.full};\n  box-shadow: 0 0 10px ${props => props.$color}40;\n`\n\nconst getEffectColor = (type: string): string => {\n  switch (type) {\n    case 'score_boost':\n      return theme.colors.secondary[400]\n    case 'freeze':\n      return '#60a5fa'\n    case 'time_extend':\n      return theme.colors.success\n    default:\n      return theme.colors.primary[400]\n  }\n}\n\nconst formatTime = (seconds: number): string => {\n  if (seconds < 60) {\n    return `${seconds}s`\n  }\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = seconds % 60\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nconst PowerUpEffects: React.FC<PowerUpEffectsProps> = ({ effects }) => {\n  const [timers, setTimers] = useState<Record<string, number>>({})\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const now = Date.now()\n      const newTimers: Record<string, number> = {}\n\n      effects.forEach(effect => {\n        if (effect.duration) {\n          const elapsed = now - effect.startTime\n          const remaining = Math.max(0, effect.duration - elapsed)\n          newTimers[effect.type] = Math.ceil(remaining / 1000)\n        }\n      })\n\n      setTimers(newTimers)\n    }, 100)\n\n    return () => clearInterval(interval)\n  }, [effects])\n\n  if (effects.length === 0) {\n    return null\n  }\n\n  return (\n    <Container>\n      {effects.map(effect => {\n        const powerUp = POWER_UPS[effect.type]\n        const remainingTime = timers[effect.type] || 0\n        const isExpiring = remainingTime <= 5 && remainingTime > 0\n        const progress = effect.duration \n          ? ((effect.duration - (Date.now() - effect.startTime)) / effect.duration) * 100\n          : 100\n        const color = getEffectColor(effect.type)\n\n        return (\n          <EffectCard key={effect.type} $isExpiring={isExpiring}>\n            <EffectIcon>{powerUp.icon}</EffectIcon>\n            <EffectContent>\n              <EffectName>{powerUp.name}</EffectName>\n              {effect.duration && (\n                <>\n                  <EffectTimer>{formatTime(remainingTime)}</EffectTimer>\n                  <ProgressBar>\n                    <ProgressFill $progress={Math.max(0, progress)} $color={color} />\n                  </ProgressBar>\n                </>\n              )}\n              {effect.multiplier && (\n                <EffectTimer>倍数: {effect.multiplier}x</EffectTimer>\n              )}\n            </EffectContent>\n          </EffectCard>\n        )\n      })}\n    </Container>\n  )\n}\n\nexport default PowerUpEffects\n", "import React, { useEffect, useState } from 'react'\nimport styled from 'styled-components'\nimport { useSearchParams } from 'react-router-dom'\nimport { useGame } from '../contexts/GameContext'\nimport GameBoard from '../components/Game/GameBoard'\nimport GameOverModal from '../components/Game/GameOverModal'\nimport PowerUpPanel from '../components/Game/PowerUpPanel'\nimport PowerUpEffects from '../components/Game/PowerUpEffects'\nimport { PowerUpType, PowerUpInstance, PowerUpEffect } from '../types/powerups'\nimport { PowerUpManager } from '../utils/powerUpManager'\nimport { theme, media } from '../styles/theme'\n\nconst Container = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 2rem;\n  min-height: 100vh;\n`\n\nconst Title = styled.h1`\n  color: #fbbf24;\n  font-size: 2.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);\n`\n\nconst GameContainer = styled.div`\n  width: 100%;\n  max-width: 1200px;\n  display: grid;\n  grid-template-columns: 1fr auto;\n  gap: ${theme.spacing[6]};\n  align-items: start;\n\n  ${media.maxLg} {\n    grid-template-columns: 1fr;\n    max-width: 800px;\n    gap: ${theme.spacing[4]};\n  }\n`\n\nconst GameArea = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: ${theme.spacing[4]};\n`\n\nconst SidePanel = styled.div`\n  width: 250px;\n  display: flex;\n  flex-direction: column;\n  gap: ${theme.spacing[4]};\n\n  ${media.maxLg} {\n    width: 100%;\n    max-width: 400px;\n    margin: 0 auto;\n  }\n`\n\nconst LoadingMessage = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 3rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n\n  h2 {\n    color: #fbbf24;\n    margin-bottom: 1rem;\n  }\n`\n\nconst GamePage: React.FC = () => {\n  const { gameState, startGame } = useGame()\n  const [searchParams] = useSearchParams()\n\n  // 道具系统状态\n  const [powerUpManager] = useState(() => new PowerUpManager())\n  const [selectedPowerUp, setSelectedPowerUp] = useState<PowerUpType | null>(null)\n  const [activeEffects, setActiveEffects] = useState<PowerUpEffect[]>([])\n  const [powerUps, setPowerUps] = useState<PowerUpInstance[]>([\n    { powerUpId: 'bomb', type: 'bomb', quantity: 3 },\n    { powerUpId: 'rainbow', type: 'rainbow', quantity: 2 },\n    { powerUpId: 'hammer', type: 'hammer', quantity: 5 },\n    { powerUpId: 'shuffle', type: 'shuffle', quantity: 1 },\n    { powerUpId: 'time_extend', type: 'time_extend', quantity: 2 },\n    { powerUpId: 'score_boost', type: 'score_boost', quantity: 1 },\n    { powerUpId: 'hint', type: 'hint', quantity: 10 },\n    { powerUpId: 'freeze', type: 'freeze', quantity: 1 },\n  ])\n\n  useEffect(() => {\n    // 从URL参数获取关卡号，默认为1\n    const levelParam = searchParams.get('level')\n    const levelNumber = levelParam ? parseInt(levelParam, 10) : 1\n\n    // 如果游戏还没开始，开始指定关卡\n    if (gameState.status === 'idle') {\n      startGame(levelNumber)\n    }\n  }, [gameState.status, startGame, searchParams])\n\n  // 设置道具管理器的效果变化回调\n  useEffect(() => {\n    powerUpManager.onEffectChange = setActiveEffects\n    return () => {\n      powerUpManager.onEffectChange = undefined\n    }\n  }, [powerUpManager])\n\n  // 处理道具使用\n  const handleUsePowerUp = (type: PowerUpType) => {\n    const powerUpInstance = powerUps.find(p => p.type === type)\n    if (!powerUpInstance || powerUpInstance.quantity <= 0) {\n      return\n    }\n\n    // 这里应该根据道具类型执行不同的逻辑\n    // 暂时只是减少数量\n    setPowerUps(prev =>\n      prev.map(p =>\n        p.type === type\n          ? { ...p, quantity: p.quantity - 1 }\n          : p\n      )\n    )\n\n    // 使用道具管理器处理效果\n    const result = powerUpManager.usePowerUp({\n      type,\n      // 这里需要传入实际的游戏状态和棋盘数据\n      board: [], // 从游戏状态获取\n      gameState: gameState\n    })\n\n    if (result.success) {\n      console.log(result.message)\n      // 处理道具效果\n      if (result.effects) {\n        // 应用效果到游戏状态\n      }\n    }\n\n    setSelectedPowerUp(null)\n  }\n\n  if (gameState.status === 'idle' || gameState.status === 'loading') {\n    return (\n      <Container>\n        <Title>星光对对碰</Title>\n        <LoadingMessage>\n          <h2>🎮 正在加载游戏...</h2>\n        </LoadingMessage>\n      </Container>\n    )\n  }\n\n  return (\n    <Container>\n      <Title>星光对对碰</Title>\n      <GameContainer>\n        <GameArea>\n          <GameBoard size={6} />\n        </GameArea>\n        <SidePanel>\n          <PowerUpPanel\n            powerUps={powerUps}\n            onUsePowerUp={handleUsePowerUp}\n            selectedPowerUp={selectedPowerUp}\n            onSelectPowerUp={setSelectedPowerUp}\n            disabled={gameState.status !== 'playing'}\n          />\n        </SidePanel>\n      </GameContainer>\n      <PowerUpEffects effects={activeEffects} />\n      <GameOverModal isVisible={gameState.status === 'won' || gameState.status === 'lost'} />\n    </Container>\n  )\n}\n\nexport default GamePage\n"], "names": ["pulse", "keyframes", "slideIn", "Container", "styled", "theme", "media", "EffectCard", "props", "css", "EffectIcon", "EffectContent", "EffectName", "EffectTimer", "ProgressBar", "ProgressFill", "getEffectColor", "type", "formatTime", "seconds", "minutes", "remainingSeconds", "PowerUpEffects", "effects", "timers", "setTimers", "useState", "useEffect", "interval", "now", "newTimers", "effect", "elapsed", "remaining", "jsx", "powerUp", "POWER_UPS", "remainingTime", "isExpiring", "progress", "color", "jsxs", "Fragment", "Title", "GameContainer", "GameArea", "SidePanel", "LoadingMessage", "GamePage", "gameState", "startGame", "useGame", "searchParams", "useSearchParams", "powerUpManager", "PowerUpManager", "selectedPowerUp", "setSelectedPowerUp", "activeEffects", "setActiveEffects", "powerUps", "setPowerUps", "levelParam", "levelNumber", "handleUsePowerUp", "powerUpInstance", "p", "prev", "result", "GameBoard", "PowerUpPanel", "GameOverModal"], "mappings": "wiBASA,MAAMA,EAAQC;AAAAA;AAAAA;AAAAA,EAKRC,EAAUD;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAsBVE,EAAYC,EAAO;AAAA;AAAA;AAAA;AAAA,aAIZC,EAAM,OAAO,KAAK;AAAA;AAAA;AAAA,SAGtBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAGrBC,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA,EAMTC,EAAaH,EAAO;AAAA;AAAA;AAAA;AAAA,mBAIPC,EAAM,aAAa,EAAE;AAAA,aAC3BA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,SAGpBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,gBAETA,EAAM,QAAQ,EAAE;AAAA,eACjBH,CAAO;AAAA;AAAA,IAElBM,GAASA,EAAM,aAAeC;AAAAA,iBACjBT,CAAK;AAAA,GACnB;AAAA;AAAA,IAECM,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,EAKzBK,EAAaN,EAAO;AAAA;AAAA;AAAA;AAAA,IAItBE,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTK,EAAgBP,EAAO;AAAA;AAAA;AAAA;AAAA,SAIpBC,EAAM,QAAQ,CAAC,CAAC;AAAA,EAGnBO,EAAaR,EAAO;AAAA,WACfC,EAAM,OAAO,KAAK,OAAO;AAAA,eACrBA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,QAAQ;AAAA;AAAA,IAEvCC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BQ,EAAcT,EAAO;AAAA,WAChBC,EAAM,OAAO,KAAK,SAAS;AAAA,eACvBA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,MAAM,IAAI;AAAA;AAAA,IAE7BC,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTQ,EAAcV,EAAO;AAAA;AAAA;AAAA;AAAA,mBAIRC,EAAM,aAAa,IAAI;AAAA;AAAA,EAIpCU,EAAeX,EAAO;AAAA;AAAA,gBAEZI,GAASA,EAAM,MAAM;AAAA,WAC1BA,GAASA,EAAM,SAAS;AAAA;AAAA,mBAEhBH,EAAM,aAAa,IAAI;AAAA,yBACjBG,GAASA,EAAM,MAAM;AAAA,EAGxCQ,EAAkBC,GAAyB,CAC/C,OAAQA,EAAA,CACN,IAAK,cACH,OAAOZ,EAAM,OAAO,UAAU,GAAG,EACnC,IAAK,SACH,MAAO,UACT,IAAK,cACH,OAAOA,EAAM,OAAO,QACtB,QACE,OAAOA,EAAM,OAAO,QAAQ,GAAG,CAAA,CAErC,EAEMa,EAAcC,GAA4B,CAC9C,GAAIA,EAAU,GACZ,MAAO,GAAGA,CAAO,IAEnB,MAAMC,EAAU,KAAK,MAAMD,EAAU,EAAE,EACjCE,EAAmBF,EAAU,GACnC,MAAO,GAAGC,CAAO,IAAIC,EAAiB,WAAW,SAAS,EAAG,GAAG,CAAC,EACnE,EAEMC,EAAgD,CAAC,CAAE,QAAAC,KAAc,CACrE,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAiC,CAAA,CAAE,EAqB/D,OAnBAC,EAAAA,UAAU,IAAM,CACd,MAAMC,EAAW,YAAY,IAAM,CACjC,MAAMC,EAAM,KAAK,IAAA,EACXC,EAAoC,CAAA,EAE1CP,EAAQ,QAAQQ,GAAU,CACxB,GAAIA,EAAO,SAAU,CACnB,MAAMC,EAAUH,EAAME,EAAO,UACvBE,EAAY,KAAK,IAAI,EAAGF,EAAO,SAAWC,CAAO,EACvDF,EAAUC,EAAO,IAAI,EAAI,KAAK,KAAKE,EAAY,GAAI,CACrD,CACF,CAAC,EAEDR,EAAUK,CAAS,CACrB,EAAG,GAAG,EAEN,MAAO,IAAM,cAAcF,CAAQ,CACrC,EAAG,CAACL,CAAO,CAAC,EAERA,EAAQ,SAAW,EACd,KAIPW,EAAAA,IAAC/B,EAAA,CACE,SAAAoB,EAAQ,IAAIQ,GAAU,CACrB,MAAMI,EAAUC,EAAUL,EAAO,IAAI,EAC/BM,EAAgBb,EAAOO,EAAO,IAAI,GAAK,EACvCO,EAAaD,GAAiB,GAAKA,EAAgB,EACnDE,EAAWR,EAAO,UAClBA,EAAO,UAAY,KAAK,IAAA,EAAQA,EAAO,YAAcA,EAAO,SAAY,IAC1E,IACES,EAAQxB,EAAee,EAAO,IAAI,EAExC,OACEU,EAAAA,KAAClC,EAAA,CAA6B,YAAa+B,EACzC,SAAA,CAAAJ,EAAAA,IAACxB,EAAA,CAAY,WAAQ,IAAA,CAAK,SACzBC,EAAA,CACC,SAAA,CAAAuB,EAAAA,IAACtB,EAAA,CAAY,WAAQ,IAAA,CAAK,EACzBmB,EAAO,UACNU,EAAAA,KAAAC,EAAAA,SAAA,CACE,SAAA,CAAAR,EAAAA,IAACrB,EAAA,CAAa,SAAAK,EAAWmB,CAAa,CAAA,CAAE,EACxCH,EAAAA,IAACpB,EAAA,CACC,SAAAoB,EAAAA,IAACnB,EAAA,CAAa,UAAW,KAAK,IAAI,EAAGwB,CAAQ,EAAG,OAAQC,CAAA,CAAO,CAAA,CACjE,CAAA,EACF,EAEDT,EAAO,YACNU,EAAAA,KAAC5B,EAAA,CAAY,SAAA,CAAA,OAAKkB,EAAO,WAAW,GAAA,CAAA,CAAC,CAAA,CAAA,CAEzC,CAAA,CAAA,EAfeA,EAAO,IAgBxB,CAEJ,CAAC,CAAA,CACH,CAEJ,ECrMM5B,EAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnBuC,EAAQvC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQfwC,EAAgBxC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,SAKpBC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAGrBC,EAAM,KAAK;AAAA;AAAA;AAAA,WAGJD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBwC,EAAWzC,EAAO;AAAA;AAAA;AAAA;AAAA,SAIfC,EAAM,QAAQ,CAAC,CAAC;AAAA,EAGnByC,EAAY1C,EAAO;AAAA;AAAA;AAAA;AAAA,SAIhBC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAErBC,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOTyC,EAAiB3C,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcxB4C,GAAqB,IAAM,CAC/B,KAAM,CAAE,UAAAC,EAAW,UAAAC,CAAA,EAAcC,EAAA,EAC3B,CAACC,CAAY,EAAIC,EAAA,EAGjB,CAACC,CAAc,EAAI5B,EAAAA,SAAS,IAAM,IAAI6B,CAAgB,EACtD,CAACC,EAAiBC,CAAkB,EAAI/B,EAAAA,SAA6B,IAAI,EACzE,CAACgC,EAAeC,CAAgB,EAAIjC,EAAAA,SAA0B,CAAA,CAAE,EAChE,CAACkC,EAAUC,CAAW,EAAInC,WAA4B,CAC1D,CAAE,UAAW,OAAQ,KAAM,OAAQ,SAAU,CAAA,EAC7C,CAAE,UAAW,UAAW,KAAM,UAAW,SAAU,CAAA,EACnD,CAAE,UAAW,SAAU,KAAM,SAAU,SAAU,CAAA,EACjD,CAAE,UAAW,UAAW,KAAM,UAAW,SAAU,CAAA,EACnD,CAAE,UAAW,cAAe,KAAM,cAAe,SAAU,CAAA,EAC3D,CAAE,UAAW,cAAe,KAAM,cAAe,SAAU,CAAA,EAC3D,CAAE,UAAW,OAAQ,KAAM,OAAQ,SAAU,EAAA,EAC7C,CAAE,UAAW,SAAU,KAAM,SAAU,SAAU,CAAA,CAAE,CACpD,EAEDC,EAAAA,UAAU,IAAM,CAEd,MAAMmC,EAAaV,EAAa,IAAI,OAAO,EACrCW,EAAcD,EAAa,SAASA,EAAY,EAAE,EAAI,EAGxDb,EAAU,SAAW,QACvBC,EAAUa,CAAW,CAEzB,EAAG,CAACd,EAAU,OAAQC,EAAWE,CAAY,CAAC,EAG9CzB,EAAAA,UAAU,KACR2B,EAAe,eAAiBK,EACzB,IAAM,CACXL,EAAe,eAAiB,MAClC,GACC,CAACA,CAAc,CAAC,EAGnB,MAAMU,EAAoB/C,GAAsB,CAC9C,MAAMgD,EAAkBL,EAAS,KAAKM,GAAKA,EAAE,OAASjD,CAAI,EAC1D,GAAI,CAACgD,GAAmBA,EAAgB,UAAY,EAClD,OAKFJ,KACEM,EAAK,IAAID,GACPA,EAAE,OAASjD,EACP,CAAE,GAAGiD,EAAG,SAAUA,EAAE,SAAW,CAAA,EAC/BA,CAAA,CACN,EAIF,MAAME,EAASd,EAAe,WAAW,CACvC,KAAArC,EAEA,MAAO,CAAA,EACP,UAAAgC,CAAA,CACD,EAEGmB,EAAO,UACT,QAAQ,IAAIA,EAAO,OAAO,EAEtBA,EAAO,SAKbX,EAAmB,IAAI,CACzB,EAEA,OAAIR,EAAU,SAAW,QAAUA,EAAU,SAAW,iBAEnD9C,EAAA,CACC,SAAA,CAAA+B,EAAAA,IAACS,GAAM,SAAA,OAAA,CAAK,EACZT,MAACa,EAAA,CACC,SAAAb,EAAAA,IAAC,KAAA,CAAG,wBAAY,CAAA,CAClB,CAAA,EACF,SAKD/B,EAAA,CACC,SAAA,CAAA+B,EAAAA,IAACS,GAAM,SAAA,OAAA,CAAK,SACXC,EAAA,CACC,SAAA,CAAAV,MAACW,EAAA,CACC,SAAAX,EAAAA,IAACmC,EAAA,CAAU,KAAM,EAAG,EACtB,QACCvB,EAAA,CACC,SAAAZ,EAAAA,IAACoC,EAAA,CACC,SAAAV,EACA,aAAcI,EACd,gBAAAR,EACA,gBAAiBC,EACjB,SAAUR,EAAU,SAAW,SAAA,CAAA,CACjC,CACF,CAAA,EACF,EACAf,EAAAA,IAACZ,EAAA,CAAe,QAASoC,CAAA,CAAe,EACxCxB,MAACqC,GAAc,UAAWtB,EAAU,SAAW,OAASA,EAAU,SAAW,MAAA,CAAQ,CAAA,EACvF,CAEJ"}