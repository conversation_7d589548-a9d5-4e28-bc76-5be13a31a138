{"version": 3, "mappings": ";;;;;;;;;GASa,IAAIA,GAAEC,EAAiBC,GAAE,OAAO,IAAI,eAAe,EAAEC,GAAE,OAAO,IAAI,gBAAgB,EAAEC,GAAE,OAAO,UAAU,eAAeC,GAAEL,GAAE,mDAAmD,kBAAkBM,GAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,GAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAcJ,IAAT,SAAaG,EAAE,GAAGH,GAAYD,EAAE,MAAX,SAAiBI,EAAE,GAAGJ,EAAE,KAAcA,EAAE,MAAX,SAAiBK,EAAEL,EAAE,KAAK,IAAIE,KAAKF,EAAEL,GAAE,KAAKK,EAAEE,CAAC,GAAG,CAACL,GAAE,eAAeK,CAAC,IAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,GAAGH,GAAGA,EAAE,aAAa,IAAIG,KAAKF,EAAED,EAAE,aAAaC,EAAWG,EAAED,CAAC,aAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,MAAM,CAAC,SAAST,GAAE,KAAKM,EAAE,IAAIK,EAAE,IAAIC,EAAE,MAAMF,EAAE,OAAOP,GAAE,OAAO,CAAC,aAAkBF,GAAEY,GAAA,IAAYR,GAAEQ,GAAA,KAAaR,GCPxWS,GAAA,QAAiBf,0BCDfG,GAAIH,GAENgB,GAAA,WAAqBb,GAAE,WACvBa,GAAA,YAAsBb,GAAE,YCL1B,SAASc,GAAgBC,EAAG,EAAG,CAC7B,OAAOD,GAAkB,OAAO,eAAiB,OAAO,eAAe,KAAI,EAAK,SAAUC,EAAGN,EAAG,CAC9F,OAAOM,EAAE,UAAYN,EAAGM,CAC1B,EAAGD,GAAgBC,EAAG,CAAC,CACzB,CCHA,SAASC,GAAeD,EAAGE,EAAG,CAC5BF,EAAE,UAAY,OAAO,OAAOE,EAAE,SAAS,EAAGF,EAAE,UAAU,YAAcA,EAAGG,GAAeH,EAAGE,CAAC,CAC5F,CCHO,IAAIE,GAA4B,UAAY,CACjD,SAASA,GAAe,CACtB,KAAK,UAAY,EACnB,CAEA,IAAIC,EAASD,EAAa,UAE1B,OAAAC,EAAO,UAAY,SAAmBC,EAAU,CAC9C,IAAIC,EAAQ,KAERC,EAAWF,GAAY,UAAY,CAEvC,EAEA,YAAK,UAAU,KAAKE,CAAQ,EAC5B,KAAK,YAAW,EACT,UAAY,CACjBD,EAAM,UAAYA,EAAM,UAAU,OAAO,SAAUE,EAAG,CACpD,OAAOA,IAAMD,CACf,CAAC,EAEDD,EAAM,cAAa,CACrB,CACF,EAEAF,EAAO,aAAe,UAAwB,CAC5C,OAAO,KAAK,UAAU,OAAS,CACjC,EAEAA,EAAO,YAAc,UAAuB,CAC5C,EAEAA,EAAO,cAAgB,UAAyB,CAChD,EAEOD,CACT,EAAC,ECpCD,SAASM,GAAW,CAClB,OAAOA,EAAW,OAAO,OAAS,OAAO,OAAO,KAAI,EAAK,SAAUxB,EAAG,CACpE,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CACzC,IAAIc,EAAI,UAAU,CAAC,EACnB,QAASW,KAAKX,GAAI,IAAI,eAAe,KAAKA,EAAGW,CAAC,IAAMzB,EAAEyB,CAAC,EAAIX,EAAEW,CAAC,EAChE,CACA,OAAOzB,CACT,EAAGwB,EAAS,MAAM,KAAM,SAAS,CACnC,CCLO,IAAIE,GAAW,OAAO,OAAW,IACjC,SAASC,GAAO,CAEvB,CACO,SAASC,GAAiBC,EAASC,EAAO,CAC/C,OAAO,OAAOD,GAAY,WAAaA,EAAQC,CAAK,EAAID,CAC1D,CACO,SAASE,GAAeC,EAAO,CACpC,OAAO,OAAOA,GAAU,UAAYA,GAAS,GAAKA,IAAU,GAC9D,CACO,SAASC,GAAoBD,EAAO,CACzC,OAAO,MAAM,QAAQA,CAAK,EAAIA,EAAQ,CAACA,CAAK,CAC9C,CAWO,SAASE,GAAeC,EAAWC,EAAW,CACnD,OAAO,KAAK,IAAID,GAAaC,GAAa,GAAK,KAAK,IAAG,EAAI,CAAC,CAC9D,CACO,SAASC,GAAeC,EAAMC,EAAMC,EAAM,CAC/C,OAAKC,GAAWH,CAAI,EAIhB,OAAOC,GAAS,WACXf,EAAS,GAAIgB,EAAM,CACxB,SAAUF,EACV,QAASC,CACf,CAAK,EAGIf,EAAS,GAAIe,EAAM,CACxB,SAAUD,CACd,CAAG,EAZQA,CAaX,CACO,SAASI,GAAkBJ,EAAMC,EAAMC,EAAM,CAClD,OAAIC,GAAWH,CAAI,EACb,OAAOC,GAAS,WACXf,EAAS,GAAIgB,EAAM,CACxB,YAAaF,EACb,WAAYC,CACpB,CAAO,EAGIf,EAAS,GAAIe,EAAM,CACxB,YAAaD,CACnB,CAAK,EAGC,OAAOA,GAAS,WACXd,EAAS,GAAIe,EAAM,CACxB,WAAYD,CAClB,CAAK,EAGId,EAAS,GAAIc,CAAI,CAC1B,CACO,SAASK,EAAgBL,EAAMC,EAAMC,EAAM,CAChD,OAAOC,GAAWH,CAAI,EAAI,CAACd,EAAS,GAAIe,EAAM,CAC5C,SAAUD,CACd,CAAG,EAAGE,CAAI,EAAI,CAACF,GAAQ,GAAIC,CAAI,CAC/B,CAMO,SAASK,GAAqBC,EAAQC,EAAU,CACrD,GAAID,IAAW,IAAQC,IAAa,IAAQD,GAAU,MAAQC,GAAY,KACxE,MAAO,MACF,GAAID,IAAW,IAASC,IAAa,GAC1C,MAAO,OAIP,IAAIC,EAAWF,GAA0B,CAACC,EAC1C,OAAOC,EAAW,SAAW,UAEjC,CACO,SAASC,GAAWC,EAASC,EAAO,CACzC,IAAIL,EAASI,EAAQ,OACjBE,EAAQF,EAAQ,MAChBG,EAAWH,EAAQ,SACnBH,EAAWG,EAAQ,SACnBI,EAAYJ,EAAQ,UACpBK,EAAWL,EAAQ,SACnBM,EAAQN,EAAQ,MAEpB,GAAIR,GAAWa,CAAQ,GACrB,GAAIH,GACF,GAAID,EAAM,YAAcM,GAAsBF,EAAUJ,EAAM,OAAO,EACnE,MAAO,WAEA,CAACO,GAAgBP,EAAM,SAAUI,CAAQ,EAClD,MAAO,GAIX,IAAII,EAAoBd,GAAqBC,EAAQC,CAAQ,EAE7D,GAAIY,IAAsB,OACxB,MAAO,GACF,GAAIA,IAAsB,MAAO,CACtC,IAAIX,EAAWG,EAAM,SAAQ,EAM7B,GAJIQ,IAAsB,UAAY,CAACX,GAInCW,IAAsB,YAAcX,EACtC,MAAO,EAEX,CAUA,MARI,SAAOQ,GAAU,WAAaL,EAAM,QAAO,IAAOK,GAIlD,OAAOH,GAAa,WAAaF,EAAM,WAAU,IAAOE,GAIxDC,GAAa,CAACA,EAAUH,CAAK,EAKnC,CACO,SAASS,GAAcV,EAASW,EAAU,CAC/C,IAAIT,EAAQF,EAAQ,MAChBG,EAAWH,EAAQ,SACnBI,EAAYJ,EAAQ,UACpBY,EAAcZ,EAAQ,YAE1B,GAAIR,GAAWoB,CAAW,EAAG,CAC3B,GAAI,CAACD,EAAS,QAAQ,YACpB,MAAO,GAGT,GAAIT,GACF,GAAIW,EAAaF,EAAS,QAAQ,WAAW,IAAME,EAAaD,CAAW,EACzE,MAAO,WAEA,CAACJ,GAAgBG,EAAS,QAAQ,YAAaC,CAAW,EACnE,MAAO,EAEX,CAMA,MAJI,SAAOT,GAAa,WAAaQ,EAAS,MAAM,SAAW,YAAcR,GAIzEC,GAAa,CAACA,EAAUO,CAAQ,EAKtC,CACO,SAASJ,GAAsBF,EAAUS,EAAS,CACvD,IAAIC,GAAUD,GAAW,KAAO,OAASA,EAAQ,iBAAmBD,EACpE,OAAOE,EAAOV,CAAQ,CACxB,CAKO,SAASQ,EAAaR,EAAU,CACrC,IAAIW,EAAUhC,GAAoBqB,CAAQ,EAC1C,OAAOY,GAAgBD,CAAO,CAChC,CAKO,SAASC,GAAgBlC,EAAO,CACrC,OAAO,KAAK,UAAUA,EAAO,SAAUmC,EAAGC,EAAK,CAC7C,OAAOC,GAAcD,CAAG,EAAI,OAAO,KAAKA,CAAG,EAAE,OAAO,OAAO,SAAUE,EAAQC,EAAK,CAChF,OAAAD,EAAOC,CAAG,EAAIH,EAAIG,CAAG,EACdD,CACT,EAAG,EAAE,EAAIF,CACX,CAAC,CACH,CAKO,SAASX,GAAgBrD,EAAGE,EAAG,CACpC,OAAOkE,GAAiBvC,GAAoB7B,CAAC,EAAG6B,GAAoB3B,CAAC,CAAC,CACxE,CAKO,SAASkE,GAAiBpE,EAAGE,EAAG,CACrC,OAAIF,IAAME,EACD,GAGL,OAAOF,GAAM,OAAOE,EACf,GAGLF,GAAKE,GAAK,OAAOF,GAAM,UAAY,OAAOE,GAAM,SAC3C,CAAC,OAAO,KAAKA,CAAC,EAAE,KAAK,SAAUiE,EAAK,CACzC,MAAO,CAACC,GAAiBpE,EAAEmE,CAAG,EAAGjE,EAAEiE,CAAG,CAAC,CACzC,CAAC,EAGI,EACT,CAOO,SAASE,GAAiBrE,EAAGE,EAAG,CACrC,GAAIF,IAAME,EACR,OAAOF,EAGT,IAAIsE,EAAQ,MAAM,QAAQtE,CAAC,GAAK,MAAM,QAAQE,CAAC,EAE/C,GAAIoE,GAASL,GAAcjE,CAAC,GAAKiE,GAAc/D,CAAC,EAAG,CAOjD,QANIqE,EAAQD,EAAQtE,EAAE,OAAS,OAAO,KAAKA,CAAC,EAAE,OAC1CwE,EAASF,EAAQpE,EAAI,OAAO,KAAKA,CAAC,EAClCuE,EAAQD,EAAO,OACfE,EAAOJ,EAAQ,GAAK,GACpBK,EAAa,EAERC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,IAAIT,EAAMG,EAAQM,EAAIJ,EAAOI,CAAC,EAC9BF,EAAKP,CAAG,EAAIE,GAAiBrE,EAAEmE,CAAG,EAAGjE,EAAEiE,CAAG,CAAC,EAEvCO,EAAKP,CAAG,IAAMnE,EAAEmE,CAAG,GACrBQ,GAEJ,CAEA,OAAOJ,IAAUE,GAASE,IAAeJ,EAAQvE,EAAI0E,CACvD,CAEA,OAAOxE,CACT,CAKO,SAAS2E,GAAoB7E,EAAGE,EAAG,CACxC,GAAIF,GAAK,CAACE,GAAKA,GAAK,CAACF,EACnB,MAAO,GAGT,QAASmE,KAAOnE,EACd,GAAIA,EAAEmE,CAAG,IAAMjE,EAAEiE,CAAG,EAClB,MAAO,GAIX,MAAO,EACT,CAEO,SAASF,GAAcrD,EAAG,CAC/B,GAAI,CAACkE,GAAmBlE,CAAC,EACvB,MAAO,GAIT,IAAImE,EAAOnE,EAAE,YAEb,GAAI,OAAOmE,EAAS,IAClB,MAAO,GAIT,IAAIC,EAAOD,EAAK,UAOhB,MALI,GAACD,GAAmBE,CAAI,GAKxB,CAACA,EAAK,eAAe,eAAe,EAM1C,CAEA,SAASF,GAAmBlE,EAAG,CAC7B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBAC/C,CAEO,SAASyB,GAAWT,EAAO,CAChC,OAAO,OAAOA,GAAU,UAAY,MAAM,QAAQA,CAAK,CACzD,CAIO,SAASqD,GAAMC,EAAS,CAC7B,OAAO,IAAI,QAAQ,SAAUC,EAAS,CACpC,WAAWA,EAASD,CAAO,CAC7B,CAAC,CACH,CAMO,SAASE,GAAkBlE,EAAU,CAC1C,QAAQ,QAAO,EAAG,KAAKA,CAAQ,EAAE,MAAM,SAAUmE,EAAO,CACtD,OAAO,WAAW,UAAY,CAC5B,MAAMA,CACR,CAAC,CACH,CAAC,CACH,CACO,SAASC,IAAqB,CACnC,GAAI,OAAO,iBAAoB,WAC7B,OAAO,IAAI,eAEf,CCxUO,IAAIC,GAA4B,SAAUC,EAAe,CAC9D7E,GAAe4E,EAAcC,CAAa,EAE1C,SAASD,GAAe,CACtB,IAAItE,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KAEpCvE,EAAM,MAAQ,SAAUwE,EAAS,CAC/B,IAAIC,EAEJ,GAAI,CAACpE,MAAcoE,EAAU,SAAW,MAAgBA,EAAQ,kBAAmB,CACjF,IAAI1E,EAAW,UAAoB,CACjC,OAAOyE,EAAO,CAChB,EAGA,cAAO,iBAAiB,mBAAoBzE,EAAU,EAAK,EAC3D,OAAO,iBAAiB,QAASA,EAAU,EAAK,EACzC,UAAY,CAEjB,OAAO,oBAAoB,mBAAoBA,CAAQ,EACvD,OAAO,oBAAoB,QAASA,CAAQ,CAC9C,CACF,CACF,EAEOC,CACT,CAEA,IAAIF,EAASwE,EAAa,UAE1B,OAAAxE,EAAO,YAAc,UAAuB,CACrC,KAAK,SACR,KAAK,iBAAiB,KAAK,KAAK,CAEpC,EAEAA,EAAO,cAAgB,UAAyB,CAC9C,GAAI,CAAC,KAAK,eAAgB,CACxB,IAAI4E,GAEHA,EAAgB,KAAK,UAAY,MAAgBA,EAAc,KAAK,IAAI,EACzE,KAAK,QAAU,MACjB,CACF,EAEA5E,EAAO,iBAAmB,SAA0B6E,EAAO,CACzD,IAAIC,EACAC,EAAS,KAEb,KAAK,MAAQF,GACZC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,KAAK,IAAI,EAC3E,KAAK,QAAUD,EAAM,SAAUG,EAAS,CAClC,OAAOA,GAAY,UACrBD,EAAO,WAAWC,CAAO,EAEzBD,EAAO,QAAO,CAElB,CAAC,CACH,EAEA/E,EAAO,WAAa,SAAoBgF,EAAS,CAC/C,KAAK,QAAUA,EAEXA,GACF,KAAK,QAAO,CAEhB,EAEAhF,EAAO,QAAU,UAAmB,CAClC,KAAK,UAAU,QAAQ,SAAUC,EAAU,CACzCA,EAAQ,CACV,CAAC,CACH,EAEAD,EAAO,UAAY,UAAqB,CACtC,OAAI,OAAO,KAAK,SAAY,UACnB,KAAK,QAIV,OAAO,SAAa,IACf,GAGF,CAAC,OAAW,UAAW,WAAW,EAAE,SAAS,SAAS,eAAe,CAC9E,EAEOwE,CACT,EAAEzE,EAAY,EACHkF,GAAe,IAAIT,GC3FnBU,GAA6B,SAAUT,EAAe,CAC/D7E,GAAesF,EAAeT,CAAa,EAE3C,SAASS,GAAgB,CACvB,IAAIhF,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KAEpCvE,EAAM,MAAQ,SAAUiF,EAAU,CAChC,IAAIR,EAEJ,GAAI,CAACpE,MAAcoE,EAAU,SAAW,MAAgBA,EAAQ,kBAAmB,CACjF,IAAI1E,EAAW,UAAoB,CACjC,OAAOkF,EAAQ,CACjB,EAGA,cAAO,iBAAiB,SAAUlF,EAAU,EAAK,EACjD,OAAO,iBAAiB,UAAWA,EAAU,EAAK,EAC3C,UAAY,CAEjB,OAAO,oBAAoB,SAAUA,CAAQ,EAC7C,OAAO,oBAAoB,UAAWA,CAAQ,CAChD,CACF,CACF,EAEOC,CACT,CAEA,IAAIF,EAASkF,EAAc,UAE3B,OAAAlF,EAAO,YAAc,UAAuB,CACrC,KAAK,SACR,KAAK,iBAAiB,KAAK,KAAK,CAEpC,EAEAA,EAAO,cAAgB,UAAyB,CAC9C,GAAI,CAAC,KAAK,eAAgB,CACxB,IAAI4E,GAEHA,EAAgB,KAAK,UAAY,MAAgBA,EAAc,KAAK,IAAI,EACzE,KAAK,QAAU,MACjB,CACF,EAEA5E,EAAO,iBAAmB,SAA0B6E,EAAO,CACzD,IAAIC,EACAC,EAAS,KAEb,KAAK,MAAQF,GACZC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,KAAK,IAAI,EAC3E,KAAK,QAAUD,EAAM,SAAUO,EAAQ,CACjC,OAAOA,GAAW,UACpBL,EAAO,UAAUK,CAAM,EAEvBL,EAAO,SAAQ,CAEnB,CAAC,CACH,EAEA/E,EAAO,UAAY,SAAmBoF,EAAQ,CAC5C,KAAK,OAASA,EAEVA,GACF,KAAK,SAAQ,CAEjB,EAEApF,EAAO,SAAW,UAAoB,CACpC,KAAK,UAAU,QAAQ,SAAUC,EAAU,CACzCA,EAAQ,CACV,CAAC,CACH,EAEAD,EAAO,SAAW,UAAoB,CACpC,OAAI,OAAO,KAAK,QAAW,UAClB,KAAK,OAGV,OAAO,UAAc,KAAe,OAAO,UAAU,OAAW,IAC3D,GAGF,UAAU,MACnB,EAEOkF,CACT,EAAEnF,EAAY,EACHsF,GAAgB,IAAIH,GCzF/B,SAASI,GAAkBC,EAAc,CACvC,OAAO,KAAK,IAAI,IAAO,KAAK,IAAI,EAAGA,CAAY,EAAG,GAAK,CACzD,CAEO,SAASC,GAAa3E,EAAO,CAClC,OAAO,OAAQA,GAAS,KAAO,OAASA,EAAM,SAAY,UAC5D,CACO,IAAI4E,GAAiB,SAAwB7C,EAAS,CAC3D,KAAK,OAASA,GAAW,KAAO,OAASA,EAAQ,OACjD,KAAK,OAASA,GAAW,KAAO,OAASA,EAAQ,MACnD,EACO,SAAS8C,GAAiB7E,EAAO,CACtC,OAAOA,aAAiB4E,EAC1B,CAEO,IAAIE,GAAU,SAAiBC,EAAQ,CAC5C,IAAI1F,EAAQ,KAER2F,EAAc,GACdC,EACAC,EACAC,EACAC,EACJ,KAAK,MAAQL,EAAO,MAEpB,KAAK,OAAS,SAAUM,EAAe,CACrC,OAAOJ,GAAY,KAAO,OAASA,EAASI,CAAa,CAC3D,EAEA,KAAK,YAAc,UAAY,CAC7BL,EAAc,EAChB,EAEA,KAAK,cAAgB,UAAY,CAC/BA,EAAc,EAChB,EAEA,KAAK,SAAW,UAAY,CAC1B,OAAOE,GAAc,KAAO,OAASA,EAAU,CACjD,EAEA,KAAK,aAAe,EACpB,KAAK,SAAW,GAChB,KAAK,WAAa,GAClB,KAAK,sBAAwB,GAC7B,KAAK,QAAU,IAAI,QAAQ,SAAUI,EAAcC,EAAa,CAC9DJ,EAAiBG,EACjBF,EAAgBG,CAClB,CAAC,EAED,IAAIhC,EAAU,SAAiBvD,EAAO,CAC/BX,EAAM,aACTA,EAAM,WAAa,GACnB0F,EAAO,WAAa,MAAgBA,EAAO,UAAU/E,CAAK,EAC1DkF,GAAc,MAAgBA,EAAU,EACxCC,EAAenF,CAAK,EAExB,EAEIwF,EAAS,SAAgBxF,EAAO,CAC7BX,EAAM,aACTA,EAAM,WAAa,GACnB0F,EAAO,SAAW,MAAgBA,EAAO,QAAQ/E,CAAK,EACtDkF,GAAc,MAAgBA,EAAU,EACxCE,EAAcpF,CAAK,EAEvB,EAEIyF,EAAQ,UAAiB,CAC3B,OAAO,IAAI,QAAQ,SAAUC,EAAiB,CAC5CR,EAAaQ,EACbrG,EAAM,SAAW,GACjB0F,EAAO,SAAW,MAAgBA,EAAO,QAAO,CAClD,CAAC,EAAE,KAAK,UAAY,CAClBG,EAAa,OACb7F,EAAM,SAAW,GACjB0F,EAAO,YAAc,MAAgBA,EAAO,WAAU,CACxD,CAAC,CACH,EAGIY,EAAM,SAASA,GAAM,CAEvB,GAAI,CAAAtG,EAAM,WAIV,KAAIuG,EAEJ,GAAI,CACFA,EAAiBb,EAAO,GAAE,CAC5B,OAAStB,EAAO,CACdmC,EAAiB,QAAQ,OAAOnC,CAAK,CACvC,CAGAwB,EAAW,SAAkBI,EAAe,CAC1C,GAAI,CAAChG,EAAM,aACTmG,EAAO,IAAIZ,GAAeS,CAAa,CAAC,EACxChG,EAAM,OAAS,MAAgBA,EAAM,MAAK,EAEtCsF,GAAaiB,CAAc,GAC7B,GAAI,CACFA,EAAe,OAAM,CACvB,MAAkB,CAAC,CAGzB,EAGAvG,EAAM,sBAAwBsF,GAAaiB,CAAc,EACzD,QAAQ,QAAQA,CAAc,EAAE,KAAKrC,CAAO,EAAE,MAAM,SAAUE,EAAO,CACnE,IAAIoC,EAAeC,EAGnB,GAAI,CAAAzG,EAAM,WAKV,KAAI0G,GAASF,EAAgBd,EAAO,QAAU,KAAOc,EAAgB,EACjEG,GAAcF,EAAqBf,EAAO,aAAe,KAAOe,EAAqBrB,GACrFwB,EAAQ,OAAOD,GAAe,WAAaA,EAAW3G,EAAM,aAAcoE,CAAK,EAAIuC,EACnFE,EAAcH,IAAU,IAAQ,OAAOA,GAAU,UAAY1G,EAAM,aAAe0G,GAAS,OAAOA,GAAU,YAAcA,EAAM1G,EAAM,aAAcoE,CAAK,EAE7J,GAAIuB,GAAe,CAACkB,EAAa,CAE/BV,EAAO/B,CAAK,EACZ,MACF,CAEApE,EAAM,eAEN0F,EAAO,QAAU,MAAgBA,EAAO,OAAO1F,EAAM,aAAcoE,CAAK,EAExEJ,GAAM4C,CAAK,EACV,KAAK,UAAY,CAChB,GAAI,CAAC7B,GAAa,UAAS,GAAM,CAACI,GAAc,SAAQ,EACtD,OAAOiB,EAAK,CAEhB,CAAC,EAAE,KAAK,UAAY,CACdT,EACFQ,EAAO/B,CAAK,EAEZkC,EAAG,CAEP,CAAC,EACH,CAAC,EACH,EAGAA,EAAG,CACL,ECzJWQ,GAA6B,UAAY,CAClD,SAASA,GAAgB,CACvB,KAAK,MAAQ,GACb,KAAK,aAAe,EAEpB,KAAK,SAAW,SAAU7G,EAAU,CAClCA,EAAQ,CACV,EAEA,KAAK,cAAgB,SAAUA,EAAU,CACvCA,EAAQ,CACV,CACF,CAEA,IAAIH,EAASgH,EAAc,UAE3B,OAAAhH,EAAO,MAAQ,SAAeG,EAAU,CACtC,IAAIgD,EACJ,KAAK,eAEL,GAAI,CACFA,EAAShD,EAAQ,CACnB,QAAC,CACC,KAAK,eAEA,KAAK,cACR,KAAK,MAAK,CAEd,CAEA,OAAOgD,CACT,EAEAnD,EAAO,SAAW,SAAkBG,EAAU,CAC5C,IAAID,EAAQ,KAER,KAAK,aACP,KAAK,MAAM,KAAKC,CAAQ,EAExBkE,GAAkB,UAAY,CAC5BnE,EAAM,SAASC,CAAQ,CACzB,CAAC,CAEL,EAMAH,EAAO,WAAa,SAAoBG,EAAU,CAChD,IAAI4E,EAAS,KAEb,OAAO,UAAY,CACjB,QAASkC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAG7BpC,EAAO,SAAS,UAAY,CAC1B5E,EAAS,MAAM,OAAQ+G,CAAI,CAC7B,CAAC,CACH,CACF,EAEAlH,EAAO,MAAQ,UAAiB,CAC9B,IAAIoH,EAAS,KAETC,EAAQ,KAAK,MACjB,KAAK,MAAQ,GAETA,EAAM,QACRhD,GAAkB,UAAY,CAC5B+C,EAAO,cAAc,UAAY,CAC/BC,EAAM,QAAQ,SAAUlH,EAAU,CAChCiH,EAAO,SAASjH,CAAQ,CAC1B,CAAC,CACH,CAAC,CACH,CAAC,CAEL,EAOAH,EAAO,kBAAoB,SAA2BsH,EAAI,CACxD,KAAK,SAAWA,CAClB,EAOAtH,EAAO,uBAAyB,SAAgCsH,EAAI,CAClE,KAAK,cAAgBA,CACvB,EAEON,CACT,IAEWO,EAAgB,IAAIP,GCtG3BQ,GAAS,QACN,SAASC,IAAY,CAC1B,OAAOD,EACT,CACO,SAASE,GAAUC,EAAW,CACnCH,GAASG,CACX,CCDO,IAAIC,GAAqB,UAAY,CAC1C,SAASA,EAAMhC,EAAQ,CACrB,KAAK,oBAAsB,GAC3B,KAAK,aAAe,GACpB,KAAK,eAAiBA,EAAO,eAC7B,KAAK,WAAWA,EAAO,OAAO,EAC9B,KAAK,UAAY,GACjB,KAAK,MAAQA,EAAO,MACpB,KAAK,SAAWA,EAAO,SACvB,KAAK,UAAYA,EAAO,UACxB,KAAK,aAAeA,EAAO,OAAS,KAAK,gBAAgB,KAAK,OAAO,EACrE,KAAK,MAAQ,KAAK,aAClB,KAAK,KAAOA,EAAO,KACnB,KAAK,WAAU,CACjB,CAEA,IAAI5F,EAAS4H,EAAM,UAEnB,OAAA5H,EAAO,WAAa,SAAoB4C,EAAS,CAC/C,IAAIiF,EAEJ,KAAK,QAAUxH,EAAS,GAAI,KAAK,eAAgBuC,CAAO,EACxD,KAAK,KAAOA,GAAW,KAAO,OAASA,EAAQ,KAE/C,KAAK,UAAY,KAAK,IAAI,KAAK,WAAa,GAAIiF,EAAwB,KAAK,QAAQ,YAAc,KAAOA,EAAwB,EAAI,GAAK,GAAI,CACjJ,EAEA7H,EAAO,kBAAoB,SAA2B4C,EAAS,CAC7D,KAAK,eAAiBA,CACxB,EAEA5C,EAAO,WAAa,UAAsB,CACxC,IAAIE,EAAQ,KAEZ,KAAK,eAAc,EAEfU,GAAe,KAAK,SAAS,IAC/B,KAAK,UAAY,WAAW,UAAY,CACtCV,EAAM,eAAc,CACtB,EAAG,KAAK,SAAS,EAErB,EAEAF,EAAO,eAAiB,UAA0B,CAC5C,KAAK,YACP,aAAa,KAAK,SAAS,EAC3B,KAAK,UAAY,OAErB,EAEAA,EAAO,eAAiB,UAA0B,CAC3C,KAAK,UAAU,SACd,KAAK,MAAM,WACT,KAAK,cACP,KAAK,WAAU,EAGjB,KAAK,MAAM,OAAO,IAAI,EAG5B,EAEAA,EAAO,QAAU,SAAiBU,EAASkC,EAAS,CAClD,IAAIkF,EAAuBC,EAEvBC,EAAW,KAAK,MAAM,KAEtBC,EAAOxH,GAAiBC,EAASsH,CAAQ,EAE7C,OAAKF,GAAyBC,EAAgB,KAAK,SAAS,cAAgB,MAAgBD,EAAsB,KAAKC,EAAeC,EAAUC,CAAI,EAClJA,EAAOD,EACE,KAAK,QAAQ,oBAAsB,KAE5CC,EAAO3E,GAAiB0E,EAAUC,CAAI,GAIxC,KAAK,SAAS,CACZ,KAAMA,EACN,KAAM,UACN,cAAerF,GAAW,KAAO,OAASA,EAAQ,SACxD,CAAK,EACMqF,CACT,EAEAjI,EAAO,SAAW,SAAkBkI,EAAOC,EAAiB,CAC1D,KAAK,SAAS,CACZ,KAAM,WACN,MAAOD,EACP,gBAAiBC,CACvB,CAAK,CACH,EAEAnI,EAAO,OAAS,SAAgB4C,EAAS,CACvC,IAAIwF,EAEAC,EAAU,KAAK,QACnB,OAACD,EAAgB,KAAK,UAAY,MAAgBA,EAAc,OAAOxF,CAAO,EACvEyF,EAAUA,EAAQ,KAAK7H,CAAI,EAAE,MAAMA,CAAI,EAAI,QAAQ,QAAO,CACnE,EAEAR,EAAO,QAAU,UAAmB,CAClC,KAAK,eAAc,EACnB,KAAK,OAAO,CACV,OAAQ,EACd,CAAK,CACH,EAEAA,EAAO,MAAQ,UAAiB,CAC9B,KAAK,QAAO,EACZ,KAAK,SAAS,KAAK,YAAY,CACjC,EAEAA,EAAO,SAAW,UAAoB,CACpC,OAAO,KAAK,UAAU,KAAK,SAAUsI,EAAU,CAC7C,OAAOA,EAAS,QAAQ,UAAY,EACtC,CAAC,CACH,EAEAtI,EAAO,WAAa,UAAsB,CACxC,OAAO,KAAK,MAAM,UACpB,EAEAA,EAAO,QAAU,UAAmB,CAClC,OAAO,KAAK,MAAM,eAAiB,CAAC,KAAK,MAAM,eAAiB,KAAK,UAAU,KAAK,SAAUsI,EAAU,CACtG,OAAOA,EAAS,iBAAgB,EAAG,OACrC,CAAC,CACH,EAEAtI,EAAO,cAAgB,SAAuBiB,EAAW,CACvD,OAAIA,IAAc,SAChBA,EAAY,GAGP,KAAK,MAAM,eAAiB,CAAC,KAAK,MAAM,eAAiB,CAACF,GAAe,KAAK,MAAM,cAAeE,CAAS,CACrH,EAEAjB,EAAO,QAAU,UAAmB,CAClC,IAAIuI,EAEAD,EAAW,KAAK,UAAU,KAAK,SAAUlI,EAAG,CAC9C,OAAOA,EAAE,yBAAwB,CACnC,CAAC,EAEGkI,GACFA,EAAS,QAAO,GAIjBC,EAAiB,KAAK,UAAY,MAAgBA,EAAe,SAAQ,CAC5E,EAEAvI,EAAO,SAAW,UAAoB,CACpC,IAAIwI,EAEAF,EAAW,KAAK,UAAU,KAAK,SAAUlI,EAAG,CAC9C,OAAOA,EAAE,uBAAsB,CACjC,CAAC,EAEGkI,GACFA,EAAS,QAAO,GAIjBE,EAAiB,KAAK,UAAY,MAAgBA,EAAe,SAAQ,CAC5E,EAEAxI,EAAO,YAAc,SAAqBsI,EAAU,CAC9C,KAAK,UAAU,QAAQA,CAAQ,IAAM,KACvC,KAAK,UAAU,KAAKA,CAAQ,EAC5B,KAAK,aAAe,GAEpB,KAAK,eAAc,EACnB,KAAK,MAAM,OAAO,CAChB,KAAM,gBACN,MAAO,KACP,SAAUA,CAClB,CAAO,EAEL,EAEAtI,EAAO,eAAiB,SAAwBsI,EAAU,CACpD,KAAK,UAAU,QAAQA,CAAQ,IAAM,KACvC,KAAK,UAAY,KAAK,UAAU,OAAO,SAAUlI,EAAG,CAClD,OAAOA,IAAMkI,CACf,CAAC,EAEI,KAAK,UAAU,SAGd,KAAK,UACH,KAAK,QAAQ,uBAAyB,KAAK,oBAC7C,KAAK,QAAQ,OAAO,CAClB,OAAQ,EACtB,CAAa,EAED,KAAK,QAAQ,YAAW,GAIxB,KAAK,UACP,KAAK,WAAU,EAEf,KAAK,MAAM,OAAO,IAAI,GAI1B,KAAK,MAAM,OAAO,CAChB,KAAM,kBACN,MAAO,KACP,SAAUA,CAClB,CAAO,EAEL,EAEAtI,EAAO,kBAAoB,UAA6B,CACtD,OAAO,KAAK,UAAU,MACxB,EAEAA,EAAO,WAAa,UAAsB,CACnC,KAAK,MAAM,eACd,KAAK,SAAS,CACZ,KAAM,YACd,CAAO,CAEL,EAEAA,EAAO,MAAQ,SAAe4C,EAAS6F,EAAc,CACnD,IAAI1D,EAAS,KACT2D,EACAC,EACAC,EAEJ,GAAI,KAAK,MAAM,YACb,GAAI,KAAK,MAAM,gBAAkBH,GAAgB,MAAgBA,EAAa,eAE5E,KAAK,OAAO,CACV,OAAQ,EAClB,CAAS,UACQ,KAAK,QAAS,CACvB,IAAII,EAGJ,OAACA,EAAiB,KAAK,UAAY,MAAgBA,EAAe,gBAE3D,KAAK,OACd,EAUF,GANIjG,GACF,KAAK,WAAWA,CAAO,EAKrB,CAAC,KAAK,QAAQ,QAAS,CACzB,IAAI0F,EAAW,KAAK,UAAU,KAAK,SAAUlI,EAAG,CAC9C,OAAOA,EAAE,QAAQ,OACnB,CAAC,EAEGkI,GACF,KAAK,WAAWA,EAAS,OAAO,CAEpC,CAEA,IAAInG,EAAWrB,GAAoB,KAAK,QAAQ,EAC5CgI,EAAkBvE,KAElBwE,EAAiB,CACnB,SAAU5G,EACV,UAAW,OACX,KAAM,KAAK,IACjB,EACI,OAAO,eAAe4G,EAAgB,SAAU,CAC9C,WAAY,GACZ,IAAK,UAAe,CAClB,GAAID,EACF,OAAA/D,EAAO,oBAAsB,GACtB+D,EAAgB,MAI3B,CACN,CAAK,EAED,IAAIE,EAAU,UAAmB,CAC/B,OAAKjE,EAAO,QAAQ,SAIpBA,EAAO,oBAAsB,GACtBA,EAAO,QAAQ,QAAQgE,CAAc,GAJnC,QAAQ,OAAO,iBAAiB,CAK3C,EAGIE,EAAU,CACZ,aAAcR,EACd,QAAS,KAAK,QACd,SAAUtG,EACV,MAAO,KAAK,MACZ,QAAS6G,EACT,KAAM,KAAK,IACjB,EAEI,IAAKN,EAAwB,KAAK,QAAQ,WAAa,MAAgBA,EAAsB,QAAS,CACpG,IAAIQ,GAEHA,EAAyB,KAAK,QAAQ,WAAa,MAAgBA,EAAuB,QAAQD,CAAO,CAC5G,CAKA,GAFA,KAAK,YAAc,KAAK,MAEpB,CAAC,KAAK,MAAM,YAAc,KAAK,MAAM,cAAgBN,EAAwBM,EAAQ,eAAiB,KAAO,OAASN,EAAsB,MAAO,CACrJ,IAAIQ,EAEJ,KAAK,SAAS,CACZ,KAAM,QACN,MAAOA,EAAyBF,EAAQ,eAAiB,KAAO,OAASE,EAAuB,IACxG,CAAO,CACH,CAGA,YAAK,QAAU,IAAIxD,GAAQ,CACzB,GAAIsD,EAAQ,QACZ,MAAOH,GAAmB,OAAiBF,EAAwBE,EAAgB,QAAU,KAA5D,OAA4EF,EAAsB,KAAKE,CAAe,EACvJ,UAAW,SAAmBb,EAAM,CAClClD,EAAO,QAAQkD,CAAI,EAGnBlD,EAAO,MAAM,OAAO,WAAa,MAAgBA,EAAO,MAAM,OAAO,UAAUkD,EAAMlD,CAAM,EAEvFA,EAAO,YAAc,GACvBA,EAAO,eAAc,CAEzB,EACA,QAAS,SAAiBT,EAAO,CAEzBoB,GAAiBpB,CAAK,GAAKA,EAAM,QACrCS,EAAO,SAAS,CACd,KAAM,QACN,MAAOT,CACnB,CAAW,EAGEoB,GAAiBpB,CAAK,IAEzBS,EAAO,MAAM,OAAO,SAAW,MAAgBA,EAAO,MAAM,OAAO,QAAQT,EAAOS,CAAM,EAExF0C,GAAS,EAAG,MAAMnD,CAAK,GAIrBS,EAAO,YAAc,GACvBA,EAAO,eAAc,CAEzB,EACA,OAAQ,UAAkB,CACxBA,EAAO,SAAS,CACd,KAAM,QAChB,CAAS,CACH,EACA,QAAS,UAAmB,CAC1BA,EAAO,SAAS,CACd,KAAM,OAChB,CAAS,CACH,EACA,WAAY,UAAsB,CAChCA,EAAO,SAAS,CACd,KAAM,UAChB,CAAS,CACH,EACA,MAAOkE,EAAQ,QAAQ,MACvB,WAAYA,EAAQ,QAAQ,UAClC,CAAK,EACD,KAAK,QAAU,KAAK,QAAQ,QACrB,KAAK,OACd,EAEAjJ,EAAO,SAAW,SAAkBoJ,EAAQ,CAC1C,IAAIhC,EAAS,KAEb,KAAK,MAAQ,KAAK,QAAQ,KAAK,MAAOgC,CAAM,EAC5C7B,EAAc,MAAM,UAAY,CAC9BH,EAAO,UAAU,QAAQ,SAAUkB,EAAU,CAC3CA,EAAS,cAAcc,CAAM,CAC/B,CAAC,EAEDhC,EAAO,MAAM,OAAO,CAClB,MAAOA,EACP,KAAM,eACN,OAAQgC,CAChB,CAAO,CACH,CAAC,CACH,EAEApJ,EAAO,gBAAkB,SAAyB4C,EAAS,CACzD,IAAIqF,EAAO,OAAOrF,EAAQ,aAAgB,WAAaA,EAAQ,cAAgBA,EAAQ,YACnFyG,EAAiB,OAAOzG,EAAQ,YAAgB,IAChD0G,EAAuBD,EAAiB,OAAOzG,EAAQ,sBAAyB,WAAaA,EAAQ,qBAAoB,EAAKA,EAAQ,qBAAuB,EAC7J2G,EAAU,OAAOtB,EAAS,IAC9B,MAAO,CACL,KAAMA,EACN,gBAAiB,EACjB,cAAesB,EAAUD,GAAsD,KAAK,IAAG,EAAK,EAC5F,MAAO,KACP,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,EACnB,UAAW,KACX,WAAY,GACZ,cAAe,GACf,SAAU,GACV,OAAQC,EAAU,UAAY,MACpC,CACE,EAEAvJ,EAAO,QAAU,SAAiBkI,EAAOkB,EAAQ,CAC/C,IAAII,EAAcC,EAElB,OAAQL,EAAO,KAAI,CACjB,IAAK,SACH,OAAO/I,EAAS,GAAI6H,EAAO,CACzB,kBAAmBA,EAAM,kBAAoB,CACvD,CAAS,EAEH,IAAK,QACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,SAAU,EACpB,CAAS,EAEH,IAAK,WACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,SAAU,EACpB,CAAS,EAEH,IAAK,QACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,kBAAmB,EACnB,WAAYsB,EAAeJ,EAAO,OAAS,KAAOI,EAAe,KACjE,WAAY,GACZ,SAAU,EACpB,EAAW,CAACtB,EAAM,eAAiB,CACzB,MAAO,KACP,OAAQ,SAClB,CAAS,EAEH,IAAK,UACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,KAAMkB,EAAO,KACb,gBAAiBlB,EAAM,gBAAkB,EACzC,eAAgBuB,EAAwBL,EAAO,gBAAkB,KAAOK,EAAwB,KAAK,IAAG,EACxG,MAAO,KACP,kBAAmB,EACnB,WAAY,GACZ,cAAe,GACf,SAAU,GACV,OAAQ,SAClB,CAAS,EAEH,IAAK,QACH,IAAInF,EAAQ8E,EAAO,MAEnB,OAAI1D,GAAiBpB,CAAK,GAAKA,EAAM,QAAU,KAAK,YAC3CjE,EAAS,GAAI,KAAK,WAAW,EAG/BA,EAAS,GAAI6H,EAAO,CACzB,MAAO5D,EACP,iBAAkB4D,EAAM,iBAAmB,EAC3C,eAAgB,KAAK,IAAG,EACxB,kBAAmBA,EAAM,kBAAoB,EAC7C,WAAY,GACZ,SAAU,GACV,OAAQ,OAClB,CAAS,EAEH,IAAK,aACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,cAAe,EACzB,CAAS,EAEH,IAAK,WACH,OAAO7H,EAAS,GAAI6H,EAAOkB,EAAO,KAAK,EAEzC,QACE,OAAOlB,CACf,CACE,EAEON,CACT,EAAC,EC7eU8B,GAA0B,SAAUjF,EAAe,CAC5D7E,GAAe8J,EAAYjF,CAAa,EAExC,SAASiF,EAAW9D,EAAQ,CAC1B,IAAI1F,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KACpCvE,EAAM,OAAS0F,GAAU,GACzB1F,EAAM,QAAU,GAChBA,EAAM,WAAa,GACZA,CACT,CAEA,IAAIF,EAAS0J,EAAW,UAExB,OAAA1J,EAAO,MAAQ,SAAeP,EAAQmD,EAASsF,EAAO,CACpD,IAAIyB,EAEAxH,EAAWS,EAAQ,SACnBgH,GAAaD,EAAqB/G,EAAQ,YAAc,KAAO+G,EAAqBtH,GAAsBF,EAAUS,CAAO,EAC3Hb,EAAQ,KAAK,IAAI6H,CAAS,EAE9B,OAAK7H,IACHA,EAAQ,IAAI6F,GAAM,CAChB,MAAO,KACP,SAAUzF,EACV,UAAWyH,EACX,QAASnK,EAAO,oBAAoBmD,CAAO,EAC3C,MAAOsF,EACP,eAAgBzI,EAAO,iBAAiB0C,CAAQ,EAChD,KAAMS,EAAQ,IACtB,CAAO,EACD,KAAK,IAAIb,CAAK,GAGTA,CACT,EAEA/B,EAAO,IAAM,SAAa+B,EAAO,CAC1B,KAAK,WAAWA,EAAM,SAAS,IAClC,KAAK,WAAWA,EAAM,SAAS,EAAIA,EACnC,KAAK,QAAQ,KAAKA,CAAK,EACvB,KAAK,OAAO,CACV,KAAM,aACN,MAAOA,CACf,CAAO,EAEL,EAEA/B,EAAO,OAAS,SAAgB+B,EAAO,CACrC,IAAI8H,EAAa,KAAK,WAAW9H,EAAM,SAAS,EAE5C8H,IACF9H,EAAM,QAAO,EACb,KAAK,QAAU,KAAK,QAAQ,OAAO,SAAU3B,EAAG,CAC9C,OAAOA,IAAM2B,CACf,CAAC,EAEG8H,IAAe9H,GACjB,OAAO,KAAK,WAAWA,EAAM,SAAS,EAGxC,KAAK,OAAO,CACV,KAAM,eACN,MAAOA,CACf,CAAO,EAEL,EAEA/B,EAAO,MAAQ,UAAiB,CAC9B,IAAI+E,EAAS,KAEbwC,EAAc,MAAM,UAAY,CAC9BxC,EAAO,QAAQ,QAAQ,SAAUhD,EAAO,CACtCgD,EAAO,OAAOhD,CAAK,CACrB,CAAC,CACH,CAAC,CACH,EAEA/B,EAAO,IAAM,SAAa4J,EAAW,CACnC,OAAO,KAAK,WAAWA,CAAS,CAClC,EAEA5J,EAAO,OAAS,UAAkB,CAChC,OAAO,KAAK,OACd,EAEAA,EAAO,KAAO,SAAcmB,EAAMC,EAAM,CACtC,IAAI0I,EAAmBtI,EAAgBL,EAAMC,CAAI,EAC7CU,EAAUgI,EAAiB,CAAC,EAEhC,OAAI,OAAOhI,EAAQ,MAAU,MAC3BA,EAAQ,MAAQ,IAGX,KAAK,QAAQ,KAAK,SAAUC,EAAO,CACxC,OAAOF,GAAWC,EAASC,CAAK,CAClC,CAAC,CACH,EAEA/B,EAAO,QAAU,SAAiBmB,EAAMC,EAAM,CAC5C,IAAI2I,EAAoBvI,EAAgBL,EAAMC,CAAI,EAC9CU,EAAUiI,EAAkB,CAAC,EAEjC,OAAO,OAAO,KAAKjI,CAAO,EAAE,OAAS,EAAI,KAAK,QAAQ,OAAO,SAAUC,EAAO,CAC5E,OAAOF,GAAWC,EAASC,CAAK,CAClC,CAAC,EAAI,KAAK,OACZ,EAEA/B,EAAO,OAAS,SAAgBgK,EAAO,CACrC,IAAI5C,EAAS,KAEbG,EAAc,MAAM,UAAY,CAC9BH,EAAO,UAAU,QAAQ,SAAUnH,EAAU,CAC3CA,EAAS+J,CAAK,CAChB,CAAC,CACH,CAAC,CACH,EAEAhK,EAAO,QAAU,UAAmB,CAClC,IAAIiK,EAAS,KAEb1C,EAAc,MAAM,UAAY,CAC9B0C,EAAO,QAAQ,QAAQ,SAAUlI,EAAO,CACtCA,EAAM,QAAO,CACf,CAAC,CACH,CAAC,CACH,EAEA/B,EAAO,SAAW,UAAoB,CACpC,IAAIkK,EAAS,KAEb3C,EAAc,MAAM,UAAY,CAC9B2C,EAAO,QAAQ,QAAQ,SAAUnI,EAAO,CACtCA,EAAM,SAAQ,CAChB,CAAC,CACH,CAAC,CACH,EAEO2H,CACT,EAAE3J,EAAY,EC3IHoK,GAAwB,UAAY,CAC7C,SAASA,EAASvE,EAAQ,CACxB,KAAK,QAAUvF,EAAS,GAAIuF,EAAO,eAAgBA,EAAO,OAAO,EACjE,KAAK,WAAaA,EAAO,WACzB,KAAK,cAAgBA,EAAO,cAC5B,KAAK,UAAY,GACjB,KAAK,MAAQA,EAAO,OAASwE,GAAe,EAC5C,KAAK,KAAOxE,EAAO,IACrB,CAEA,IAAI5F,EAASmK,EAAS,UAEtB,OAAAnK,EAAO,SAAW,SAAkBkI,EAAO,CACzC,KAAK,SAAS,CACZ,KAAM,WACN,MAAOA,CACb,CAAK,CACH,EAEAlI,EAAO,YAAc,SAAqBsI,EAAU,CAC9C,KAAK,UAAU,QAAQA,CAAQ,IAAM,IACvC,KAAK,UAAU,KAAKA,CAAQ,CAEhC,EAEAtI,EAAO,eAAiB,SAAwBsI,EAAU,CACxD,KAAK,UAAY,KAAK,UAAU,OAAO,SAAUlI,EAAG,CAClD,OAAOA,IAAMkI,CACf,CAAC,CACH,EAEAtI,EAAO,OAAS,UAAkB,CAChC,OAAI,KAAK,SACP,KAAK,QAAQ,OAAM,EACZ,KAAK,QAAQ,QAAQ,KAAKQ,CAAI,EAAE,MAAMA,CAAI,GAG5C,QAAQ,QAAO,CACxB,EAEAR,EAAO,SAAW,UAAqB,CACrC,OAAI,KAAK,SACP,KAAK,QAAQ,SAAQ,EACd,KAAK,QAAQ,SAGf,KAAK,QAAO,CACrB,EAEAA,EAAO,QAAU,UAAmB,CAClC,IAAIE,EAAQ,KAER+H,EACAoC,EAAW,KAAK,MAAM,SAAW,UACjChC,EAAU,QAAQ,QAAO,EAE7B,OAAKgC,IACH,KAAK,SAAS,CACZ,KAAM,UACN,UAAW,KAAK,QAAQ,SAChC,CAAO,EACDhC,EAAUA,EAAQ,KAAK,UAAY,CAEjCnI,EAAM,cAAc,OAAO,UAAY,MAAgBA,EAAM,cAAc,OAAO,SAASA,EAAM,MAAM,UAAWA,CAAK,CACzH,CAAC,EAAE,KAAK,UAAY,CAClB,OAAOA,EAAM,QAAQ,UAAY,KAAO,OAASA,EAAM,QAAQ,SAASA,EAAM,MAAM,SAAS,CAC/F,CAAC,EAAE,KAAK,SAAU+I,EAAS,CACrBA,IAAY/I,EAAM,MAAM,SAC1BA,EAAM,SAAS,CACb,KAAM,UACN,QAAS+I,EACT,UAAW/I,EAAM,MAAM,SACnC,CAAW,CAEL,CAAC,GAGImI,EAAQ,KAAK,UAAY,CAC9B,OAAOnI,EAAM,gBAAe,CAC9B,CAAC,EAAE,KAAK,SAAUiD,EAAQ,CACxB8E,EAAO9E,EAEPjD,EAAM,cAAc,OAAO,WAAa,MAAgBA,EAAM,cAAc,OAAO,UAAU+H,EAAM/H,EAAM,MAAM,UAAWA,EAAM,MAAM,QAASA,CAAK,CACtJ,CAAC,EAAE,KAAK,UAAY,CAClB,OAAOA,EAAM,QAAQ,WAAa,KAAO,OAASA,EAAM,QAAQ,UAAU+H,EAAM/H,EAAM,MAAM,UAAWA,EAAM,MAAM,OAAO,CAC5H,CAAC,EAAE,KAAK,UAAY,CAClB,OAAOA,EAAM,QAAQ,WAAa,KAAO,OAASA,EAAM,QAAQ,UAAU+H,EAAM,KAAM/H,EAAM,MAAM,UAAWA,EAAM,MAAM,OAAO,CAClI,CAAC,EAAE,KAAK,UAAY,CAClB,OAAAA,EAAM,SAAS,CACb,KAAM,UACN,KAAM+H,CACd,CAAO,EAEMA,CACT,CAAC,EAAE,MAAM,SAAU3D,EAAO,CAExB,OAAApE,EAAM,cAAc,OAAO,SAAW,MAAgBA,EAAM,cAAc,OAAO,QAAQoE,EAAOpE,EAAM,MAAM,UAAWA,EAAM,MAAM,QAASA,CAAK,EAEjJuH,GAAS,EAAG,MAAMnD,CAAK,EAChB,QAAQ,UAAU,KAAK,UAAY,CACxC,OAAOpE,EAAM,QAAQ,SAAW,KAAO,OAASA,EAAM,QAAQ,QAAQoE,EAAOpE,EAAM,MAAM,UAAWA,EAAM,MAAM,OAAO,CACzH,CAAC,EAAE,KAAK,UAAY,CAClB,OAAOA,EAAM,QAAQ,WAAa,KAAO,OAASA,EAAM,QAAQ,UAAU,OAAWoE,EAAOpE,EAAM,MAAM,UAAWA,EAAM,MAAM,OAAO,CACxI,CAAC,EAAE,KAAK,UAAY,CAClB,MAAAA,EAAM,SAAS,CACb,KAAM,QACN,MAAOoE,CACjB,CAAS,EAEKA,CACR,CAAC,CACH,CAAC,CACH,EAEAtE,EAAO,gBAAkB,UAA2B,CAClD,IAAI+E,EAAS,KACTuF,EAEJ,YAAK,QAAU,IAAI3E,GAAQ,CACzB,GAAI,UAAc,CAChB,OAAKZ,EAAO,QAAQ,WAIbA,EAAO,QAAQ,WAAWA,EAAO,MAAM,SAAS,EAH9C,QAAQ,OAAO,qBAAqB,CAI/C,EACA,OAAQ,UAAkB,CACxBA,EAAO,SAAS,CACd,KAAM,QAChB,CAAS,CACH,EACA,QAAS,UAAmB,CAC1BA,EAAO,SAAS,CACd,KAAM,OAChB,CAAS,CACH,EACA,WAAY,UAAsB,CAChCA,EAAO,SAAS,CACd,KAAM,UAChB,CAAS,CACH,EACA,OAAQuF,EAAsB,KAAK,QAAQ,QAAU,KAAOA,EAAsB,EAClF,WAAY,KAAK,QAAQ,UAC/B,CAAK,EACM,KAAK,QAAQ,OACtB,EAEAtK,EAAO,SAAW,SAAkBoJ,EAAQ,CAC1C,IAAIhC,EAAS,KAEb,KAAK,MAAQmD,GAAQ,KAAK,MAAOnB,CAAM,EACvC7B,EAAc,MAAM,UAAY,CAC9BH,EAAO,UAAU,QAAQ,SAAUkB,EAAU,CAC3CA,EAAS,iBAAiBc,CAAM,CAClC,CAAC,EAEDhC,EAAO,cAAc,OAAOA,CAAM,CACpC,CAAC,CACH,EAEO+C,CACT,EAAC,EACM,SAASC,IAAkB,CAChC,MAAO,CACL,QAAS,OACT,KAAM,OACN,MAAO,KACP,aAAc,EACd,SAAU,GACV,OAAQ,OACR,UAAW,MACf,CACA,CAEA,SAASG,GAAQrC,EAAOkB,EAAQ,CAC9B,OAAQA,EAAO,KAAI,CACjB,IAAK,SACH,OAAO/I,EAAS,GAAI6H,EAAO,CACzB,aAAcA,EAAM,aAAe,CAC3C,CAAO,EAEH,IAAK,QACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,SAAU,EAClB,CAAO,EAEH,IAAK,WACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,SAAU,EAClB,CAAO,EAEH,IAAK,UACH,OAAO7H,EAAS,GAAI6H,EAAO,CACzB,QAASkB,EAAO,QAChB,KAAM,OACN,MAAO,KACP,SAAU,GACV,OAAQ,UACR,UAAWA,EAAO,SAC1B,CAAO,EAEH,IAAK,UACH,OAAO/I,EAAS,GAAI6H,EAAO,CACzB,KAAMkB,EAAO,KACb,MAAO,KACP,OAAQ,UACR,SAAU,EAClB,CAAO,EAEH,IAAK,QACH,OAAO/I,EAAS,GAAI6H,EAAO,CACzB,KAAM,OACN,MAAOkB,EAAO,MACd,aAAclB,EAAM,aAAe,EACnC,SAAU,GACV,OAAQ,OAChB,CAAO,EAEH,IAAK,WACH,OAAO7H,EAAS,GAAI6H,EAAOkB,EAAO,KAAK,EAEzC,QACE,OAAOlB,CACb,CACA,CChOO,IAAIsC,GAA6B,SAAU/F,EAAe,CAC/D7E,GAAe4K,EAAe/F,CAAa,EAE3C,SAAS+F,EAAc5E,EAAQ,CAC7B,IAAI1F,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KACpCvE,EAAM,OAAS0F,GAAU,GACzB1F,EAAM,UAAY,GAClBA,EAAM,WAAa,EACZA,CACT,CAEA,IAAIF,EAASwK,EAAc,UAE3B,OAAAxK,EAAO,MAAQ,SAAeP,EAAQmD,EAASsF,EAAO,CACpD,IAAIzF,EAAW,IAAI0H,GAAS,CAC1B,cAAe,KACf,WAAY,EAAE,KAAK,WACnB,QAAS1K,EAAO,uBAAuBmD,CAAO,EAC9C,MAAOsF,EACP,eAAgBtF,EAAQ,YAAcnD,EAAO,oBAAoBmD,EAAQ,WAAW,EAAI,OACxF,KAAMA,EAAQ,IACpB,CAAK,EACD,YAAK,IAAIH,CAAQ,EACVA,CACT,EAEAzC,EAAO,IAAM,SAAayC,EAAU,CAClC,KAAK,UAAU,KAAKA,CAAQ,EAC5B,KAAK,OAAOA,CAAQ,CACtB,EAEAzC,EAAO,OAAS,SAAgByC,EAAU,CACxC,KAAK,UAAY,KAAK,UAAU,OAAO,SAAUrC,EAAG,CAClD,OAAOA,IAAMqC,CACf,CAAC,EACDA,EAAS,OAAM,EACf,KAAK,OAAOA,CAAQ,CACtB,EAEAzC,EAAO,MAAQ,UAAiB,CAC9B,IAAI+E,EAAS,KAEbwC,EAAc,MAAM,UAAY,CAC9BxC,EAAO,UAAU,QAAQ,SAAUtC,EAAU,CAC3CsC,EAAO,OAAOtC,CAAQ,CACxB,CAAC,CACH,CAAC,CACH,EAEAzC,EAAO,OAAS,UAAkB,CAChC,OAAO,KAAK,SACd,EAEAA,EAAO,KAAO,SAAc8B,EAAS,CACnC,OAAI,OAAOA,EAAQ,MAAU,MAC3BA,EAAQ,MAAQ,IAGX,KAAK,UAAU,KAAK,SAAUW,EAAU,CAC7C,OAAOD,GAAcV,EAASW,CAAQ,CACxC,CAAC,CACH,EAEAzC,EAAO,QAAU,SAAiB8B,EAAS,CACzC,OAAO,KAAK,UAAU,OAAO,SAAUW,EAAU,CAC/C,OAAOD,GAAcV,EAASW,CAAQ,CACxC,CAAC,CACH,EAEAzC,EAAO,OAAS,SAAgByC,EAAU,CACxC,IAAI2E,EAAS,KAEbG,EAAc,MAAM,UAAY,CAC9BH,EAAO,UAAU,QAAQ,SAAUnH,EAAU,CAC3CA,EAASwC,CAAQ,CACnB,CAAC,CACH,CAAC,CACH,EAEAzC,EAAO,QAAU,UAAmB,CAClC,KAAK,sBAAqB,CAC5B,EAEAA,EAAO,SAAW,UAAoB,CACpC,KAAK,sBAAqB,CAC5B,EAEAA,EAAO,sBAAwB,UAAiC,CAC9D,IAAIyK,EAAkB,KAAK,UAAU,OAAO,SAAUrK,EAAG,CACvD,OAAOA,EAAE,MAAM,QACjB,CAAC,EACD,OAAOmH,EAAc,MAAM,UAAY,CACrC,OAAOkD,EAAgB,OAAO,SAAUpC,EAAS5F,EAAU,CACzD,OAAO4F,EAAQ,KAAK,UAAY,CAC9B,OAAO5F,EAAS,WAAW,MAAMjC,CAAI,CACvC,CAAC,CACH,EAAG,QAAQ,SAAS,CACtB,CAAC,CACH,EAEOgK,CACT,EAAEzK,EAAY,EC5GP,SAAS2K,IAAwB,CACtC,MAAO,CACL,QAAS,SAAiBzB,EAAS,CACjCA,EAAQ,QAAU,UAAY,CAC5B,IAAIN,EAAuBQ,EAAwBwB,EAAwBC,EAAwBC,EAAqBC,EAEpHC,GAAepC,EAAwBM,EAAQ,eAAiB,OAAiBE,EAAyBR,EAAsB,OAAS,KAAlE,OAAkFQ,EAAuB,YAChL6B,GAAaL,EAAyB1B,EAAQ,eAAiB,OAAiB2B,EAAyBD,EAAuB,OAAS,KAAnE,OAAmFC,EAAuB,UAChLK,EAAYD,GAAa,KAAO,OAASA,EAAU,UACnDE,GAAsBF,GAAa,KAAO,OAASA,EAAU,aAAe,UAC5EG,GAA0BH,GAAa,KAAO,OAASA,EAAU,aAAe,WAChFI,IAAaP,EAAsB5B,EAAQ,MAAM,OAAS,KAAO,OAAS4B,EAAoB,QAAU,GACxGQ,IAAkBP,EAAuB7B,EAAQ,MAAM,OAAS,KAAO,OAAS6B,EAAqB,aAAe,GACpHhC,EAAkBvE,GAAkB,EACpC+G,EAAcxC,GAAmB,KAAO,OAASA,EAAgB,OACjEyC,EAAgBF,EAChBG,EAAY,GAEZC,EAAUxC,EAAQ,QAAQ,SAAW,UAAY,CACnD,OAAO,QAAQ,OAAO,iBAAiB,CACzC,EAEIyC,EAAgB,SAAuBC,EAAOC,EAAOC,EAAMC,GAAU,CACvE,OAAAP,EAAgBO,GAAW,CAACF,CAAK,EAAE,OAAOL,CAAa,EAAI,GAAG,OAAOA,EAAe,CAACK,CAAK,CAAC,EACpFE,GAAW,CAACD,CAAI,EAAE,OAAOF,CAAK,EAAI,GAAG,OAAOA,EAAO,CAACE,CAAI,CAAC,CAClE,EAGIE,EAAY,SAAmBJ,EAAOK,EAAQJ,EAAOE,GAAU,CACjE,GAAIN,EACF,OAAO,QAAQ,OAAO,WAAW,EAGnC,GAAI,OAAOI,EAAU,KAAe,CAACI,GAAUL,EAAM,OACnD,OAAO,QAAQ,QAAQA,CAAK,EAG9B,IAAI5C,EAAiB,CACnB,SAAUE,EAAQ,SAClB,OAAQqC,EACR,UAAWM,EACX,KAAM3C,EAAQ,IAC1B,EACcgD,EAAgBR,EAAQ1C,CAAc,EACtCV,GAAU,QAAQ,QAAQ4D,CAAa,EAAE,KAAK,SAAUJ,GAAM,CAChE,OAAOH,EAAcC,EAAOC,EAAOC,GAAMC,EAAQ,CACnD,CAAC,EAED,GAAItG,GAAayG,CAAa,EAAG,CAC/B,IAAIC,GAAe7D,GACnB6D,GAAa,OAASD,EAAc,MACtC,CAEA,OAAO5D,EACT,EAEIA,EAEJ,GAAI,CAAC+C,EAAS,OACZ/C,EAAU0D,EAAU,EAAE,UAEfb,EAAoB,CACzB,IAAIc,GAAS,OAAOf,EAAc,IAC9BW,GAAQI,GAASf,EAAYkB,GAAiBlD,EAAQ,QAASmC,CAAQ,EAC3E/C,EAAU0D,EAAUX,EAAUY,GAAQJ,EAAK,CAC7C,SACST,EAAwB,CAC7B,IAAIiB,GAAU,OAAOnB,EAAc,IAE/BoB,EAASD,GAAUnB,EAAYqB,GAAqBrD,EAAQ,QAASmC,CAAQ,EAEjF/C,EAAU0D,EAAUX,EAAUgB,GAASC,EAAQ,EAAI,CACrD,MAEK,UAAY,CACXd,EAAgB,GAChB,IAAIS,EAAS,OAAO/C,EAAQ,QAAQ,iBAAqB,IACrDsD,EAAuBxB,GAAeK,EAAS,CAAC,EAAIL,EAAYK,EAAS,CAAC,EAAG,EAAGA,CAAQ,EAAI,GAEhG/C,EAAUkE,EAAuBR,EAAU,GAAIC,EAAQX,EAAc,CAAC,CAAC,EAAI,QAAQ,QAAQK,EAAc,GAAIL,EAAc,CAAC,EAAGD,EAAS,CAAC,CAAC,CAAC,EAgB3I,QAdIoB,EAAQ,SAAe3I,EAAG,CAC5BwE,EAAUA,EAAQ,KAAK,SAAUsD,EAAO,CACtC,IAAIc,GAAsB1B,GAAeK,EAASvH,CAAC,EAAIkH,EAAYK,EAASvH,CAAC,EAAGA,EAAGuH,CAAQ,EAAI,GAE/F,GAAIqB,GAAqB,CACvB,IAAIC,GAAUV,EAASX,EAAcxH,CAAC,EAAIsI,GAAiBlD,EAAQ,QAAS0C,CAAK,EAEjF,OAAOI,EAAUJ,EAAOK,EAAQU,EAAO,CACzC,CAEA,OAAO,QAAQ,QAAQhB,EAAcC,EAAON,EAAcxH,CAAC,EAAGuH,EAASvH,CAAC,CAAC,CAAC,CAC5E,CAAC,CACH,EAESA,EAAI,EAAGA,EAAIuH,EAAS,OAAQvH,IACnC2I,EAAM3I,CAAC,CAEX,GAAC,EAGT,IAAI8I,GAAetE,EAAQ,KAAK,SAAUsD,EAAO,CAC/C,MAAO,CACL,MAAOA,EACP,WAAYJ,CACxB,CACQ,CAAC,EACGqB,EAAoBD,GAExB,OAAAC,EAAkB,OAAS,UAAY,CACrCpB,EAAY,GACZ1C,GAAmB,MAAgBA,EAAgB,MAAK,EAEpDtD,GAAa6C,CAAO,GACtBA,EAAQ,OAAM,CAElB,EAEOsE,EACT,CACF,CACJ,CACA,CACO,SAASR,GAAiBvJ,EAAS+I,EAAO,CAC/C,OAAO/I,EAAQ,kBAAoB,KAAO,OAASA,EAAQ,iBAAiB+I,EAAMA,EAAM,OAAS,CAAC,EAAGA,CAAK,CAC5G,CACO,SAASW,GAAqB1J,EAAS+I,EAAO,CACnD,OAAO/I,EAAQ,sBAAwB,KAAO,OAASA,EAAQ,qBAAqB+I,EAAM,CAAC,EAAGA,CAAK,CACrG,CCzHO,IAAIkB,GAA2B,UAAY,CAChD,SAASA,EAAYjH,EAAQ,CACvBA,IAAW,SACbA,EAAS,IAGX,KAAK,WAAaA,EAAO,YAAc,IAAI8D,GAC3C,KAAK,cAAgB9D,EAAO,eAAiB,IAAI4E,GACjD,KAAK,eAAiB5E,EAAO,gBAAkB,GAC/C,KAAK,cAAgB,GACrB,KAAK,iBAAmB,EAC1B,CAEA,IAAI5F,EAAS6M,EAAY,UAEzB,OAAA7M,EAAO,MAAQ,UAAiB,CAC9B,IAAIE,EAAQ,KAEZ,KAAK,iBAAmB+E,GAAa,UAAU,UAAY,CACrDA,GAAa,UAAS,GAAMI,GAAc,SAAQ,IACpDnF,EAAM,cAAc,QAAO,EAE3BA,EAAM,WAAW,QAAO,EAE5B,CAAC,EACD,KAAK,kBAAoBmF,GAAc,UAAU,UAAY,CACvDJ,GAAa,UAAS,GAAMI,GAAc,SAAQ,IACpDnF,EAAM,cAAc,SAAQ,EAE5BA,EAAM,WAAW,SAAQ,EAE7B,CAAC,CACH,EAEAF,EAAO,QAAU,UAAmB,CAClC,IAAI8M,EAAuBC,GAE1BD,EAAwB,KAAK,mBAAqB,MAAgBA,EAAsB,KAAK,IAAI,GACjGC,EAAwB,KAAK,oBAAsB,MAAgBA,EAAsB,KAAK,IAAI,CACrG,EAEA/M,EAAO,WAAa,SAAoBmB,EAAMC,EAAM,CAClD,IAAI0I,EAAmBtI,EAAgBL,EAAMC,CAAI,EAC7CU,EAAUgI,EAAiB,CAAC,EAEhC,OAAAhI,EAAQ,SAAW,GACZ,KAAK,WAAW,QAAQA,CAAO,EAAE,MAC1C,EAEA9B,EAAO,WAAa,SAAoB8B,EAAS,CAC/C,OAAO,KAAK,cAAc,QAAQzB,EAAS,GAAIyB,EAAS,CACtD,SAAU,EAChB,CAAK,CAAC,EAAE,MACN,EAEA9B,EAAO,aAAe,SAAsBmC,EAAUL,EAAS,CAC7D,IAAIkL,EAEJ,OAAQA,EAAwB,KAAK,WAAW,KAAK7K,EAAUL,CAAO,IAAM,KAAO,OAASkL,EAAsB,MAAM,IAC1H,EAEAhN,EAAO,eAAiB,SAAwBiN,EAAmB,CACjE,OAAO,KAAK,gBAAgB,QAAQA,CAAiB,EAAE,IAAI,SAAUC,EAAM,CACzE,IAAI/K,EAAW+K,EAAK,SAChBhF,EAAQgF,EAAK,MACbjF,EAAOC,EAAM,KACjB,MAAO,CAAC/F,EAAU8F,CAAI,CACxB,CAAC,CACH,EAEAjI,EAAO,aAAe,SAAsBmC,EAAUzB,EAASkC,EAAS,CACtE,IAAIuK,EAAgBjM,GAAeiB,CAAQ,EACvCiL,EAAmB,KAAK,oBAAoBD,CAAa,EAC7D,OAAO,KAAK,WAAW,MAAM,KAAMC,CAAgB,EAAE,QAAQ1M,EAASkC,CAAO,CAC/E,EAEA5C,EAAO,eAAiB,SAAwBiN,EAAmBvM,EAASkC,EAAS,CACnF,IAAImC,EAAS,KAEb,OAAOwC,EAAc,MAAM,UAAY,CACrC,OAAOxC,EAAO,gBAAgB,QAAQkI,CAAiB,EAAE,IAAI,SAAUI,EAAO,CAC5E,IAAIlL,EAAWkL,EAAM,SACrB,MAAO,CAAClL,EAAU4C,EAAO,aAAa5C,EAAUzB,EAASkC,CAAO,CAAC,CACnE,CAAC,CACH,CAAC,CACH,EAEA5C,EAAO,cAAgB,SAAuBmC,EAAUL,EAAS,CAC/D,IAAIwL,EAEJ,OAAQA,EAAyB,KAAK,WAAW,KAAKnL,EAAUL,CAAO,IAAM,KAAO,OAASwL,EAAuB,KACtH,EAEAtN,EAAO,cAAgB,SAAuBmB,EAAMC,EAAM,CACxD,IAAI2I,EAAoBvI,EAAgBL,EAAMC,CAAI,EAC9CU,EAAUiI,EAAkB,CAAC,EAE7BwD,EAAa,KAAK,WACtBhG,EAAc,MAAM,UAAY,CAC9BgG,EAAW,QAAQzL,CAAO,EAAE,QAAQ,SAAUC,EAAO,CACnDwL,EAAW,OAAOxL,CAAK,CACzB,CAAC,CACH,CAAC,CACH,EAEA/B,EAAO,aAAe,SAAsBmB,EAAMC,EAAMC,EAAM,CAC5D,IAAI+F,EAAS,KAEToG,EAAoBhM,EAAgBL,EAAMC,EAAMC,CAAI,EACpDS,EAAU0L,EAAkB,CAAC,EAC7B5K,EAAU4K,EAAkB,CAAC,EAE7BD,EAAa,KAAK,WAElBE,EAAiBpN,EAAS,GAAIyB,EAAS,CACzC,OAAQ,EACd,CAAK,EAED,OAAOyF,EAAc,MAAM,UAAY,CACrC,OAAAgG,EAAW,QAAQzL,CAAO,EAAE,QAAQ,SAAUC,EAAO,CACnDA,EAAM,MAAK,CACb,CAAC,EACMqF,EAAO,eAAeqG,EAAgB7K,CAAO,CACtD,CAAC,CACH,EAEA5C,EAAO,cAAgB,SAAuBmB,EAAMC,EAAMC,EAAM,CAC9D,IAAI4I,EAAS,KAETyD,EAAoBlM,EAAgBL,EAAMC,EAAMC,CAAI,EACpDS,EAAU4L,EAAkB,CAAC,EAC7BC,EAAqBD,EAAkB,CAAC,EACxCxH,EAAgByH,IAAuB,OAAS,GAAKA,EAErD,OAAOzH,EAAc,OAAW,MAClCA,EAAc,OAAS,IAGzB,IAAI0H,EAAWrG,EAAc,MAAM,UAAY,CAC7C,OAAO0C,EAAO,WAAW,QAAQnI,CAAO,EAAE,IAAI,SAAUC,EAAO,CAC7D,OAAOA,EAAM,OAAOmE,CAAa,CACnC,CAAC,CACH,CAAC,EACD,OAAO,QAAQ,IAAI0H,CAAQ,EAAE,KAAKpN,CAAI,EAAE,MAAMA,CAAI,CACpD,EAEAR,EAAO,kBAAoB,SAA2BmB,EAAMC,EAAMC,EAAM,CACtE,IAAIwM,EACAC,EACAC,EACA7D,EAAS,KAET8D,EAAoBxM,EAAgBL,EAAMC,EAAMC,CAAI,EACpDS,EAAUkM,EAAkB,CAAC,EAC7BpL,EAAUoL,EAAkB,CAAC,EAE7BP,EAAiBpN,EAAS,GAAIyB,EAAS,CAGzC,QAAS+L,GAASC,EAAwBhM,EAAQ,gBAAkB,KAAOgM,EAAwBhM,EAAQ,SAAW,KAAO+L,EAAQ,GACrI,UAAWE,EAAwBjM,EAAQ,kBAAoB,KAAOiM,EAAwB,EACpG,CAAK,EAED,OAAOxG,EAAc,MAAM,UAAY,CACrC,OAAA2C,EAAO,WAAW,QAAQpI,CAAO,EAAE,QAAQ,SAAUC,EAAO,CAC1DA,EAAM,WAAU,CAClB,CAAC,EAEMmI,EAAO,eAAeuD,EAAgB7K,CAAO,CACtD,CAAC,CACH,EAEA5C,EAAO,eAAiB,SAAwBmB,EAAMC,EAAMC,EAAM,CAChE,IAAI4M,EAAS,KAETC,EAAoB1M,EAAgBL,EAAMC,EAAMC,CAAI,EACpDS,EAAUoM,EAAkB,CAAC,EAC7BtL,EAAUsL,EAAkB,CAAC,EAE7BN,EAAWrG,EAAc,MAAM,UAAY,CAC7C,OAAO0G,EAAO,WAAW,QAAQnM,CAAO,EAAE,IAAI,SAAUC,EAAO,CAC7D,OAAOA,EAAM,MAAM,OAAW1B,EAAS,GAAIuC,EAAS,CAClD,KAAM,CACJ,YAAad,GAAW,KAAO,OAASA,EAAQ,WAC5D,CACA,CAAS,CAAC,CACJ,CAAC,CACH,CAAC,EACGuG,EAAU,QAAQ,IAAIuF,CAAQ,EAAE,KAAKpN,CAAI,EAE7C,OAAMoC,GAAW,MAAgBA,EAAQ,eACvCyF,EAAUA,EAAQ,MAAM7H,CAAI,GAGvB6H,CACT,EAEArI,EAAO,WAAa,SAAoBmB,EAAMC,EAAMC,EAAM,CACxD,IAAI8L,EAAgBjM,GAAeC,EAAMC,EAAMC,CAAI,EAC/C+L,EAAmB,KAAK,oBAAoBD,CAAa,EAEzD,OAAOC,EAAiB,MAAU,MACpCA,EAAiB,MAAQ,IAG3B,IAAIrL,EAAQ,KAAK,WAAW,MAAM,KAAMqL,CAAgB,EACxD,OAAOrL,EAAM,cAAcqL,EAAiB,SAAS,EAAIrL,EAAM,MAAMqL,CAAgB,EAAI,QAAQ,QAAQrL,EAAM,MAAM,IAAI,CAC3H,EAEA/B,EAAO,cAAgB,SAAuBmB,EAAMC,EAAMC,EAAM,CAC9D,OAAO,KAAK,WAAWF,EAAMC,EAAMC,CAAI,EAAE,KAAKb,CAAI,EAAE,MAAMA,CAAI,CAChE,EAEAR,EAAO,mBAAqB,SAA4BmB,EAAMC,EAAMC,EAAM,CACxE,IAAI8L,EAAgBjM,GAAeC,EAAMC,EAAMC,CAAI,EACnD,OAAA8L,EAAc,SAAWzC,GAAqB,EACvC,KAAK,WAAWyC,CAAa,CACtC,EAEAnN,EAAO,sBAAwB,SAA+BmB,EAAMC,EAAMC,EAAM,CAC9E,OAAO,KAAK,mBAAmBF,EAAMC,EAAMC,CAAI,EAAE,KAAKb,CAAI,EAAE,MAAMA,CAAI,CACxE,EAEAR,EAAO,gBAAkB,UAA2B,CAClD,IAAImO,EAAS,KAETP,EAAWrG,EAAc,MAAM,UAAY,CAC7C,OAAO4G,EAAO,cAAc,OAAM,EAAG,IAAI,SAAU1L,EAAU,CAC3D,OAAOA,EAAS,OAAM,CACxB,CAAC,CACH,CAAC,EACD,OAAO,QAAQ,IAAImL,CAAQ,EAAE,KAAKpN,CAAI,EAAE,MAAMA,CAAI,CACpD,EAEAR,EAAO,sBAAwB,UAAiC,CAC9D,OAAO,KAAK,iBAAgB,EAAG,sBAAqB,CACtD,EAEAA,EAAO,gBAAkB,SAAyB4C,EAAS,CACzD,OAAO,KAAK,cAAc,MAAM,KAAMA,CAAO,EAAE,QAAO,CACxD,EAEA5C,EAAO,cAAgB,UAAyB,CAC9C,OAAO,KAAK,UACd,EAEAA,EAAO,iBAAmB,UAA4B,CACpD,OAAO,KAAK,aACd,EAEAA,EAAO,kBAAoB,UAA6B,CACtD,OAAO,KAAK,cACd,EAEAA,EAAO,kBAAoB,SAA2B4C,EAAS,CAC7D,KAAK,eAAiBA,CACxB,EAEA5C,EAAO,iBAAmB,SAA0BmC,EAAUS,EAAS,CACrE,IAAIO,EAAS,KAAK,cAAc,KAAK,SAAU/C,EAAG,CAChD,OAAOuC,EAAaR,CAAQ,IAAMQ,EAAavC,EAAE,QAAQ,CAC3D,CAAC,EAEG+C,EACFA,EAAO,eAAiBP,EAExB,KAAK,cAAc,KAAK,CACtB,SAAUT,EACV,eAAgBS,CACxB,CAAO,CAEL,EAEA5C,EAAO,iBAAmB,SAA0BmC,EAAU,CAC5D,IAAIiM,EAEJ,OAAOjM,GAAYiM,EAAwB,KAAK,cAAc,KAAK,SAAUhO,EAAG,CAC9E,OAAOkC,GAAgBH,EAAU/B,EAAE,QAAQ,CAC7C,CAAC,IAAM,KAAO,OAASgO,EAAsB,eAAiB,MAChE,EAEApO,EAAO,oBAAsB,SAA6B0C,EAAaE,EAAS,CAC9E,IAAIO,EAAS,KAAK,iBAAiB,KAAK,SAAU/C,EAAG,CACnD,OAAOuC,EAAaD,CAAW,IAAMC,EAAavC,EAAE,WAAW,CACjE,CAAC,EAEG+C,EACFA,EAAO,eAAiBP,EAExB,KAAK,iBAAiB,KAAK,CACzB,YAAaF,EACb,eAAgBE,CACxB,CAAO,CAEL,EAEA5C,EAAO,oBAAsB,SAA6B0C,EAAa,CACrE,IAAI2L,EAEJ,OAAO3L,GAAe2L,EAAwB,KAAK,iBAAiB,KAAK,SAAUjO,EAAG,CACpF,OAAOkC,GAAgBI,EAAatC,EAAE,WAAW,CACnD,CAAC,IAAM,KAAO,OAASiO,EAAsB,eAAiB,MAChE,EAEArO,EAAO,oBAAsB,SAA6B4C,EAAS,CACjE,GAAIA,GAAW,MAAgBA,EAAQ,WACrC,OAAOA,EAGT,IAAIwK,EAAmB/M,EAAS,GAAI,KAAK,eAAe,QAAS,KAAK,iBAAiBuC,GAAW,KAAO,OAASA,EAAQ,QAAQ,EAAGA,EAAS,CAC5I,WAAY,EAClB,CAAK,EAED,MAAI,CAACwK,EAAiB,WAAaA,EAAiB,WAClDA,EAAiB,UAAY/K,GAAsB+K,EAAiB,SAAUA,CAAgB,GAGzFA,CACT,EAEApN,EAAO,4BAA8B,SAAqC4C,EAAS,CACjF,OAAO,KAAK,oBAAoBA,CAAO,CACzC,EAEA5C,EAAO,uBAAyB,SAAgC4C,EAAS,CACvE,OAAIA,GAAW,MAAgBA,EAAQ,WAC9BA,EAGFvC,EAAS,GAAI,KAAK,eAAe,UAAW,KAAK,oBAAoBuC,GAAW,KAAO,OAASA,EAAQ,WAAW,EAAGA,EAAS,CACpI,WAAY,EAClB,CAAK,CACH,EAEA5C,EAAO,MAAQ,UAAiB,CAC9B,KAAK,WAAW,MAAK,EACrB,KAAK,cAAc,MAAK,CAC1B,EAEO6M,CACT,EAAC,ECrVUyB,GAA6B,SAAU7J,EAAe,CAC/D7E,GAAe0O,EAAe7J,CAAa,EAE3C,SAAS6J,EAAc7O,EAAQmD,EAAS,CACtC,IAAI1C,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KACpCvE,EAAM,OAAST,EACfS,EAAM,QAAU0C,EAChB1C,EAAM,aAAe,GACrBA,EAAM,YAAc,KAEpBA,EAAM,YAAW,EAEjBA,EAAM,WAAW0C,CAAO,EAEjB1C,CACT,CAEA,IAAIF,EAASsO,EAAc,UAE3B,OAAAtO,EAAO,YAAc,UAAuB,CAC1C,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,EACnC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACvC,EAEAA,EAAO,YAAc,UAAuB,CACtC,KAAK,UAAU,SAAW,IAC5B,KAAK,aAAa,YAAY,IAAI,EAE9BuO,GAAmB,KAAK,aAAc,KAAK,OAAO,GACpD,KAAK,aAAY,EAGnB,KAAK,aAAY,EAErB,EAEAvO,EAAO,cAAgB,UAAyB,CACzC,KAAK,UAAU,QAClB,KAAK,QAAO,CAEhB,EAEAA,EAAO,uBAAyB,UAAkC,CAChE,OAAOwO,GAAc,KAAK,aAAc,KAAK,QAAS,KAAK,QAAQ,kBAAkB,CACvF,EAEAxO,EAAO,yBAA2B,UAAoC,CACpE,OAAOwO,GAAc,KAAK,aAAc,KAAK,QAAS,KAAK,QAAQ,oBAAoB,CACzF,EAEAxO,EAAO,QAAU,UAAmB,CAClC,KAAK,UAAY,GACjB,KAAK,YAAW,EAChB,KAAK,aAAa,eAAe,IAAI,CACvC,EAEAA,EAAO,WAAa,SAAoB4C,EAAS6L,EAAe,CAC9D,IAAIC,EAAc,KAAK,QACnBC,EAAY,KAAK,aAGrB,GAFA,KAAK,QAAU,KAAK,OAAO,4BAA4B/L,CAAO,EAE1D,OAAO,KAAK,QAAQ,QAAY,KAAe,OAAO,KAAK,QAAQ,SAAY,UACjF,MAAM,IAAI,MAAM,kCAAkC,EAI/C,KAAK,QAAQ,WAChB,KAAK,QAAQ,SAAW8L,EAAY,UAGtC,KAAK,YAAW,EAChB,IAAIE,EAAU,KAAK,eAEfA,GAAWC,GAAsB,KAAK,aAAcF,EAAW,KAAK,QAASD,CAAW,GAC1F,KAAK,aAAY,EAInB,KAAK,aAAaD,CAAa,EAE3BG,IAAY,KAAK,eAAiBD,GAAa,KAAK,QAAQ,UAAYD,EAAY,SAAW,KAAK,QAAQ,YAAcA,EAAY,YACxI,KAAK,mBAAkB,EAGzB,IAAII,EAAsB,KAAK,yBAE3BF,IAAY,KAAK,eAAiBD,GAAa,KAAK,QAAQ,UAAYD,EAAY,SAAWI,IAAwB,KAAK,yBAC9H,KAAK,sBAAsBA,CAAmB,CAElD,EAEA9O,EAAO,oBAAsB,SAA6B4C,EAAS,CACjE,IAAIwK,EAAmB,KAAK,OAAO,4BAA4BxK,CAAO,EAClEb,EAAQ,KAAK,OAAO,cAAa,EAAG,MAAM,KAAK,OAAQqL,CAAgB,EAC3E,OAAO,KAAK,aAAarL,EAAOqL,CAAgB,CAClD,EAEApN,EAAO,iBAAmB,UAA4B,CACpD,OAAO,KAAK,aACd,EAEAA,EAAO,YAAc,SAAqBmD,EAAQiK,EAAkB,CAClE,IAAIrI,EAAS,KAETgK,EAAgB,GAEhBC,EAAY,SAAmB5L,EAAK,CACjC2B,EAAO,aAAa,SAAS3B,CAAG,GACnC2B,EAAO,aAAa,KAAK3B,CAAG,CAEhC,EAEA,cAAO,KAAKD,CAAM,EAAE,QAAQ,SAAUC,EAAK,CACzC,OAAO,eAAe2L,EAAe3L,EAAK,CACxC,aAAc,GACd,WAAY,GACZ,IAAK,UAAe,CAClB,OAAA4L,EAAU5L,CAAG,EACND,EAAOC,CAAG,CACnB,CACR,CAAO,CACH,CAAC,GAEGgK,EAAiB,kBAAoBA,EAAiB,WACxD4B,EAAU,OAAO,EAGZD,CACT,EAEA/O,EAAO,cAAgB,SAAuB4C,EAAS,CACrD,IAAIwE,EAAS,KAEb,OAAO,IAAI,QAAQ,SAAUhD,EAASiC,EAAQ,CAC5C,IAAI4I,EAAc7H,EAAO,UAAU,SAAUjE,EAAQ,CAC9CA,EAAO,aACV8L,EAAW,EAEP9L,EAAO,UAAYP,GAAW,MAAgBA,EAAQ,cACxDyD,EAAOlD,EAAO,KAAK,EAEnBiB,EAAQjB,CAAM,EAGpB,CAAC,CACH,CAAC,CACH,EAEAnD,EAAO,gBAAkB,UAA2B,CAClD,OAAO,KAAK,YACd,EAEAA,EAAO,OAAS,UAAkB,CAChC,KAAK,OAAO,cAAa,EAAG,OAAO,KAAK,YAAY,CACtD,EAEAA,EAAO,QAAU,SAAiB4C,EAAS,CACzC,OAAO,KAAK,MAAMvC,EAAS,GAAIuC,EAAS,CACtC,KAAM,CACJ,YAAaA,GAAW,KAAO,OAASA,EAAQ,WACxD,CACA,CAAK,CAAC,CACJ,EAEA5C,EAAO,gBAAkB,SAAyB4C,EAAS,CACzD,IAAIqH,EAAS,KAETmD,EAAmB,KAAK,OAAO,4BAA4BxK,CAAO,EAClEb,EAAQ,KAAK,OAAO,cAAa,EAAG,MAAM,KAAK,OAAQqL,CAAgB,EAC3E,OAAOrL,EAAM,QAAQ,KAAK,UAAY,CACpC,OAAOkI,EAAO,aAAalI,EAAOqL,CAAgB,CACpD,CAAC,CACH,EAEApN,EAAO,MAAQ,SAAeyI,EAAc,CAC1C,IAAIyB,EAAS,KAEb,OAAO,KAAK,aAAazB,CAAY,EAAE,KAAK,UAAY,CACtD,OAAAyB,EAAO,aAAY,EAEZA,EAAO,aAChB,CAAC,CACH,EAEAlK,EAAO,aAAe,SAAsByI,EAAc,CAExD,KAAK,YAAW,EAEhB,IAAIJ,EAAU,KAAK,aAAa,MAAM,KAAK,QAASI,CAAY,EAEhE,OAAMA,GAAgB,MAAgBA,EAAa,eACjDJ,EAAUA,EAAQ,MAAM7H,CAAI,GAGvB6H,CACT,EAEArI,EAAO,mBAAqB,UAA8B,CACxD,IAAIiO,EAAS,KAIb,GAFA,KAAK,kBAAiB,EAElB,EAAA1N,IAAY,KAAK,cAAc,SAAW,CAACK,GAAe,KAAK,QAAQ,SAAS,GAIpF,KAAIsO,EAAOnO,GAAe,KAAK,cAAc,cAAe,KAAK,QAAQ,SAAS,EAG9EoD,EAAU+K,EAAO,EACrB,KAAK,eAAiB,WAAW,UAAY,CACtCjB,EAAO,cAAc,SACxBA,EAAO,aAAY,CAEvB,EAAG9J,CAAO,EACZ,EAEAnE,EAAO,uBAAyB,UAAkC,CAChE,IAAImP,EAEJ,OAAO,OAAO,KAAK,QAAQ,iBAAoB,WAAa,KAAK,QAAQ,gBAAgB,KAAK,cAAc,KAAM,KAAK,YAAY,GAAKA,EAAwB,KAAK,QAAQ,kBAAoB,KAAOA,EAAwB,EAClO,EAEAnP,EAAO,sBAAwB,SAA+BoP,EAAc,CAC1E,IAAIjB,EAAS,KAEb,KAAK,qBAAoB,EACzB,KAAK,uBAAyBiB,EAE1B,EAAA7O,IAAY,KAAK,QAAQ,UAAY,IAAS,CAACK,GAAe,KAAK,sBAAsB,GAAK,KAAK,yBAA2B,KAIlI,KAAK,kBAAoB,YAAY,UAAY,EAC3CuN,EAAO,QAAQ,6BAA+BlJ,GAAa,UAAS,IACtEkJ,EAAO,aAAY,CAEvB,EAAG,KAAK,sBAAsB,EAChC,EAEAnO,EAAO,aAAe,UAAwB,CAC5C,KAAK,mBAAkB,EACvB,KAAK,sBAAsB,KAAK,wBAAwB,CAC1D,EAEAA,EAAO,YAAc,UAAuB,CAC1C,KAAK,kBAAiB,EACtB,KAAK,qBAAoB,CAC3B,EAEAA,EAAO,kBAAoB,UAA6B,CAClD,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,OAE1B,EAEAA,EAAO,qBAAuB,UAAgC,CACxD,KAAK,oBACP,cAAc,KAAK,iBAAiB,EACpC,KAAK,kBAAoB,OAE7B,EAEAA,EAAO,aAAe,SAAsB+B,EAAOa,EAAS,CAC1D,IAAI+L,EAAY,KAAK,aACjBD,EAAc,KAAK,QACnBW,EAAa,KAAK,cAClBC,EAAkB,KAAK,mBACvBC,EAAoB,KAAK,qBACzBC,EAAczN,IAAU4M,EACxBc,EAAoBD,EAAczN,EAAM,MAAQ,KAAK,yBACrD2N,EAAkBF,EAAc,KAAK,cAAgB,KAAK,oBAC1DtH,EAAQnG,EAAM,MACd4N,EAAgBzH,EAAM,cACtB5D,EAAQ4D,EAAM,MACd0H,EAAiB1H,EAAM,eACvB2H,EAAa3H,EAAM,WACnB4H,EAAS5H,EAAM,OACf6H,EAAiB,GACjBC,EAAoB,GACpB/H,EAEJ,GAAIrF,EAAQ,kBAAmB,CAC7B,IAAIgM,GAAU,KAAK,aAAY,EAC3BqB,GAAe,CAACrB,IAAWL,GAAmBxM,EAAOa,CAAO,EAC5DsN,GAAkBtB,IAAWC,GAAsB9M,EAAO4M,EAAW/L,EAAS8L,CAAW,GAEzFuB,IAAgBC,MAClBL,EAAa,GAERF,IACHG,EAAS,WAGf,CAGA,GAAIlN,EAAQ,kBAAoB,CAACsF,EAAM,kBAAoBwH,GAAmB,MAAgBA,EAAgB,YAAcI,IAAW,QACrI7H,EAAOyH,EAAgB,KACvBC,EAAgBD,EAAgB,cAChCI,EAASJ,EAAgB,OACzBK,EAAiB,WAEVnN,EAAQ,QAAU,OAAOsF,EAAM,KAAS,IAE7C,GAAImH,GAAcnH,EAAM,QAAUoH,GAAmB,KAAO,OAASA,EAAgB,OAAS1M,EAAQ,SAAW,KAAK,SACpHqF,EAAO,KAAK,iBAEZ,IAAI,CACF,KAAK,SAAWrF,EAAQ,OACxBqF,EAAOrF,EAAQ,OAAOsF,EAAM,IAAI,EAE5BtF,EAAQ,oBAAsB,KAChCqF,EAAO3E,GAAiB+L,GAAc,KAAO,OAASA,EAAW,KAAMpH,CAAI,GAG7E,KAAK,aAAeA,EACpB,KAAK,YAAc,IACrB,OAASkI,EAAa,CACpB1I,GAAS,EAAG,MAAM0I,CAAW,EAC7B,KAAK,YAAcA,CACrB,MAIAlI,EAAOC,EAAM,KAInB,GAAI,OAAOtF,EAAQ,gBAAoB,KAAe,OAAOqF,EAAS,MAAgB6H,IAAW,WAAaA,IAAW,QAAS,CAChI,IAAIM,EAEJ,GAAKf,GAAc,MAAgBA,EAAW,mBAAsBzM,EAAQ,mBAAqB2M,GAAqB,KAAO,OAASA,EAAkB,iBACtJa,EAAkBf,EAAW,aAE7Be,EAAkB,OAAOxN,EAAQ,iBAAoB,WAAaA,EAAQ,gBAAe,EAAKA,EAAQ,gBAElGA,EAAQ,QAAU,OAAOwN,EAAoB,IAC/C,GAAI,CACFA,EAAkBxN,EAAQ,OAAOwN,CAAe,EAE5CxN,EAAQ,oBAAsB,KAChCwN,EAAkB9M,GAAiB+L,GAAc,KAAO,OAASA,EAAW,KAAMe,CAAe,GAGnG,KAAK,YAAc,IACrB,OAASD,EAAa,CACpB1I,GAAS,EAAG,MAAM0I,CAAW,EAC7B,KAAK,YAAcA,CACrB,CAIA,OAAOC,EAAoB,MAC7BN,EAAS,UACT7H,EAAOmI,EACPJ,EAAoB,GAExB,CAEI,KAAK,cACP1L,EAAQ,KAAK,YACb2D,EAAO,KAAK,aACZ2H,EAAiB,KAAK,IAAG,EACzBE,EAAS,SAGX,IAAI3M,GAAS,CACX,OAAQ2M,EACR,UAAWA,IAAW,UACtB,UAAWA,IAAW,UACtB,QAASA,IAAW,QACpB,OAAQA,IAAW,OACnB,KAAM7H,EACN,cAAe0H,EACf,MAAOrL,EACP,eAAgBsL,EAChB,aAAc1H,EAAM,kBACpB,iBAAkBA,EAAM,iBACxB,UAAWA,EAAM,gBAAkB,GAAKA,EAAM,iBAAmB,EACjE,oBAAqBA,EAAM,gBAAkBuH,EAAkB,iBAAmBvH,EAAM,iBAAmBuH,EAAkB,iBAC7H,WAAYI,EACZ,aAAcA,GAAcC,IAAW,UACvC,eAAgBA,IAAW,SAAW5H,EAAM,gBAAkB,EAC9D,kBAAmB8H,EACnB,eAAgBD,EAChB,eAAgBD,IAAW,SAAW5H,EAAM,gBAAkB,EAC9D,QAASmI,GAAQtO,EAAOa,CAAO,EAC/B,QAAS,KAAK,QACd,OAAQ,KAAK,MACnB,EACI,OAAOO,EACT,EAEAnD,EAAO,sBAAwB,SAA+BmD,EAAQkM,EAAY,CAChF,GAAI,CAACA,EACH,MAAO,GAGT,IAAItH,EAAgB,KAAK,QACrBuI,EAAsBvI,EAAc,oBACpCwI,EAAgCxI,EAAc,8BAMlD,GAJI,CAACuI,GAAuB,CAACC,GAIzBD,IAAwB,WAAa,CAAC,KAAK,aAAa,OAC1D,MAAO,GAGT,IAAIE,EAAgBF,IAAwB,UAAY,KAAK,aAAeA,EAC5E,OAAO,OAAO,KAAKnN,CAAM,EAAE,KAAK,SAAUC,EAAK,CAC7C,IAAIqN,EAAWrN,EACXsN,EAAUvN,EAAOsN,CAAQ,IAAMpB,EAAWoB,CAAQ,EAClDE,EAAaH,GAAiB,KAAO,OAASA,EAAc,KAAK,SAAU,EAAG,CAChF,OAAO,IAAMpN,CACf,CAAC,EACGwN,EAAaL,GAAiC,KAAO,OAASA,EAA8B,KAAK,SAAU,EAAG,CAChH,OAAO,IAAMnN,CACf,CAAC,EACD,OAAOsN,GAAW,CAACE,IAAe,CAACJ,GAAiBG,EACtD,CAAC,CACH,EAEA3Q,EAAO,aAAe,SAAsByO,EAAe,CACzD,IAAIY,EAAa,KAAK,cAKtB,GAJA,KAAK,cAAgB,KAAK,aAAa,KAAK,aAAc,KAAK,OAAO,EACtE,KAAK,mBAAqB,KAAK,aAAa,MAC5C,KAAK,qBAAuB,KAAK,QAE7B,CAAAvL,GAAoB,KAAK,cAAeuL,CAAU,EAKtD,KAAIwB,EAAuB,CACzB,MAAO,EACb,GAESpC,GAAiB,KAAO,OAASA,EAAc,aAAe,IAAS,KAAK,sBAAsB,KAAK,cAAeY,CAAU,IACnIwB,EAAqB,UAAY,IAGnC,KAAK,OAAOxQ,EAAS,GAAIwQ,EAAsBpC,CAAa,CAAC,EAC/D,EAEAzO,EAAO,YAAc,UAAuB,CAC1C,IAAI+B,EAAQ,KAAK,OAAO,cAAa,EAAG,MAAM,KAAK,OAAQ,KAAK,OAAO,EAEvE,GAAIA,IAAU,KAAK,aAInB,KAAI4M,EAAY,KAAK,aACrB,KAAK,aAAe5M,EACpB,KAAK,yBAA2BA,EAAM,MACtC,KAAK,oBAAsB,KAAK,cAE5B,KAAK,iBACP4M,GAAa,MAAgBA,EAAU,eAAe,IAAI,EAC1D5M,EAAM,YAAY,IAAI,GAE1B,EAEA/B,EAAO,cAAgB,SAAuBoJ,EAAQ,CACpD,IAAIqF,EAAgB,GAEhBrF,EAAO,OAAS,UAClBqF,EAAc,UAAY,GACjBrF,EAAO,OAAS,SAAW,CAAC1D,GAAiB0D,EAAO,KAAK,IAClEqF,EAAc,QAAU,IAG1B,KAAK,aAAaA,CAAa,EAE3B,KAAK,gBACP,KAAK,aAAY,CAErB,EAEAzO,EAAO,OAAS,SAAgByO,EAAe,CAC7C,IAAIqC,EAAS,KAEbvJ,EAAc,MAAM,UAAY,CAE1BkH,EAAc,WAChBqC,EAAO,QAAQ,WAAa,MAAgBA,EAAO,QAAQ,UAAUA,EAAO,cAAc,IAAI,EAC9FA,EAAO,QAAQ,WAAa,MAAgBA,EAAO,QAAQ,UAAUA,EAAO,cAAc,KAAM,IAAI,GAC3FrC,EAAc,UACvBqC,EAAO,QAAQ,SAAW,MAAgBA,EAAO,QAAQ,QAAQA,EAAO,cAAc,KAAK,EAC3FA,EAAO,QAAQ,WAAa,MAAgBA,EAAO,QAAQ,UAAU,OAAWA,EAAO,cAAc,KAAK,GAIxGrC,EAAc,WAChBqC,EAAO,UAAU,QAAQ,SAAU7Q,EAAU,CAC3CA,EAAS6Q,EAAO,aAAa,CAC/B,CAAC,EAICrC,EAAc,OAChBqC,EAAO,OAAO,cAAa,EAAG,OAAO,CACnC,MAAOA,EAAO,aACd,KAAM,wBAChB,CAAS,CAEL,CAAC,CACH,EAEOxC,CACT,EAAEvO,EAAY,EAEd,SAASgR,GAAkBhP,EAAOa,EAAS,CACzC,OAAOA,EAAQ,UAAY,IAAS,CAACb,EAAM,MAAM,eAAiB,EAAEA,EAAM,MAAM,SAAW,SAAWa,EAAQ,eAAiB,GACjI,CAEA,SAAS2L,GAAmBxM,EAAOa,EAAS,CAC1C,OAAOmO,GAAkBhP,EAAOa,CAAO,GAAKb,EAAM,MAAM,cAAgB,GAAKyM,GAAczM,EAAOa,EAASA,EAAQ,cAAc,CACnI,CAEA,SAAS4L,GAAczM,EAAOa,EAASoO,EAAO,CAC5C,GAAIpO,EAAQ,UAAY,GAAO,CAC7B,IAAI/B,EAAQ,OAAOmQ,GAAU,WAAaA,EAAMjP,CAAK,EAAIiP,EACzD,OAAOnQ,IAAU,UAAYA,IAAU,IAASwP,GAAQtO,EAAOa,CAAO,CACxE,CAEA,MAAO,EACT,CAEA,SAASiM,GAAsB9M,EAAO4M,EAAW/L,EAAS8L,EAAa,CACrE,OAAO9L,EAAQ,UAAY,KAAUb,IAAU4M,GAAaD,EAAY,UAAY,MAAW,CAAC9L,EAAQ,UAAYb,EAAM,MAAM,SAAW,UAAYsO,GAAQtO,EAAOa,CAAO,CAC/K,CAEA,SAASyN,GAAQtO,EAAOa,EAAS,CAC/B,OAAOb,EAAM,cAAca,EAAQ,SAAS,CAC9C,CC9hBO,IAAIqO,GAAgC,SAAUxM,EAAe,CAClE7E,GAAeqR,EAAkBxM,CAAa,EAE9C,SAASwM,EAAiBxR,EAAQmD,EAAS,CACzC,IAAI1C,EAEJ,OAAAA,EAAQuE,EAAc,KAAK,IAAI,GAAK,KACpCvE,EAAM,OAAST,EAEfS,EAAM,WAAW0C,CAAO,EAExB1C,EAAM,YAAW,EAEjBA,EAAM,aAAY,EAEXA,CACT,CAEA,IAAIF,EAASiR,EAAiB,UAE9B,OAAAjR,EAAO,YAAc,UAAuB,CAC1C,KAAK,OAAS,KAAK,OAAO,KAAK,IAAI,EACnC,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,CACnC,EAEAA,EAAO,WAAa,SAAoB4C,EAAS,CAC/C,KAAK,QAAU,KAAK,OAAO,uBAAuBA,CAAO,CAC3D,EAEA5C,EAAO,cAAgB,UAAyB,CAC9C,GAAI,CAAC,KAAK,UAAU,OAAQ,CAC1B,IAAIkR,GAEHA,EAAwB,KAAK,kBAAoB,MAAgBA,EAAsB,eAAe,IAAI,CAC7G,CACF,EAEAlR,EAAO,iBAAmB,SAA0BoJ,EAAQ,CAC1D,KAAK,aAAY,EAEjB,IAAIqF,EAAgB,CAClB,UAAW,EACjB,EAEQrF,EAAO,OAAS,UAClBqF,EAAc,UAAY,GACjBrF,EAAO,OAAS,UACzBqF,EAAc,QAAU,IAG1B,KAAK,OAAOA,CAAa,CAC3B,EAEAzO,EAAO,iBAAmB,UAA4B,CACpD,OAAO,KAAK,aACd,EAEAA,EAAO,MAAQ,UAAiB,CAC9B,KAAK,gBAAkB,OACvB,KAAK,aAAY,EACjB,KAAK,OAAO,CACV,UAAW,EACjB,CAAK,CACH,EAEAA,EAAO,OAAS,SAAgBmR,EAAWvO,EAAS,CAClD,YAAK,cAAgBA,EAEjB,KAAK,iBACP,KAAK,gBAAgB,eAAe,IAAI,EAG1C,KAAK,gBAAkB,KAAK,OAAO,iBAAgB,EAAG,MAAM,KAAK,OAAQvC,EAAS,GAAI,KAAK,QAAS,CAClG,UAAW,OAAO8Q,EAAc,IAAcA,EAAY,KAAK,QAAQ,SAC7E,CAAK,CAAC,EACF,KAAK,gBAAgB,YAAY,IAAI,EAC9B,KAAK,gBAAgB,QAAO,CACrC,EAEAnR,EAAO,aAAe,UAAwB,CAC5C,IAAIkI,EAAQ,KAAK,gBAAkB,KAAK,gBAAgB,MAAQkC,GAAe,EAE3EjH,EAAS9C,EAAS,GAAI6H,EAAO,CAC/B,UAAWA,EAAM,SAAW,UAC5B,UAAWA,EAAM,SAAW,UAC5B,QAASA,EAAM,SAAW,QAC1B,OAAQA,EAAM,SAAW,OACzB,OAAQ,KAAK,OACb,MAAO,KAAK,KAClB,CAAK,EAED,KAAK,cAAgB/E,CACvB,EAEAnD,EAAO,OAAS,SAAgB4C,EAAS,CACvC,IAAImC,EAAS,KAEbwC,EAAc,MAAM,UAAY,CAE1BxC,EAAO,gBACLnC,EAAQ,WACVmC,EAAO,cAAc,WAAa,MAAgBA,EAAO,cAAc,UAAUA,EAAO,cAAc,KAAMA,EAAO,cAAc,UAAWA,EAAO,cAAc,OAAO,EACxKA,EAAO,cAAc,WAAa,MAAgBA,EAAO,cAAc,UAAUA,EAAO,cAAc,KAAM,KAAMA,EAAO,cAAc,UAAWA,EAAO,cAAc,OAAO,GACrKnC,EAAQ,UACjBmC,EAAO,cAAc,SAAW,MAAgBA,EAAO,cAAc,QAAQA,EAAO,cAAc,MAAOA,EAAO,cAAc,UAAWA,EAAO,cAAc,OAAO,EACrKA,EAAO,cAAc,WAAa,MAAgBA,EAAO,cAAc,UAAU,OAAWA,EAAO,cAAc,MAAOA,EAAO,cAAc,UAAWA,EAAO,cAAc,OAAO,IAKpLnC,EAAQ,WACVmC,EAAO,UAAU,QAAQ,SAAU9E,EAAU,CAC3CA,EAAS8E,EAAO,aAAa,CAC/B,CAAC,CAEL,CAAC,CACH,EAEOkM,CACT,EAAElR,EAAY,EC5HHqR,GAA0BC,GAAS,wBCC9C9J,EAAc,uBAAuB6J,EAAuB,ECFrD,IAAI5J,GAAS,QCEpBE,GAAUF,EAAM,ECDhB,IAAI8J,GAA8BC,EAAM,cAAc,MAAS,EAC3DC,GAAyCD,EAAM,cAAc,EAAK,EAOtE,SAASE,GAAsBC,EAAgB,CAC7C,OAAIA,GAAkB,OAAO,OAAW,KACjC,OAAO,0BACV,OAAO,wBAA0BJ,IAG5B,OAAO,yBAGTA,EACT,CAEO,IAAIK,GAAiB,UAA0B,CACpD,IAAIC,EAAcL,EAAM,WAAWE,GAAsBF,EAAM,WAAWC,EAAyB,CAAC,CAAC,EAErG,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,wDAAwD,EAG1E,OAAOA,CACT,EACWC,GAAsB,SAA6B3E,EAAM,CAClE,IAAIzN,EAASyN,EAAK,OACd4E,EAAsB5E,EAAK,eAC3BwE,EAAiBI,IAAwB,OAAS,GAAQA,EAC1DC,EAAW7E,EAAK,SACpBqE,EAAM,UAAU,UAAY,CAC1B,OAAA9R,EAAO,MAAK,EACL,UAAY,CACjBA,EAAO,QAAO,CAChB,CACF,EAAG,CAACA,CAAM,CAAC,EACX,IAAIuS,EAAUP,GAAsBC,CAAc,EAClD,OAAoBH,EAAM,cAAcC,GAA0B,SAAU,CAC1E,MAAOE,CACX,EAAkBH,EAAM,cAAcS,EAAQ,SAAU,CACpD,MAAOvS,CACX,EAAKsS,CAAQ,CAAC,CACd,EC7CA,SAASE,IAAc,CACrB,IAAIC,EAAW,GACf,MAAO,CACL,WAAY,UAAsB,CAChCA,EAAW,EACb,EACA,MAAO,UAAiB,CACtBA,EAAW,EACb,EACA,QAAS,UAAmB,CAC1B,OAAOA,CACT,CACJ,CACA,CAEA,IAAIC,GAA8CZ,EAAM,cAAcU,GAAW,CAAE,EAExEG,GAA6B,UAAsC,CAC5E,OAAOb,EAAM,WAAWY,EAA8B,CACxD,ECrBO,SAASE,GAAiBC,EAAUC,EAAmBC,EAAQ,CAEpE,OAAI,OAAOD,GAAsB,WACxBA,EAAkB,MAAM,OAAQC,CAAM,EAI3C,OAAOD,GAAsB,UAAkBA,EAE5C,CAAC,CAACD,CACX,CCFO,SAASG,GAAYtR,EAAMC,EAAMC,EAAM,CAC5C,IAAIqR,EAAanB,EAAM,OAAO,EAAK,EAE/BoB,EAAkBpB,EAAM,SAAS,CAAC,EAClCqB,EAAcD,EAAgB,CAAC,EAE/B/P,EAAUrB,GAAkBJ,EAAMC,EAAMC,CAAI,EAC5CuQ,EAAcD,GAAc,EAC5BkB,EAAStB,EAAM,OAAM,EAEpBsB,EAAO,QAGVA,EAAO,QAAQ,WAAWjQ,CAAO,EAFjCiQ,EAAO,QAAU,IAAI5B,GAAiBW,EAAahP,CAAO,EAK5D,IAAIkQ,EAAgBD,EAAO,QAAQ,iBAAgB,EACnDtB,EAAM,UAAU,UAAY,CAC1BmB,EAAW,QAAU,GACrB,IAAIzD,EAAc4D,EAAO,QAAQ,UAAUtL,EAAc,WAAW,UAAY,CAC1EmL,EAAW,SACbE,EAAY,SAAUxS,EAAG,CACvB,OAAOA,EAAI,CACb,CAAC,CAEL,CAAC,CAAC,EACF,OAAO,UAAY,CACjBsS,EAAW,QAAU,GACrBzD,EAAW,CACb,CACF,EAAG,EAAE,EACL,IAAI8D,EAASxB,EAAM,YAAY,SAAUJ,EAAW6B,EAAe,CACjEH,EAAO,QAAQ,OAAO1B,EAAW6B,CAAa,EAAE,MAAMxS,CAAI,CAC5D,EAAG,EAAE,EAEL,GAAIsS,EAAc,OAAST,GAAiB,OAAWQ,EAAO,QAAQ,QAAQ,iBAAkB,CAACC,EAAc,KAAK,CAAC,EACnH,MAAMA,EAAc,MAGtB,OAAOzS,EAAS,GAAIyS,EAAe,CACjC,OAAQC,EACR,YAAaD,EAAc,MAC/B,CAAG,CACH,CC9CO,SAASG,GAAarQ,EAASsQ,EAAU,CAC9C,IAAIR,EAAanB,EAAM,OAAO,EAAK,EAE/BoB,EAAkBpB,EAAM,SAAS,CAAC,EAClCqB,EAAcD,EAAgB,CAAC,EAE/Bf,EAAcD,GAAc,EAC5BwB,EAAqBf,GAA0B,EAC/ChF,EAAmBwE,EAAY,4BAA4BhP,CAAO,EAEtEwK,EAAiB,kBAAoB,GAEjCA,EAAiB,UACnBA,EAAiB,QAAU7F,EAAc,WAAW6F,EAAiB,OAAO,GAG1EA,EAAiB,YACnBA,EAAiB,UAAY7F,EAAc,WAAW6F,EAAiB,SAAS,GAG9EA,EAAiB,YACnBA,EAAiB,UAAY7F,EAAc,WAAW6F,EAAiB,SAAS,GAG9EA,EAAiB,WAGf,OAAOA,EAAiB,WAAc,WACxCA,EAAiB,UAAY,KAK3BA,EAAiB,YAAc,IACjCA,EAAiB,UAAY,KAI7BA,EAAiB,UAAYA,EAAiB,oBAE3C+F,EAAmB,YACtB/F,EAAiB,aAAe,KAIpC,IAAIgG,EAAmB7B,EAAM,SAAS,UAAY,CAChD,OAAO,IAAI2B,EAAStB,EAAaxE,CAAgB,CACnD,CAAC,EACG9E,EAAW8K,EAAiB,CAAC,EAE7BjQ,EAASmF,EAAS,oBAAoB8E,CAAgB,EA2B1D,GA1BAmE,EAAM,UAAU,UAAY,CAC1BmB,EAAW,QAAU,GACrBS,EAAmB,WAAU,EAC7B,IAAIlE,EAAc3G,EAAS,UAAUf,EAAc,WAAW,UAAY,CACpEmL,EAAW,SACbE,EAAY,SAAUxS,EAAG,CACvB,OAAOA,EAAI,CACb,CAAC,CAEL,CAAC,CAAC,EAGF,OAAAkI,EAAS,aAAY,EACd,UAAY,CACjBoK,EAAW,QAAU,GACrBzD,EAAW,CACb,CACF,EAAG,CAACkE,EAAoB7K,CAAQ,CAAC,EACjCiJ,EAAM,UAAU,UAAY,CAG1BjJ,EAAS,WAAW8E,EAAkB,CACpC,UAAW,EACjB,CAAK,CACH,EAAG,CAACA,EAAkB9E,CAAQ,CAAC,EAE3B8E,EAAiB,UAAYjK,EAAO,UACtC,MAAMmF,EAAS,gBAAgB8E,CAAgB,EAAE,KAAK,SAAUF,EAAM,CACpE,IAAIjF,EAAOiF,EAAK,KAChBE,EAAiB,WAAa,MAAgBA,EAAiB,UAAUnF,CAAI,EAC7EmF,EAAiB,WAAa,MAAgBA,EAAiB,UAAUnF,EAAM,IAAI,CACrF,CAAC,EAAE,MAAM,SAAU3D,EAAO,CACxB6O,EAAmB,WAAU,EAC7B/F,EAAiB,SAAW,MAAgBA,EAAiB,QAAQ9I,CAAK,EAC1E8I,EAAiB,WAAa,MAAgBA,EAAiB,UAAU,OAAW9I,CAAK,CAC3F,CAAC,EAIH,GAAInB,EAAO,SAAW,CAACgQ,EAAmB,QAAO,GAAM,CAAChQ,EAAO,YAAckP,GAAiBjF,EAAiB,SAAUA,EAAiB,iBAAkB,CAACjK,EAAO,MAAOmF,EAAS,gBAAe,CAAE,CAAC,EACpM,MAAMnF,EAAO,MAIf,OAAIiK,EAAiB,sBAAwB,YAC3CjK,EAASmF,EAAS,YAAYnF,EAAQiK,CAAgB,GAGjDjK,CACT,CCrGO,SAASkQ,GAASlS,EAAMC,EAAMC,EAAM,CACzC,IAAI8L,EAAgBjM,GAAeC,EAAMC,EAAMC,CAAI,EACnD,OAAO4R,GAAa9F,EAAemB,EAAa,CAClD,CCPG,IAACjP,GAAE,CAAC,KAAK,EAAE,EAAEM,GAAEA,GAAa,OAAO,QAAjB,WAA0BA,EAAEA,EAAE,cAAc,UAAU,EAAE,OAAO,UAAU,OAAO,QAAQA,GAAG,SAAS,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC,EAAE,CAAC,UAAU,IAAI,GAAG,SAAS,CAAC,GAAG,WAAWA,GAAGN,GAAgDV,GAAE,oEAAoEM,GAAE,qBAAqBJ,GAAE,OAAOgB,EAAE,CAACR,EAAEM,IAAI,CAAC,IAAI,EAAE,GAAGhB,EAAE,GAAGM,EAAE,GAAG,QAAQJ,KAAKQ,EAAE,CAAC,IAAIL,EAAEK,EAAER,CAAC,EAAOA,EAAE,CAAC,GAAR,IAAeA,EAAE,CAAC,GAAR,IAAU,EAAEA,EAAE,IAAIG,EAAE,IAAIL,GAAQE,EAAE,CAAC,GAAR,IAAUgB,EAAEb,EAAEH,CAAC,EAAEA,EAAE,IAAIgB,EAAEb,EAAOH,EAAE,CAAC,GAAR,IAAU,GAAGc,CAAC,EAAE,IAAc,OAAOX,GAAjB,SAAmBL,GAAGkB,EAAEb,EAAEW,EAAEA,EAAE,QAAQ,WAAWN,GAAGR,EAAE,QAAQ,gCAAgCc,GAAG,IAAI,KAAKA,CAAC,EAAEA,EAAE,QAAQ,KAAKN,CAAC,EAAEA,EAAEA,EAAE,IAAIM,EAAEA,CAAC,CAAC,EAAEd,CAAC,EAAQG,GAAN,OAAUH,EAAE,MAAM,KAAKA,CAAC,EAAEA,EAAEA,EAAE,QAAQ,SAAS,KAAK,EAAE,YAAW,EAAGI,GAAGY,EAAE,EAAEA,EAAE,EAAEhB,EAAEG,CAAC,EAAEH,EAAE,IAAIG,EAAE,IAAI,CAAC,OAAO,GAAGW,GAAGV,EAAEU,EAAE,IAAIV,EAAE,IAAIA,GAAGN,CAAC,EAAEK,EAAE,GAAGsU,GAAEjU,GAAG,CAAC,GAAa,OAAOA,GAAjB,SAAmB,CAAC,IAAIM,EAAE,GAAG,QAAQ,KAAKN,EAAEM,GAAG,EAAE2T,GAAEjU,EAAE,CAAC,CAAC,EAAE,OAAOM,CAAC,CAAC,OAAON,CAAC,EAAEwE,GAAE,CAACxE,EAAEM,EAAE,EAAEkE,EAAE/E,IAAI,CAAC,IAAIyU,EAAED,GAAEjU,CAAC,EAAED,EAAEJ,EAAEuU,CAAC,IAAIvU,EAAEuU,CAAC,GAAGlU,GAAG,CAAC,IAAIM,EAAE,EAAEW,EAAE,GAAG,KAAKX,EAAEN,EAAE,QAAQiB,EAAE,IAAIA,EAAEjB,EAAE,WAAWM,GAAG,IAAI,EAAE,MAAM,KAAKW,CAAC,GAAGiT,CAAC,GAAG,GAAG,CAACvU,EAAEI,CAAC,EAAE,CAAC,IAAIO,EAAE4T,IAAIlU,EAAEA,GAAGA,GAAG,CAAC,IAAIM,EAAEW,EAAET,EAAE,CAAC,EAAE,EAAE,KAAKF,EAAEhB,GAAE,KAAKU,EAAE,QAAQJ,GAAE,EAAE,CAAC,GAAGU,EAAE,CAAC,EAAEE,EAAE,MAAK,EAAGF,EAAE,CAAC,GAAGW,EAAEX,EAAE,CAAC,EAAE,QAAQd,GAAE,GAAG,EAAE,KAAI,EAAGgB,EAAE,QAAQA,EAAE,CAAC,EAAES,CAAC,EAAET,EAAE,CAAC,EAAES,CAAC,GAAG,EAAE,GAAGT,EAAE,CAAC,EAAEF,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,EAAE,QAAQd,GAAE,GAAG,EAAE,KAAI,EAAG,OAAOgB,EAAE,CAAC,CAAC,GAAGR,CAAC,EAAEL,EAAEI,CAAC,EAAES,EAAEf,EAAE,CAAC,CAAC,cAAcM,CAAC,EAAEO,CAAC,EAAEA,EAAE,EAAE,GAAG,IAAIP,CAAC,CAAC,CAAC,IAAIZ,EAAE,GAAGQ,EAAE,EAAEA,EAAE,EAAE,KAAK,OAAO,IAAIA,EAAE,EAAEA,EAAEI,CAAC,IAAI,CAACC,EAAEM,EAAEW,EAAE3B,IAAI,CAACA,EAAEgB,EAAE,KAAKA,EAAE,KAAK,QAAQhB,EAAEU,CAAC,EAAOM,EAAE,KAAK,QAAQN,CAAC,IAArB,KAAyBM,EAAE,KAAKW,EAAEjB,EAAEM,EAAE,KAAKA,EAAE,KAAKN,EAAE,GAAGL,EAAEI,CAAC,EAAEO,EAAEkE,EAAErF,CAAC,EAAEY,CAAC,EAAEN,GAAE,CAACO,EAAEM,EAAE,IAAIN,EAAE,OAAO,CAACA,EAAEV,EAAEM,IAAI,CAAC,IAAIJ,EAAEc,EAAEV,CAAC,EAAE,GAAGJ,GAAGA,EAAE,KAAK,CAAC,IAAIQ,EAAER,EAAE,CAAC,EAAEc,EAAEN,GAAGA,EAAE,OAAOA,EAAE,MAAM,WAAW,MAAM,KAAKA,CAAC,GAAGA,EAAER,EAAEc,EAAE,IAAIA,EAAEN,GAAa,OAAOA,GAAjB,SAAmBA,EAAE,MAAM,GAAGQ,EAAER,EAAE,EAAE,EAAOA,IAAL,GAAO,GAAGA,CAAC,CAAC,OAAOA,EAAEV,GAASE,GAAE,GAAK,EAAE,EAAE,EAAE,SAAS0U,GAAElU,EAAE,CAAC,IAAIiB,EAAE,MAAM,GAAG3B,EAAEU,EAAE,KAAKA,EAAEiB,EAAE,CAAC,EAAEjB,EAAE,OAAOwE,GAAElF,EAAE,QAAQA,EAAE,IAAIG,GAAEH,EAAE,GAAG,MAAM,KAAK,UAAU,CAAC,EAAE2B,EAAE,CAAC,EAAE3B,EAAE,OAAO,CAACU,EAAEM,IAAI,OAAO,OAAON,EAAEM,GAAGA,EAAE,KAAKA,EAAEW,EAAE,CAAC,EAAEX,CAAC,EAAE,EAAE,EAAEhB,EAAEgB,GAAEW,EAAE,MAAM,EAAEA,EAAE,EAAEA,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAI,IAAClB,GAAEZ,GAAEU,GAAIqU,GAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAC,IAACjU,EAAEiU,GAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS3U,GAAES,EAAEM,EAAE,EAAEhB,EAAE,CAACkB,EAAE,EAAEF,EAAEP,GAAEC,EAAEb,GAAE,EAAEU,GAAEP,CAAC,CAAC,SAAS6U,EAAEnU,EAAEM,EAAE,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,UAAU,CAAC,IAAIhB,EAAE,UAAU,SAASM,EAAEJ,EAAE,EAAE,CAAC,IAAIG,EAAE,OAAO,OAAO,GAAGH,CAAC,EAAEyU,EAAEtU,EAAE,WAAWC,EAAE,UAAU,EAAE,EAAE,OAAO,OAAO,CAAC,MAAMT,IAAGA,GAAC,CAAE,EAAEQ,CAAC,EAAE,EAAE,EAAE,UAAU,KAAKsU,CAAC,EAAEtU,EAAE,UAAUuU,GAAE,MAAM,EAAE5U,CAAC,GAAG2U,EAAE,IAAIA,EAAE,IAAiB,IAAIzP,EAAExE,EAAE,OAAOA,EAAE,CAAC,IAAIwE,EAAE7E,EAAE,IAAIK,EAAE,OAAOL,EAAE,IAAIE,IAAG2E,EAAE,CAAC,GAAG3E,GAAEF,CAAC,EAAEI,GAAEyE,EAAE7E,CAAC,CAAC,CAAC,OAAcC,CAAC,CAAC,CCCvqE,IAAIwU,GAAEpU,GAAG,OAAOA,GAAG,WAAWb,GAAE,CAACa,EAAEM,IAAI8T,GAAEpU,CAAC,EAAEA,EAAEM,CAAC,EAAEN,EAAMqU,IAAG,IAAI,CAAC,IAAIrU,EAAE,EAAE,MAAM,KAAK,EAAEA,GAAG,SAAQ,CAAE,GAAC,EAAIsU,IAAG,IAAI,CAAC,IAAItU,EAAE,MAAM,IAAI,CAAC,GAAGA,IAAI,QAAQ,OAAO,OAAO,IAAI,CAAC,IAAIM,EAAE,WAAW,kCAAkC,EAAEN,EAAE,CAACM,GAAGA,EAAE,OAAO,CAAC,OAAON,CAAC,CAAC,GAAC,EAAoEuU,GAAE,GAAOC,GAAE,CAACxU,EAAEM,IAAI,CAAC,OAAOA,EAAE,MAAM,IAAK,GAAE,MAAM,CAAC,GAAGN,EAAE,OAAO,CAACM,EAAE,MAAM,GAAGN,EAAE,MAAM,EAAE,MAAM,EAAEuU,EAAC,CAAC,EAAE,IAAK,GAAE,MAAM,CAAC,GAAGvU,EAAE,OAAOA,EAAE,OAAO,IAAIQ,GAAGA,EAAE,KAAKF,EAAE,MAAM,GAAG,CAAC,GAAGE,EAAE,GAAGF,EAAE,KAAK,EAAEE,CAAC,CAAC,EAAE,IAAK,GAAE,GAAG,CAAC,MAAM,CAAC,EAAEF,EAAE,OAAOkU,GAAExU,EAAE,CAAC,KAAKA,EAAE,OAAO,KAAKQ,GAAGA,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,IAAK,GAAE,GAAG,CAAC,QAAQyT,CAAC,EAAE3T,EAAE,MAAM,CAAC,GAAGN,EAAE,OAAOA,EAAE,OAAO,IAAIQ,GAAGA,EAAE,KAAKyT,GAAGA,IAAI,OAAO,CAAC,GAAGzT,EAAE,UAAU,GAAG,QAAQ,EAAE,EAAEA,CAAC,CAAC,EAAE,IAAK,GAAE,OAAOF,EAAE,UAAU,OAAO,CAAC,GAAGN,EAAE,OAAO,EAAE,EAAE,CAAC,GAAGA,EAAE,OAAOA,EAAE,OAAO,OAAOQ,GAAGA,EAAE,KAAKF,EAAE,OAAO,CAAC,EAAE,IAAK,GAAE,MAAM,CAAC,GAAGN,EAAE,SAASM,EAAE,IAAI,EAAE,IAAK,GAAE,IAAIV,EAAEU,EAAE,MAAMN,EAAE,UAAU,GAAG,MAAM,CAAC,GAAGA,EAAE,SAAS,OAAO,OAAOA,EAAE,OAAO,IAAIQ,IAAI,CAAC,GAAGA,EAAE,cAAcA,EAAE,cAAcZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE6U,GAAE,GAAGC,EAAE,CAAC,OAAO,GAAG,SAAS,MAAM,EAAER,EAAElU,GAAG,CAAC0U,EAAEF,GAAEE,EAAE1U,CAAC,EAAEyU,GAAE,QAAQnU,GAAG,CAACA,EAAEoU,CAAC,CAAC,CAAC,CAAC,EAAEhV,GAAE,CAAC,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,OAAO,GAAG,EAAEiV,GAAE,CAAC3U,EAAE,KAAK,CAAC,GAAG,CAACM,EAAE,CAAC,EAAE6T,WAAEO,CAAC,EAAET,EAAEW,SAAEF,CAAC,EAAEG,YAAE,KAAKZ,EAAE,UAAUS,GAAG,EAAEA,CAAC,EAAED,GAAE,KAAK,CAAC,EAAE,IAAI,CAAC,IAAIjU,EAAEiU,GAAE,QAAQ,CAAC,EAAEjU,EAAE,IAAIiU,GAAE,OAAOjU,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,IAAIZ,EAAEU,EAAE,OAAO,IAAIE,GAAG,CAAC,IAAIhB,EAAEgF,EAAE/E,EAAE,MAAM,CAAC,GAAGO,EAAE,GAAGA,EAAEQ,EAAE,IAAI,EAAE,GAAGA,EAAE,YAAYA,EAAE,eAAehB,EAAEQ,EAAEQ,EAAE,IAAI,IAAI,KAAK,OAAOhB,EAAE,eAAeQ,GAAG,KAAK,OAAOA,EAAE,aAAa,SAASQ,EAAE,YAAYgE,EAAExE,EAAEQ,EAAE,IAAI,IAAI,KAAK,OAAOgE,EAAE,YAAYxE,GAAG,KAAK,OAAOA,EAAE,WAAWN,GAAEc,EAAE,IAAI,EAAE,MAAM,CAAC,GAAGR,EAAE,MAAM,IAAIP,EAAEO,EAAEQ,EAAE,IAAI,IAAI,KAAK,OAAOf,EAAE,MAAM,GAAGe,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAGF,EAAE,OAAOV,CAAC,CAAC,EAAMkV,GAAE,CAAC9U,EAAEM,EAAE,QAAQ,KAAK,CAAC,UAAU,KAAK,IAAG,EAAG,QAAQ,GAAG,UAAU,GAAG,KAAKA,EAAE,UAAU,CAAC,KAAK,SAAS,YAAY,QAAQ,EAAE,QAAQN,EAAE,cAAc,EAAE,GAAG,EAAE,IAAI,GAAG,KAAK,OAAO,EAAE,KAAKqU,GAAC,CAAE,GAAGtT,GAAEf,GAAG,CAACM,EAAE,IAAI,CAAC,IAAI2T,EAAEa,GAAExU,EAAEN,EAAE,CAAC,EAAE,OAAOkU,EAAE,CAAC,KAAK,EAAE,MAAMD,CAAC,CAAC,EAAEA,EAAE,EAAE,EAAEtU,EAAE,CAACK,EAAEM,IAAIS,GAAE,OAAO,EAAEf,EAAEM,CAAC,EAAEX,EAAE,MAAMoB,GAAE,OAAO,EAAEpB,EAAE,QAAQoB,GAAE,SAAS,EAAEpB,EAAE,QAAQoB,GAAE,SAAS,EAAEpB,EAAE,OAAOoB,GAAE,QAAQ,EAAEpB,EAAE,QAAQK,GAAG,CAACkU,EAAE,CAAC,KAAK,EAAE,QAAQlU,CAAC,CAAC,CAAC,EAAEL,EAAE,OAAOK,GAAGkU,EAAE,CAAC,KAAK,EAAE,QAAQlU,CAAC,CAAC,EAAEL,EAAE,QAAQ,CAACK,EAAEM,EAAE,IAAI,CAAC,IAAI2T,EAAEtU,EAAE,QAAQW,EAAE,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,OAAO,OAAON,GAAG,aAAaA,EAAEA,EAAC,GAAIA,EAAE,KAAKJ,GAAG,CAAC,IAAIY,EAAEF,EAAE,QAAQnB,GAAEmB,EAAE,QAAQV,CAAC,EAAE,OAAO,OAAOY,EAAEb,EAAE,QAAQa,EAAE,CAAC,GAAGyT,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,OAAO,EAAE,OAAO,CAAC,EAAEtU,EAAE,QAAQsU,CAAC,EAAErU,CAAC,CAAC,EAAE,MAAMA,GAAG,CAAC,IAAIY,EAAEF,EAAE,MAAMnB,GAAEmB,EAAE,MAAMV,CAAC,EAAE,OAAOY,EAAEb,EAAE,MAAMa,EAAE,CAAC,GAAGyT,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,OAAO,EAAE,KAAK,CAAC,EAAEtU,EAAE,QAAQsU,CAAC,CAAC,CAAC,EAAEjU,CAAC,EAAqD,IAAI+U,GAAE,CAAC/U,EAAEM,IAAI,CAAC4T,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,GAAGlU,EAAE,OAAOM,CAAC,CAAC,CAAC,CAAC,EAAE0U,GAAE,IAAI,CAACd,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAG,CAAE,CAAC,CAAC,EAAEpU,GAAE,IAAI,IAAImV,GAAE,IAAIC,GAAG,CAAClV,EAAEM,EAAE2U,KAAI,CAAC,GAAGnV,GAAE,IAAIE,CAAC,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,CAACF,GAAE,OAAOE,CAAC,EAAEkU,EAAE,CAAC,KAAK,EAAE,QAAQlU,CAAC,CAAC,CAAC,EAAEM,CAAC,EAAER,GAAE,IAAIE,EAAE,CAAC,CAAC,EAAEmV,GAAEnV,GAAG,CAAC,GAAG,CAAC,OAAOM,EAAE,SAAS,CAAC,EAAEqU,GAAE3U,CAAC,EAAEoV,YAAE,IAAI,CAAC,GAAG,EAAE,OAAO,IAAI5U,EAAE,KAAK,IAAG,EAAGhB,EAAEc,EAAE,IAAIkE,GAAG,CAAC,GAAGA,EAAE,WAAW,IAAI,OAAO,IAAI/E,GAAG+E,EAAE,UAAU,GAAGA,EAAE,eAAehE,EAAEgE,EAAE,WAAW,GAAG/E,EAAE,EAAE,CAAC+E,EAAE,SAAS7E,EAAE,QAAQ6E,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO,WAAW,IAAI7E,EAAE,QAAQ6E,EAAE,EAAE,EAAE/E,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,CAACD,EAAE,QAAQgF,GAAGA,GAAG,aAAaA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAClE,EAAE,CAAC,CAAC,EAAE,IAAI2T,EAAEoB,cAAE,IAAI,CAAC,GAAGnB,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEtU,EAAEyV,cAAE,CAAC7U,EAAEhB,IAAI,CAAC,GAAG,CAAC,aAAagF,EAAE,GAAG,OAAO/E,EAAE,EAAE,gBAAgBM,CAAC,EAAEP,GAAG,GAAGS,EAAEK,EAAE,OAAOf,IAAIA,EAAE,UAAUQ,MAAMS,EAAE,UAAUT,IAAIR,EAAE,MAAM,EAAE+V,EAAErV,EAAE,UAAUV,GAAGA,EAAE,KAAKiB,EAAE,EAAE,EAAE+U,EAAEtV,EAAE,OAAO,CAACV,EAAEiW,IAAIA,EAAEF,GAAG/V,EAAE,OAAO,EAAE,OAAO,OAAOU,EAAE,OAAOV,GAAGA,EAAE,OAAO,EAAE,MAAM,GAAGiF,EAAE,CAAC+Q,EAAE,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,OAAO,CAAChW,EAAEiW,IAAIjW,GAAGiW,EAAE,QAAQ,GAAG/V,EAAE,CAAC,CAAC,EAAE,CAACa,CAAC,CAAC,EAAE,OAAO8U,YAAE,IAAI,CAAC9U,EAAE,QAAQE,GAAG,CAAC,GAAGA,EAAE,UAAU0U,GAAG1U,EAAE,GAAGA,EAAE,WAAW,MAAM,CAAC,IAAIhB,EAAEM,GAAE,IAAIU,EAAE,EAAE,EAAEhB,IAAI,aAAaA,CAAC,EAAEM,GAAE,OAAOU,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAACF,CAAC,CAAC,EAAE,CAAC,OAAOA,EAAE,SAAS,CAAC,aAAayU,GAAE,WAAWC,GAAE,SAASf,EAAE,gBAAgBrU,CAAC,CAAC,CAAC,EAAqM6V,GAAGC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAQhlHC,GAAGD;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAQHE,GAAGF;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAQHrW,GAAEwW,EAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKG7V,GAAGA,EAAE,SAAS,SAAS;AAAA;AAAA;AAAA;AAAA,eAIxByV,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAOAE,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKD3V,GAAGA,EAAE,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAQvB4V,EAAE;AAAA;AAAA;AAAA;AAAA,EAIoCE,GAAGC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAOxDC,GAAEC,EAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMMjW,GAAGA,EAAE,WAAW,SAAS;AAAA,wBACnBA,GAAGA,EAAE,SAAS,SAAS;AAAA,eAChC8V,EAAE;AAAA,EACqCI,GAAGC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAQtDC,GAAGD;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAcHxS,GAAE0S,EAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKGrW,GAAGA,EAAE,SAAS,SAAS;AAAA;AAAA;AAAA;AAAA,eAIxBkW,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAMAE,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAMCpW,GAAGA,EAAE,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpCsW,GAAGC,EAAE,KAAK;AAAA;AAAA,EAEdC,GAAGD,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOVE,GAAGC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,GAQFC,GAAGJ,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,eAKEE,EAAE;AAAA;AAAA,EAEfG,GAAE,CAAC,CAAC,MAAM5W,CAAC,IAAI,CAAC,GAAG,CAAC,KAAKM,EAAE,KAAK,EAAE,UAAU2T,CAAC,EAAEjU,EAAE,OAAOM,IAAI,OAAO,OAAOA,GAAG,SAASuW,gBAAgBF,GAAG,KAAKrW,CAAC,EAAEA,EAAE,IAAI,QAAQ,KAAKuW,gBAAgBL,GAAG,KAAKK,gBAAgBb,GAAE,CAAC,GAAG/B,CAAC,CAAC,EAAE,IAAI,WAAW4C,gBAAgBP,GAAG,KAAK,IAAI,QAAQO,gBAAgBxX,GAAE,CAAC,GAAG4U,CAAC,CAAC,EAAE4C,gBAAgBlT,GAAE,CAAC,GAAGsQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAM6C,GAAG9W,GAAG;AAAA,+BAC7QA,EAAE,IAAI;AAAA;AAAA,EAEnC+W,GAAG/W,GAAG;AAAA;AAAA,iCAEyBA,EAAE,IAAI;AAAA,EACrCgX,GAAG,kCAAkCC,GAAG,kCAAkCC,GAAGC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYpFC,GAAGD,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOVE,GAAG,CAACrX,EAAEM,IAAI,CAAC,IAAI2T,EAAEjU,EAAE,SAAS,KAAK,EAAE,EAAE,GAAG,CAACJ,EAAEY,CAAC,EAAE8T,GAAC,EAAG,CAAC0C,GAAGC,EAAE,EAAE,CAACH,GAAG7C,CAAC,EAAE8C,GAAG9C,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU3T,EAAE,GAAGgX,EAAE1X,CAAC,CAAC,+CAA+C,GAAG0X,EAAE9W,CAAC,CAAC,4CAA4C,CAAC,EAAE+W,GAAEC,OAAO,CAAC,CAAC,MAAMxX,EAAE,SAASM,EAAE,MAAM,EAAE,SAAS2T,CAAC,IAAI,CAAC,IAAIrU,EAAEI,EAAE,OAAOqX,GAAGrX,EAAE,UAAUM,GAAG,aAAaN,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAEQ,EAAEiX,gBAAgBb,GAAE,CAAC,MAAM5W,CAAC,CAAC,EAAER,EAAEiY,gBAAgBL,GAAG,CAAC,GAAGpX,EAAE,SAAS,EAAEb,GAAEa,EAAE,QAAQA,CAAC,CAAC,EAAE,OAAOyX,gBAAgBP,GAAG,CAAC,UAAUlX,EAAE,UAAU,MAAM,CAAC,GAAGJ,EAAE,GAAG,EAAE,GAAGI,EAAE,KAAK,CAAC,EAAE,OAAOiU,GAAG,WAAWA,EAAE,CAAC,KAAKzT,EAAE,QAAQhB,CAAC,CAAC,EAAEiY,gBAAgBC,WAAW,KAAKlX,EAAEhB,CAAC,CAAC,CAAC,CAAC,EAAoEmY,GAAGC,eAAe,EAAE,IAAIC,GAAG,CAAC,CAAC,GAAG7X,EAAE,UAAUM,EAAE,MAAM,EAAE,eAAe2T,EAAE,SAASrU,CAAC,IAAI,CAAC,IAAIY,EAAEsX,cAActY,GAAG,CAAC,GAAGA,EAAE,CAAC,IAAIgF,EAAE,IAAI,CAAC,IAAI/E,EAAED,EAAE,sBAAqB,EAAG,OAAOyU,EAAEjU,EAAEP,CAAC,CAAC,EAAE+E,IAAI,IAAI,iBAAiBA,CAAC,EAAE,QAAQhF,EAAE,CAAC,QAAQ,GAAG,UAAU,GAAG,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,CAACQ,EAAEiU,CAAC,CAAC,EAAE,OAAO2D,gBAAgB,MAAM,CAAC,IAAIpX,EAAE,UAAUF,EAAE,MAAM,CAAC,EAAEV,CAAC,CAAC,EAAEmY,GAAG,CAAC/X,EAAEM,IAAI,CAAC,IAAI,EAAEN,EAAE,SAAS,KAAK,EAAEiU,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAErU,EAAEI,EAAE,SAAS,QAAQ,EAAE,CAAC,eAAe,QAAQ,EAAEA,EAAE,SAAS,OAAO,EAAE,CAAC,eAAe,UAAU,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,OAAO,SAAS,WAAW,WAAWsU,GAAC,EAAG,OAAO,yCAAyC,UAAU,cAAchU,GAAG,EAAE,EAAE,GAAG,MAAM,GAAG2T,EAAE,GAAGrU,CAAC,CAAC,EAAEoY,GAAGC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAK7wCC,GAAE,GAAGC,GAAG,CAAC,CAAC,aAAanY,EAAE,SAASM,EAAE,aAAa,aAAa,EAAE,OAAO2T,EAAE,SAASrU,EAAE,eAAeY,EAAE,mBAAmBhB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAOgF,EAAE,SAAS/E,CAAC,EAAE0V,GAAE,CAAC,EAAE,OAAOyC,gBAAgB,MAAM,CAAC,GAAG,eAAe,MAAM,CAAC,SAAS,QAAQ,OAAO,KAAK,IAAIM,GAAE,KAAKA,GAAE,MAAMA,GAAE,OAAOA,GAAE,cAAc,OAAO,GAAG1X,CAAC,EAAE,UAAUhB,EAAE,aAAaC,EAAE,WAAW,aAAaA,EAAE,QAAQ,EAAE+E,EAAE,IAAIzE,GAAG,CAAC,IAAIE,EAAEF,EAAE,UAAUO,EAAEgV,EAAE7V,EAAE,gBAAgBM,EAAE,CAAC,aAAaC,EAAE,OAAOiU,EAAE,gBAAgB3T,CAAC,CAAC,EAAEiV,EAAEwC,GAAG9X,EAAEqV,CAAC,EAAE,OAAOsC,gBAAgBC,GAAG,CAAC,GAAG9X,EAAE,GAAG,IAAIA,EAAE,GAAG,eAAeN,EAAE,aAAa,UAAUM,EAAE,QAAQiY,GAAG,GAAG,MAAMzC,CAAC,EAAExV,EAAE,OAAO,SAASZ,GAAEY,EAAE,QAAQA,CAAC,EAAEH,EAAEA,EAAEG,CAAC,EAAE6X,gBAAgBL,GAAE,CAAC,MAAMxX,EAAE,SAASE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAMmY,EAAGzY,ECxI9pB,MAAM0Y,GAAiB,CACrB,GAAI,IACJ,SAAU,OACV,MAAO,mBACP,MAAO,EACP,WAAY,KACZ,MAAO,IACP,KAAM,IACN,SAAU,GACV,UAAW,GACX,UAAW,IAAI,OAAO,cACtB,YAAa,IAAI,OAAO,aAC1B,EAGMC,GAAU,CAEd,eAAgB,SAA2B,CACzC,MAAMC,EAAQ,aAAa,QAAQ,OAAO,EAC1C,GAAI,CAACA,EAAO,MAAM,IAAI,MAAM,gBAAgB,EAE5C,GAAI,CACF,MAAMC,EAAW,MAAM,MAAM,eAAgB,CAC3C,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,mBAClB,CACD,EAED,GAAI,CAACC,EAAS,GACZ,MAAM,IAAI,MAAM,yBAAyB,EAG3C,OAAOA,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,wCAAwC,EAC9CH,EACT,CACF,EAGA,MAAO,MAAOI,GAA0E,CACtF,GAAI,CACF,MAAMD,EAAW,MAAM,MAAM,kBAAmB,CAC9C,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAUC,CAAW,EACjC,EAED,GAAI,CAACD,EAAS,GAAI,CAChB,MAAMvT,EAAQ,MAAMuT,EAAS,OAC7B,MAAM,IAAI,MAAMvT,EAAM,SAAW,cAAc,CACjD,CAEA,OAAOuT,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,wCAAwC,EAC9C,CACL,KAAMH,GACN,MAAO,cAAgB,KAAK,KAAI,CAEpC,CACF,EAGA,SAAU,MAAOI,GAA6E,CAC5F,GAAI,CACF,MAAMD,EAAW,MAAM,MAAM,qBAAsB,CACjD,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAUC,CAAW,EACjC,EAED,GAAI,CAACD,EAAS,GAAI,CAChB,MAAMvT,EAAQ,MAAMuT,EAAS,OAC7B,MAAM,IAAI,MAAMvT,EAAM,SAAW,qBAAqB,CACxD,CAEA,OAAOuT,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,wCAAwC,EAC9C,CACL,KAAM,CAAE,GAAGH,GAAU,SAAUI,EAAY,SAAU,MAAOA,EAAY,OACxE,MAAO,cAAgB,KAAK,KAAI,CAEpC,CACF,EAGA,cAAe,MAAO7P,GAAuC,CAC3D,MAAM2P,EAAQ,aAAa,QAAQ,OAAO,EAC1C,GAAI,CAACA,EAAO,MAAM,IAAI,MAAM,gBAAgB,EAE5C,GAAI,CACF,MAAMC,EAAW,MAAM,MAAM,qBAAsB,CACjD,OAAQ,MACR,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,oBAElB,KAAM,KAAK,UAAU3P,CAAI,EAC1B,EAED,GAAI,CAAC4P,EAAS,GAAI,CAChB,MAAMvT,EAAQ,MAAMuT,EAAS,OAC7B,MAAM,IAAI,MAAMvT,EAAM,SAAW,eAAe,CAClD,CAEA,OAAOuT,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,wCAAwC,EAC9C,CAAE,GAAGH,GAAU,GAAGzP,CAAA,CAC3B,CACF,CACF,EAGM8P,GAAcC,gBAA2C,MAAS,EAG3DC,GAAwD,CAAC,CAAE,SAAAlG,KAAe,CACrF,KAAM,CAACmG,EAAMC,CAAO,EAAIC,WAAsB,IAAI,EAC5CxG,EAAcD,GAAA,EAGd,CAAE,UAAA0G,GAAchF,GACpB,cACAsE,GAAQ,eACR,CACE,QAAS,CAAC,CAAC,aAAa,QAAQ,OAAO,EACvC,MAAO,GACP,UAAY1P,GAAS,CACnBkQ,EAAQlQ,CAAI,CACd,EACA,QAAS,IAAM,CACb,aAAa,WAAW,OAAO,EAC/BkQ,EAAQ,IAAI,CACd,EACF,EAIIG,EAAgB7F,GAAYkF,GAAQ,MAAO,CAC/C,UAAY1P,GAAS,CACnB,aAAa,QAAQ,QAASA,EAAK,KAAK,EACxCkQ,EAAQlQ,EAAK,IAAI,EACjB2J,EAAY,aAAa,cAAe3J,EAAK,IAAI,EACjDsQ,EAAM,QAAQ,OAAO,CACvB,EACA,QAAUjU,GAAiB,CACzBiU,EAAM,MAAMjU,EAAM,SAAW,MAAM,CACrC,EACD,EAGKkU,EAAmB/F,GAAYkF,GAAQ,SAAU,CACrD,UAAY1P,GAAS,CACnB,aAAa,QAAQ,QAASA,EAAK,KAAK,EACxCkQ,EAAQlQ,EAAK,IAAI,EACjB2J,EAAY,aAAa,cAAe3J,EAAK,IAAI,EACjDsQ,EAAM,QAAQ,OAAO,CACvB,EACA,QAAUjU,GAAiB,CACzBiU,EAAM,MAAMjU,EAAM,SAAW,MAAM,CACrC,EACD,EAGKmU,EAAwBhG,GAAYkF,GAAQ,cAAe,CAC/D,UAAY1P,GAAS,CACnBkQ,EAAQlQ,CAAI,EACZ2J,EAAY,aAAa,cAAe3J,CAAI,EAC5CsQ,EAAM,QAAQ,WAAW,CAC3B,EACA,QAAUjU,GAAiB,CACzBiU,EAAM,MAAMjU,EAAM,SAAW,MAAM,CACrC,EACD,EAGKoU,EAAQ,MAAOZ,GAAkC,CACrD,MAAMQ,EAAc,YAAYR,CAAW,CAC7C,EAGMa,EAAW,MAAOb,GAAqC,CAC3D,MAAMU,EAAiB,YAAYV,CAAW,CAChD,EAGMc,EAAS,IAAM,CACnB,aAAa,WAAW,OAAO,EAC/BT,EAAQ,IAAI,EACZvG,EAAY,QACZ2G,EAAM,QAAQ,OAAO,CACvB,EAGMM,EAAgB,MAAO5Q,GAAwB,CACnD,MAAMwQ,EAAsB,YAAYxQ,CAAI,CAC9C,EAGA6Q,YAAU,IAAM,CACA,aAAa,QAAQ,OAAO,GAExCX,EAAQ,IAAI,CAEhB,EAAG,EAAE,EAEL,MAAMtX,EAAyB,CAC7B,KAAAqX,EACA,QAASG,EACT,MAAAK,EACA,SAAAC,EACA,OAAAC,EACA,cAAAC,CAAA,EAGF,OAAOE,MAAChB,GAAY,SAAZ,CAAqB,MAAAlX,EAAe,SAAAkR,CAAA,CAAS,CACvD,EAGaiH,GAAU,IAAuB,CAC5C,MAAM/P,EAAUgQ,aAAWlB,EAAW,EACtC,GAAI9O,IAAY,OACd,MAAM,IAAI,MAAM,6CAA6C,EAE/D,OAAOA,CACT,ECjRMiQ,GAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUnBC,GAAQD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQfE,GAAWF,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlBG,GAAiBH,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxBI,GAAWJ,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAelBK,GAAcL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrBM,GAASN,EAAOO,EAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBpBC,GAAkBR,EAAOM,EAAM;AAAA;AAAA,EAI/BG,GAAYT,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnBU,GAAWV,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBlBW,GAAqB,IAAM,CAC/B,KAAM,CAAE,KAAA5B,CAAA,EAASc,GAAA,EAEjB,OAAKd,SAeFgB,GAAA,CACC,UAAAH,MAACK,IAAM,iBAAK,EAEZL,MAACO,GAAA,CACC,SAAAS,OAACR,GAAA,CACC,UAAAQ,OAAC,MAAG,kBAAM7B,EAAK,SAAS,KAAC,SACxB,KAAE,gBAAIA,EAAK,OAAM,SACjB,KAAE,iBAAKA,EAAK,YAAW,SACvB,KAAE,gBAAIA,EAAK,OAAM,SACjB,KAAE,gBAAIA,EAAK,MAAK,GACnB,EACF,SAECsB,GAAA,CACC,UAAAT,MAACU,GAAA,CAAO,GAAG,UAAU,gBAAI,EACzBV,MAACY,GAAA,CAAgB,GAAG,QAAQ,gBAAI,EAChCZ,MAACY,GAAA,CAAgB,GAAG,eAAe,eAAG,EACtCZ,MAACY,GAAA,CAAgB,GAAG,WAAW,gBAAI,GACrC,SAECC,GAAA,CACC,UAAAG,OAACF,GAAA,CACC,UAAAd,MAAC,MAAI,WAAK,MAAM,EAChBA,MAAC,KAAE,gBAAI,GACT,SACCc,GAAA,CACC,UAAAd,MAAC,MAAI,WAAK,WAAW,EACrBA,MAAC,KAAE,gBAAI,GACT,SACCc,GAAA,CACC,UAAAd,MAAC,MAAI,WAAK,MAAM,EAChBA,MAAC,KAAE,cAAE,GACP,SACCc,GAAA,CACC,UAAAd,MAAC,MAAI,WAAK,KAAK,EACfA,MAAC,KAAE,cAAE,GACP,GACF,GACF,SAnDGG,GAAA,CACC,UAAAH,MAACK,IAAM,iBAAK,EACZL,MAACM,IAAS,0CAEV,QACCG,GAAA,CACC,SAAAT,MAACU,IAAO,GAAG,SAAS,gBAAI,EAC1B,GACF,CA6CN,EC7KMP,GAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnBa,GAAYb,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWnBC,GAAQD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQfc,GAAOd,EAAO;AAAA;AAAA;AAAA;AAAA,EAMde,GAAaf,EAAO;AAAA;AAAA;AAAA;AAAA,EAMpBgB,GAAQhB,EAAO;AAAA;AAAA;AAAA;AAAA,EAMfiB,GAAQjB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBfM,GAASN,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BhBkB,GAAWlB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBlBmB,GAAenB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtBoB,GAAsB,IAAM,CAChC,KAAM,CAACC,EAAOC,CAAQ,EAAIrC,WAAS,EAAE,EAC/B,CAACsC,EAAUC,CAAW,EAAIvC,WAAS,EAAE,EACrC,CAACwC,EAASC,CAAU,EAAIzC,WAAS,EAAK,EACtC,CAAC9T,EAAOwW,CAAQ,EAAI1C,WAAS,EAAE,EAE/B,CAAE,MAAAM,CAAA,EAAUM,GAAA,EAEZ+B,EAAe,MAAO1b,GAAuB,CACjDA,EAAE,iBACFyb,EAAS,EAAE,EACXD,EAAW,EAAI,EAEf,GAAI,CACF,MAAMnC,EAAM,CAAE,MAAA8B,EAAO,SAAAE,EAAU,CACjC,OAASM,EAAK,CACZF,EAASE,aAAe,MAAQA,EAAI,QAAU,MAAM,CACtD,SACEH,EAAW,EAAK,CAClB,CACF,EAEA,OACE9B,MAACG,GAAA,CACC,SAAAa,OAACC,GAAA,CACC,UAAAjB,MAACK,IAAM,cAAE,EAER9U,GAASyU,MAACuB,GAAA,CAAc,SAAAhW,CAAA,CAAM,EAE/ByV,OAACE,GAAA,CAAK,SAAUc,EACd,UAAAhB,OAACG,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,QAAQ,cAAE,EACzBpB,MAACqB,GAAA,CACC,GAAG,QACH,KAAK,QACL,YAAY,QACZ,MAAOI,EACP,SAAWnb,GAAMob,EAASpb,EAAE,OAAO,KAAK,EACxC,SAAQ,IACV,EACF,SAEC6a,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,WAAW,cAAE,EAC5BpB,MAACqB,GAAA,CACC,GAAG,WACH,KAAK,WACL,YAAY,QACZ,MAAOM,EACP,SAAWrb,GAAMsb,EAAYtb,EAAE,OAAO,KAAK,EAC3C,SAAQ,IACV,EACF,EAEA0Z,MAACU,IAAO,KAAK,SAAS,SAAUmB,EAC7B,SAAAA,EAAU,SAAW,KACxB,GACF,SAECP,GAAA,CAAS,oBACDtB,MAACW,GAAA,CAAK,GAAG,YAAY,gBAAI,GAClC,GACF,EACF,CAEJ,ECvLMR,GAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnB8B,GAAe9B,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtBC,GAAQD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQfc,GAAOd,EAAO;AAAA;AAAA;AAAA;AAAA,EAMde,GAAaf,EAAO;AAAA;AAAA;AAAA;AAAA,EAMpBgB,GAAQhB,EAAO;AAAA;AAAA;AAAA;AAAA,EAMfiB,GAAQjB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBfM,GAASN,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BhBkB,GAAWlB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBlBmB,GAAenB,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB+B,GAAyB,IAAM,CACnC,KAAM,CAACC,EAAUC,CAAW,EAAIhD,WAAS,EAAE,EACrC,CAACoC,EAAOC,CAAQ,EAAIrC,WAAS,EAAE,EAC/B,CAACsC,EAAUC,CAAW,EAAIvC,WAAS,EAAE,EACrC,CAACiD,EAAiBC,CAAkB,EAAIlD,WAAS,EAAE,EACnD,CAACwC,EAASC,CAAU,EAAIzC,WAAS,EAAK,EACtC,CAAC9T,EAAOwW,CAAQ,EAAI1C,WAAS,EAAE,EAE/B,CAAE,SAAAO,CAAA,EAAaK,GAAA,EAEf+B,EAAe,MAAO1b,GAAuB,CAKjD,GAJAA,EAAE,iBACFyb,EAAS,EAAE,EAGPJ,IAAaW,EAAiB,CAChCP,EAAS,YAAY,EACrB,MACF,CAEA,GAAIJ,EAAS,OAAS,EAAG,CACvBI,EAAS,WAAW,EACpB,MACF,CAEAD,EAAW,EAAI,EAEf,GAAI,CACF,MAAMlC,EAAS,CAAE,SAAAwC,EAAU,MAAAX,EAAO,SAAAE,EAAU,CAC9C,OAASM,EAAK,CACZF,EAASE,aAAe,MAAQA,EAAI,QAAU,MAAM,CACtD,SACEH,EAAW,EAAK,CAClB,CACF,EAEA,OACE9B,MAACG,GAAA,CACC,SAAAa,OAACkB,GAAA,CACC,UAAAlC,MAACK,IAAM,cAAE,EAER9U,GAASyU,MAACuB,GAAA,CAAc,SAAAhW,CAAA,CAAM,EAE/ByV,OAACE,GAAA,CAAK,SAAUc,EACd,UAAAhB,OAACG,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,WAAW,eAAG,EAC7BpB,MAACqB,GAAA,CACC,GAAG,WACH,KAAK,OACL,YAAY,SACZ,MAAOe,EACP,SAAW9b,GAAM+b,EAAY/b,EAAE,OAAO,KAAK,EAC3C,SAAQ,IACV,EACF,SAEC6a,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,QAAQ,cAAE,EACzBpB,MAACqB,GAAA,CACC,GAAG,QACH,KAAK,QACL,YAAY,QACZ,MAAOI,EACP,SAAWnb,GAAMob,EAASpb,EAAE,OAAO,KAAK,EACxC,SAAQ,IACV,EACF,SAEC6a,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,WAAW,cAAE,EAC5BpB,MAACqB,GAAA,CACC,GAAG,WACH,KAAK,WACL,YAAY,cACZ,MAAOM,EACP,SAAWrb,GAAMsb,EAAYtb,EAAE,OAAO,KAAK,EAC3C,SAAQ,IACV,EACF,SAEC6a,GAAA,CACC,UAAAnB,MAACoB,GAAA,CAAM,QAAQ,kBAAkB,gBAAI,EACrCpB,MAACqB,GAAA,CACC,GAAG,kBACH,KAAK,WACL,YAAY,UACZ,MAAOiB,EACP,SAAWhc,GAAMic,EAAmBjc,EAAE,OAAO,KAAK,EAClD,SAAQ,IACV,EACF,EAEA0Z,MAACU,IAAO,KAAK,SAAS,SAAUmB,EAC7B,SAAAA,EAAU,SAAW,KACxB,GACF,SAECP,GAAA,CAAS,mBACFtB,MAACW,GAAA,CAAK,GAAG,SAAS,gBAAI,GAC9B,GACF,EACF,CAEJ,4+BC/Na6B,GAAeC,OAAK,IAAAC,EAAA,IAAM,OAAO,wBAAmB,kDAAC,EACrDC,GAAiBF,OAAK,IAAAC,EAAA,IAAM,OAAO,0BAAqB,8BAAC,EACzDE,GAAsBH,OAAK,IAAAC,EAAA,IAAM,OAAO,+BAA0B,uCAAC,EACnEG,GAAuBJ,OAAK,IAAAC,EAAA,IAAM,OAAO,gCAA2B,uCAAC,EACrEI,GAAkBL,OAAK,IAAAC,EAAA,IAAM,OAAO,2BAAsB,8BAAC,EAG3CD,OAAK,IAAAC,EAAA,IAAM,OAAO,yBAA8B,iCAAC,EAC9CD,OAAK,IAAAC,EAAA,IAAM,OAAO,4BAAiC,qCAAC,EACtDD,OAAK,IAAAC,EAAA,IAAM,OAAO,0BAA+B,oCAAC,EAC/CD,OAAK,IAAAC,EAAA,IAAM,OAAO,6BAAkC,6BAAC,EAI/E,MAAMK,GAAyB,UAAM,OAAO,gCAA2B,MAAE,KAAKC,GAAUA,EAAO,gBAAgB,EACzGC,GAA0B,UAAM,OAAO,iCAA4B,2BAAE,KAAKD,GAAUA,EAAO,iBAAiB,EAC5GE,GAAuB,UAAM,OAAO,8BAAyB,2BAAE,KAAKF,GAAUA,EAAO,cAAc,EACnGG,GAA2B,UAAM,OAAO,kCAA6B,4BAAE,KAAKH,GAAUA,EAAO,kBAAkB,EAG/GI,GAAwB,IAAM,CAOzCL,GAAA,EACAE,GAAA,EACAC,GAAA,CACF,EASaG,GAAuB,IAAM,CACxCD,GAAA,EAKAD,GAAA,CACF,EAGO,MAAMG,EAAmB,CAG9B,aAAa,iBAAiBC,EAAuBC,EAA4B,CAC/E,GAAI,MAAK,oBAAoB,IAAID,CAAa,EAI9C,GAAI,CACF,MAAMC,EAAA,EACN,KAAK,oBAAoB,IAAID,CAAa,EAC1C,QAAQ,IAAI,YAAYA,CAAa,EAAE,CACzC,OAAShY,EAAO,CACd,QAAQ,MAAM,YAAYgY,CAAa,GAAIhY,CAAK,CAClD,CACF,CAEA,aAAa,eAAgB,CAC3B,GAAI,wBAAyB,OAC3B,OAAO,IAAI,QAAeF,GAAY,CACpC,oBAAoB,IAAM,CACxBgY,GAAA,EACAhY,EAAA,CACF,CAAC,CACH,CAAC,EAGD,WAAW,IAAM,CACfgY,GAAA,CACF,EAAG,GAAG,CAEV,CAEA,aAAa,0BAA2B,CACtC,MAAMI,EAAS,CAAC,YAAa,aAAc,SAAS,EAE9CC,EAAc,IAAM,CACxBN,GAAA,EACAK,EAAO,QAAQxS,GAAS,CACtB,SAAS,oBAAoBA,EAAOyS,CAAW,CACjD,CAAC,CACH,EAEAD,EAAO,QAAQxS,GAAS,CACtB,SAAS,iBAAiBA,EAAOyS,EAAa,CAAE,KAAM,GAAM,QAAS,GAAM,CAC7E,CAAC,CACH,CAEA,OAAO,wBAAyB,CAC9B,OAAO,MAAM,KAAK,KAAK,mBAAmB,CAC5C,CAEA,OAAO,0BAA2B,CAChC,KAAK,oBAAoB,OAC3B,CACF,CAtDEC,EADWL,GACI,sBAAsB,IAAI,KA6GpC,MAAMM,EAAc,CAKzB,OAAO,IAAIvZ,EAAavC,EAAY+b,EAAe,EAAG,CAEpD,KAAO,KAAK,UAAYA,EAAO,KAAK,gBAAkB,KAAK,MAAM,KAAO,GAAG,CACzE,MAAMC,EAAW,KAAK,MAAM,OAAO,OAAO,MAC1C,KAAK,OAAOA,CAAS,CACvB,CAEA,KAAK,MAAM,IAAIzZ,EAAKvC,CAAK,EACzB,KAAK,WAAa+b,CACpB,CAEA,OAAO,IAAIxZ,EAAa,CACtB,OAAO,KAAK,MAAM,IAAIA,CAAG,CAC3B,CAEA,OAAO,OAAOA,EAAa,CACrB,KAAK,MAAM,IAAIA,CAAG,IACpB,KAAK,MAAM,OAAOA,CAAG,EAErB,KAAK,UAAY,KAAK,IAAI,EAAG,KAAK,UAAY,IAAI,EAEtD,CAEA,OAAO,OAAQ,CACb,KAAK,MAAM,QACX,KAAK,UAAY,CACnB,CAEA,OAAO,cAAe,CACpB,MAAO,CACL,KAAM,KAAK,MAAM,KACjB,YAAa,KAAK,UAClB,QAAS,KAAK,eAElB,CAEA,OAAO,SAAU,CAEf,MAAM0Z,EAAM,KAAK,MACXC,EAAS,EAAI,GAAK,IAExB,SAAW,CAAC3Z,EAAKvC,CAAK,IAAK,KAAK,MAAM,UAChCA,EAAM,WAAaic,EAAMjc,EAAM,UAAYkc,GAC7C,KAAK,OAAO3Z,CAAG,CAGrB,CACF,CAnDEsZ,EADWC,GACa,iBAAiB,GAAK,KAAO,MACrDD,EAFWC,GAEI,QAAQ,IAAI,KAC3BD,EAHWC,GAGI,YAAY,GAoD7B,YAAY,IAAM,CAChBA,GAAc,SAChB,EAAG,GAAK,ECjNMxD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAaK6D,GAASA,EAAM,SAAW,IAAM,MAAM;AAAA;AAAA;AAAA,EAKjD7D,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkCNA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBPA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYLA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BEA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYbA,EAAO;AAAA;AAAA;AAAA,WAGjB6D,GAASA,EAAM,SAAW,QAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EClIrD,MAAMC,GAAkB9D,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB+D,GAAM/D,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQbgE,GAAOhE,EAAOO,EAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAclB0D,GAAWjE,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlBkE,EAAUlE,EAAOO,EAAI;AAAA,WAChBsD,GAASA,EAAM,QAAU,UAAY,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAapEzD,GAAWJ,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlBmE,GAAYnE,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnBoE,GAAepE,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAetBqE,GAAarE,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWpBsE,GAAmB,IAAM,CAC7B,KAAM,CAAE,KAAAvF,EAAM,OAAAU,CAAA,EAAWI,GAAA,EACnB0E,EAAWC,GAAA,EAEX/b,EAAYgc,GAAiBF,EAAS,WAAaE,EAEzD,OACE7E,MAACkE,GAAA,CACC,SAAAlD,OAACmD,GAAA,CACC,UAAAnE,MAACoE,GAAA,CAAK,GAAG,IAAI,mBAEb,EAECjF,EACC6B,OAAA8D,WAAA,CACE,UAAA9D,OAACqD,GAAA,CACC,UAAArE,MAACsE,GAAQ,GAAG,IAAI,QAASzb,EAAS,GAAG,EAAG,cAExC,EACAmX,MAACsE,GAAQ,GAAG,QAAQ,QAASzb,EAAS,OAAO,EAAG,cAEhD,EACAmX,MAACsE,GAAQ,GAAG,eAAe,QAASzb,EAAS,cAAc,EAAG,eAE9D,EACAmX,MAACsE,GAAQ,GAAG,gBAAgB,QAASzb,EAAS,eAAe,EAAG,cAEhE,EACAmX,MAACsE,GAAQ,GAAG,WAAW,QAASzb,EAAS,UAAU,EAAG,gBAEtD,GACF,SAEC2X,GAAA,CACC,UAAAQ,OAACuD,GAAA,CACC,UAAAvD,OAAC,QAAK,gBAAI7B,EAAK,OAAM,SACpB,QAAK,eAAGA,EAAK,OAAM,SACnB,QAAK,eAAGA,EAAK,MAAK,GACrB,EACAa,MAACwE,GAAA,CAAa,QAAS3E,EAAQ,cAE/B,GACF,GACF,SAECwE,GAAA,CACC,UAAArE,MAACsE,GAAQ,GAAG,SAAS,QAASzb,EAAS,QAAQ,EAAG,cAElD,EACAmX,MAACsE,GAAQ,GAAG,YAAY,QAASzb,EAAS,WAAW,EAAG,cAExD,GACF,EAGFmX,MAACyE,IAAW,aAEZ,GACF,EACF,CAEJ,ECrKMM,GAAkB3E,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzB4E,GAAgB5E,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB6E,GAAa7E,EAAO;AAAA;AAAA;AAAA;AAAA,EAMpB8E,GAAc9E,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYrB+E,GAAa/E,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWpBgF,GAAmB,IAErBpF,MAAC+E,GAAA,CACC,SAAA/D,OAACgE,GAAA,CACC,UAAAhE,OAACkE,GAAA,CACC,UAAAlF,MAACmF,GAAA,CAAW,KAAK,SAAS,gBAAI,EAC9BnF,MAACmF,GAAA,CAAW,KAAK,WAAW,gBAAI,EAChCnF,MAACmF,GAAA,CAAW,KAAK,SAAS,gBAAI,EAC9BnF,MAACmF,GAAA,CAAW,KAAK,WAAW,gBAAI,GAClC,EACAnF,MAACiF,IAAW,6CAEZ,GACF,EACF,EC/BG,MAAMI,EAAW,CAItB,YAAYC,EAAmBzB,EAAe,EAAG,CAHzCF,EAAA,cACAA,EAAA,aAGN,KAAK,MAAQ2B,EAAM,OAAW,CAAC,GAAGC,CAAG,CAAC,EACtC,KAAK,KAAO1B,CACd,CAGQ,gBAAgB0B,EAAaC,EAAsB,CACzD,OAAOD,GAAO,GAAKA,EAAM,KAAK,MAAQC,GAAO,GAAKA,EAAM,KAAK,IAC/D,CAGO,YAAYC,EAAgBC,EAAyB,CAC1D,MAAMC,EAAU,KAAK,IAAIF,EAAK,IAAMC,EAAK,GAAG,EACtCE,EAAU,KAAK,IAAIH,EAAK,IAAMC,EAAK,GAAG,EAC5C,OAAQC,IAAY,GAAKC,IAAY,GAAOD,IAAY,GAAKC,IAAY,CAC3E,CAGO,UAAuB,CAC5B,OAAO,KAAK,MAAM,OAAW,CAAC,GAAGL,CAAG,CAAC,CACvC,CAGQ,SAASE,EAAgBC,EAAsB,CACrD,MAAMG,EAAO,KAAK,MAAMJ,EAAK,GAAG,EAAEA,EAAK,GAAG,EAC1C,KAAK,MAAMA,EAAK,GAAG,EAAEA,EAAK,GAAG,EAAI,KAAK,MAAMC,EAAK,GAAG,EAAEA,EAAK,GAAG,EAC9D,KAAK,MAAMA,EAAK,GAAG,EAAEA,EAAK,GAAG,EAAIG,CACnC,CAGQ,uBAAiC,CACvC,MAAMC,EAAmB,GAEzB,QAASP,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CACxC,IAAIQ,EAAQ,EACRC,EAAa,KAAK,MAAMT,CAAG,EAAE,CAAC,EAElC,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,GAAI,KAAK,MAAMD,CAAG,EAAEC,CAAG,IAAMQ,GAAcA,IAAe,EACxDD,QACK,CACL,GAAIA,GAAS,EAAG,CACd,MAAME,EAAwB,GAC9B,QAASnb,EAAI0a,EAAMO,EAAOjb,EAAI0a,EAAK1a,IACjCmb,EAAU,KAAK,CAAE,IAAAV,EAAK,IAAKza,EAAG,EAEhCgb,EAAQ,KAAK,CACX,UAAAG,EACA,KAAM,aACN,MAAOF,EAAQ,GAChB,CACH,CACAA,EAAQ,EACRC,EAAa,KAAK,MAAMT,CAAG,EAAEC,CAAG,CAClC,CAIF,GAAIO,GAAS,EAAG,CACd,MAAME,EAAwB,GAC9B,QAASnb,EAAI,KAAK,KAAOib,EAAOjb,EAAI,KAAK,KAAMA,IAC7Cmb,EAAU,KAAK,CAAE,IAAAV,EAAK,IAAKza,EAAG,EAEhCgb,EAAQ,KAAK,CACX,UAAAG,EACA,KAAM,aACN,MAAOF,EAAQ,GAChB,CACH,CACF,CAEA,OAAOD,CACT,CAGQ,qBAA+B,CACrC,MAAMA,EAAmB,GAEzB,QAASN,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CACxC,IAAIO,EAAQ,EACRC,EAAa,KAAK,MAAM,CAAC,EAAER,CAAG,EAElC,QAASD,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,GAAI,KAAK,MAAMA,CAAG,EAAEC,CAAG,IAAMQ,GAAcA,IAAe,EACxDD,QACK,CACL,GAAIA,GAAS,EAAG,CACd,MAAME,EAAwB,GAC9B,QAASnb,EAAIya,EAAMQ,EAAOjb,EAAIya,EAAKza,IACjCmb,EAAU,KAAK,CAAE,IAAKnb,EAAG,IAAA0a,EAAK,EAEhCM,EAAQ,KAAK,CACX,UAAAG,EACA,KAAM,WACN,MAAOF,EAAQ,GAChB,CACH,CACAA,EAAQ,EACRC,EAAa,KAAK,MAAMT,CAAG,EAAEC,CAAG,CAClC,CAIF,GAAIO,GAAS,EAAG,CACd,MAAME,EAAwB,GAC9B,QAASnb,EAAI,KAAK,KAAOib,EAAOjb,EAAI,KAAK,KAAMA,IAC7Cmb,EAAU,KAAK,CAAE,IAAKnb,EAAG,IAAA0a,EAAK,EAEhCM,EAAQ,KAAK,CACX,UAAAG,EACA,KAAM,WACN,MAAOF,EAAQ,GAChB,CACH,CACF,CAEA,OAAOD,CACT,CAGO,cAAcjC,EAAe,EAAe,CACjD,MAAMyB,EAAoB,GAC1B,QAASC,EAAM,EAAGA,EAAM1B,EAAM0B,IAAO,CACnCD,EAAMC,CAAG,EAAI,GACb,QAASC,EAAM,EAAGA,EAAM3B,EAAM2B,IAC5BF,EAAMC,CAAG,EAAEC,CAAG,EAAI,KAAK,MAAM,KAAK,SAAW,CAAC,EAAI,CAEtD,CACA,OAAOF,CACT,CAGO,YAAYA,EAAmBY,EAAgBC,EAAuB,CAO3E,GALI,CAAC,KAAK,gBAAgBD,EAAK,IAAKA,EAAK,GAAG,GAAK,CAAC,KAAK,gBAAgBC,EAAG,IAAKA,EAAG,GAAG,GAKjF,CAAC,KAAK,YAAYD,EAAMC,CAAE,EAC5B,MAAO,GAIT,MAAMC,EAAYd,EAAM,OAAW,CAAC,GAAGC,CAAG,CAAC,EACrCM,EAAOO,EAAUF,EAAK,GAAG,EAAEA,EAAK,GAAG,EACzCE,EAAUF,EAAK,GAAG,EAAEA,EAAK,GAAG,EAAIE,EAAUD,EAAG,GAAG,EAAEA,EAAG,GAAG,EACxDC,EAAUD,EAAG,GAAG,EAAEA,EAAG,GAAG,EAAIN,EAG5B,MAAMQ,EAAgB,KAAK,MAC3B,KAAK,MAAQD,EACb,MAAMN,EAAU,KAAK,cACrB,YAAK,MAAQO,EAENP,EAAQ,OAAS,CAC1B,CAGO,aAAuB,CAC5B,MAAMQ,EAAoB,KAAK,wBACzBC,EAAkB,KAAK,sBAC7B,MAAO,CAAC,GAAGD,EAAmB,GAAGC,CAAe,CAClD,CAGQ,cAAcT,EAA0B,CAC9C,IAAIU,EAAa,EACjB,MAAMC,MAAe,IAErB,OAAAX,EAAQ,QAAQY,GAAS,CACvBF,GAAcE,EAAM,MACpBA,EAAM,UAAU,QAAQC,GAAO,CAC7BF,EAAS,IAAI,GAAGE,EAAI,GAAG,IAAIA,EAAI,GAAG,EAAE,CACtC,CAAC,CACH,CAAC,EAEDF,EAAS,QAAQG,GAAU,CACzB,KAAM,CAACrB,EAAKC,CAAG,EAAIoB,EAAO,MAAM,GAAG,EAAE,IAAI,MAAM,EAC/C,KAAK,MAAMrB,CAAG,EAAEC,CAAG,EAAI,CACzB,CAAC,EAEMgB,CACT,CAGQ,cAAwB,CAC9B,IAAIK,EAAa,GAEjB,QAASrB,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CAExC,IAAIsB,EAAW,KAAK,KAAO,EAE3B,QAASvB,EAAM,KAAK,KAAO,EAAGA,GAAO,EAAGA,IAClC,KAAK,MAAMA,CAAG,EAAEC,CAAG,IAAM,IACvBD,IAAQuB,IACV,KAAK,MAAMA,CAAQ,EAAEtB,CAAG,EAAI,KAAK,MAAMD,CAAG,EAAEC,CAAG,EAC/C,KAAK,MAAMD,CAAG,EAAEC,CAAG,EAAI,EACvBqB,EAAa,IAEfC,IAGN,CAEA,OAAOD,CACT,CAGQ,iBAAwB,CAC9B,QAASrB,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,QAASD,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAC7B,KAAK,MAAMA,CAAG,EAAEC,CAAG,IAAM,IAE3B,KAAK,MAAMD,CAAG,EAAEC,CAAG,EAAI,KAAK,MAAM,KAAK,SAAW,CAAC,EAAI,EAI/D,CAGO,SAASU,EAAgBC,EAA0B,CAExD,GAAI,CAAC,KAAK,gBAAgBD,EAAK,IAAKA,EAAK,GAAG,GAAK,CAAC,KAAK,gBAAgBC,EAAG,IAAKA,EAAG,GAAG,EACnF,MAAO,CACL,QAAS,GACT,QAAS,GACT,SAAU,KAAK,MACf,MAAO,EACP,QAAS,IAKb,GAAI,CAAC,KAAK,YAAYD,EAAMC,CAAE,EAC5B,MAAO,CACL,QAAS,GACT,QAAS,GACT,SAAU,KAAK,MACf,MAAO,EACP,QAAS,IAKb,KAAK,SAASD,EAAMC,CAAE,EAGtB,MAAML,EAAU,KAAK,cAErB,GAAIA,EAAQ,SAAW,EAErB,YAAK,SAASI,EAAMC,CAAE,EACf,CACL,QAAS,GACT,QAAS,GACT,SAAU,KAAK,MACf,MAAO,EACP,QAAS,IAKb,IAAIK,EAAa,EACbO,EAAsB,GACtBC,EAAa,GAEjB,KAAOlB,EAAQ,OAAS,GAAG,CACzBiB,EAAW,KAAK,GAAGjB,CAAO,EAC1BU,GAAc,KAAK,cAAcV,CAAO,EAGxC,KAAK,eAGL,KAAK,kBAGL,MAAMmB,EAAa,KAAK,cACxB,GAAIA,EAAW,OAAS,EACtBD,EAAa,GACblB,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGmB,CAAU,MAE/C,MAEJ,CAEA,MAAO,CACL,QAAS,GACT,QAASF,EACT,SAAU,KAAK,MAAM,OAAW,CAAC,GAAGxB,CAAG,CAAC,EACxC,MAAOiB,EACP,QAASQ,CAAA,CAEb,CAKO,kBAA4B,CACjC,QAASzB,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CAExC,GAAIA,EAAM,KAAK,KAAO,EAAG,CAEvB,GADA,KAAK,SAAS,CAAE,IAAAD,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAAD,EAAK,IAAKC,EAAM,EAAG,EAC7C,KAAK,cAAc,OAAS,EAC9B,YAAK,SAAS,CAAE,IAAAD,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAAD,EAAK,IAAKC,EAAM,EAAG,EAC1C,GAET,KAAK,SAAS,CAAE,IAAAD,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAAD,EAAK,IAAKC,EAAM,EAAG,CACnD,CAGA,GAAID,EAAM,KAAK,KAAO,EAAG,CAEvB,GADA,KAAK,SAAS,CAAE,IAAAA,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAKD,EAAM,EAAG,IAAAC,EAAK,EAC7C,KAAK,cAAc,OAAS,EAC9B,YAAK,SAAS,CAAE,IAAAD,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAKD,EAAM,EAAG,IAAAC,EAAK,EAC1C,GAET,KAAK,SAAS,CAAE,IAAAD,EAAK,IAAAC,CAAA,EAAO,CAAE,IAAKD,EAAM,EAAG,IAAAC,EAAK,CACnD,CACF,CAEF,MAAO,EACT,CAGO,SAAmD,CACxD,QAASD,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CAExC,GAAIA,EAAM,KAAK,KAAO,EAAG,CACvB,MAAMU,EAAO,CAAE,IAAAX,EAAK,IAAAC,CAAA,EACdW,EAAK,CAAE,IAAAZ,EAAK,IAAKC,EAAM,GAE7B,GADA,KAAK,SAASU,EAAMC,CAAE,EAClB,KAAK,cAAc,OAAS,EAC9B,YAAK,SAASD,EAAMC,CAAE,EACf,CAAE,KAAAD,EAAM,GAAAC,CAAA,EAEjB,KAAK,SAASD,EAAMC,CAAE,CACxB,CAGA,GAAIZ,EAAM,KAAK,KAAO,EAAG,CACvB,MAAMW,EAAO,CAAE,IAAAX,EAAK,IAAAC,CAAA,EACdW,EAAK,CAAE,IAAKZ,EAAM,EAAG,IAAAC,CAAA,EAE3B,GADA,KAAK,SAASU,EAAMC,CAAE,EAClB,KAAK,cAAc,OAAS,EAC9B,YAAK,SAASD,EAAMC,CAAE,EACf,CAAE,KAAAD,EAAM,GAAAC,CAAA,EAEjB,KAAK,SAASD,EAAMC,CAAE,CACxB,CACF,CAEF,OAAO,IACT,CAGO,eAAeL,EAAkBoB,EAA0B,EAAW,CAC3E,IAAIV,EAAa,EACjB,OAAAV,EAAQ,QAAQY,GAAS,CACvB,MAAMS,EAAYT,EAAM,UAAU,OAAS,GACrCU,EAAc,KAAK,IAAI,GAAIV,EAAM,UAAU,OAAS,GAAK,CAAC,EAChEF,IAAeW,EAAYC,GAAeF,CAC5C,CAAC,EACMV,CACT,CAGO,SAAgB,CACrB,MAAMa,EAAiB,GAGvB,QAAS9B,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAC7B,KAAK,MAAMD,CAAG,EAAEC,CAAG,IAAM,GAC3B6B,EAAK,KAAK,KAAK,MAAM9B,CAAG,EAAEC,CAAG,CAAC,EAMpC,QAAS1a,EAAIuc,EAAK,OAAS,EAAGvc,EAAI,EAAGA,IAAK,CACxC,MAAM2P,EAAI,KAAK,MAAM,KAAK,UAAY3P,EAAI,EAAE,EAC3C,CAACuc,EAAKvc,CAAC,EAAGuc,EAAK5M,CAAC,CAAC,EAAI,CAAC4M,EAAK5M,CAAC,EAAG4M,EAAKvc,CAAC,CAAC,CACzC,CAGA,IAAIwc,EAAW,EACf,QAAS/B,EAAM,EAAGA,EAAM,KAAK,KAAMA,IACjC,QAASC,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAC7B,KAAK,MAAMD,CAAG,EAAEC,CAAG,IAAM,IAC3B,KAAK,MAAMD,CAAG,EAAEC,CAAG,EAAI6B,EAAKC,GAAU,EAI9C,CAGO,aAAahC,EAA+B,CACjD,MAAMiC,EAAWjC,EAAM,OAAW,CAAC,GAAGC,CAAG,CAAC,EACpCc,EAAgB,KAAK,MAC3B,KAAK,MAAQkB,EACb,KAAK,UACL,MAAMnd,EAAS,KAAK,MAAM,OAAW,CAAC,GAAGmb,CAAG,CAAC,EAC7C,YAAK,MAAQc,EACNjc,CACT,CAGO,oBAA8B,CACnC,IAAIuN,EAAU,GACd,QAAS6N,EAAM,EAAGA,EAAM,KAAK,KAAMA,IAAO,CACxC,IAAIgC,EAAa,KAAK,KAAO,EAC7B,QAASjC,EAAM,KAAK,KAAO,EAAGA,GAAO,EAAGA,IAClC,KAAK,MAAMA,CAAG,EAAEC,CAAG,IAAM,IACvBgC,IAAejC,IACjB,KAAK,MAAMiC,CAAU,EAAEhC,CAAG,EAAI,KAAK,MAAMD,CAAG,EAAEC,CAAG,EACjD,KAAK,MAAMD,CAAG,EAAEC,CAAG,EAAI,EACvB7N,EAAU,IAEZ6P,IAGN,CACA,OAAO7P,CACT,CAGO,iBAAiB2N,EAA6D,CACnF,GAAIA,EAAO,CACT,MAAMe,EAAgB,KAAK,MAC3B,KAAK,MAAQf,EACb,MAAMlb,EAAS,KAAK,UACpB,YAAK,MAAQic,EACNjc,CACT,CACA,OAAO,KAAK,SACd,CACF,CCpdA,MAAMqd,GAAY,CAChB,GAAI,EACJ,KAAM,MACN,WAAY,OACZ,YAAa,IACb,SAAU,GACV,UAAW,EACX,WAAY,CACV,CAAE,KAAM,QAAkB,OAAQ,IAAM,YAAa,UACrD,CAAE,KAAM,gBAA0B,OAAQ,GAAI,YAAa,OAAO,CAEtE,EAEMC,GAAoB,CAAC7D,EAAe,IAAkB,CAC1D,MAAMyB,EAAoB,GAC1B,QAASxa,EAAI,EAAGA,EAAI+Y,EAAM/Y,IAAK,CAC7Bwa,EAAMxa,CAAC,EAAI,GACX,QAAS2P,EAAI,EAAGA,EAAIoJ,EAAMpJ,IACxB6K,EAAMxa,CAAC,EAAE2P,CAAC,EAAI,KAAK,MAAM,KAAK,SAAW,CAAC,EAAI,CAElD,CACA,OAAO6K,CACT,EAwEMqC,GAA8B,CAClC,MAAO,GACP,MAAO,EACP,MAAO,EACP,SAAU,GACV,UAAW,GACX,MAAO,EACP,aAAc,EACd,YAAa,IACb,OAAQ,OACR,WAAY,UACZ,aAAc,KACd,MAAO,EACP,SAAU,CACR,OAAQ,EACR,KAAM,EACN,QAAS,EACT,WAAY,EAEhB,EAGMC,GAAc,CAACzY,EAAkBkB,IAAkC,CACvE,OAAQA,EAAO,MACb,IAAK,YACH,MAAO,CACL,GAAGsX,GACH,MAAOtX,EAAO,QAAQ,MACtB,MAAOA,EAAO,QAAQ,MACtB,aAAcA,EAAO,QAAQ,MAC7B,SAAUA,EAAO,QAAQ,SACzB,UAAWA,EAAO,QAAQ,SAC1B,YAAaA,EAAO,QAAQ,aAAe,IAC3C,OAAQ,UACR,WAAY,WAGhB,IAAK,cACH,MAAO,CACL,GAAGlB,EACH,aAAckB,EAAO,SAGzB,IAAK,kBACH,MAAO,CACL,GAAGlB,EACH,aAAc,MAGlB,IAAK,YACH,MAAO,CACL,GAAGA,EACH,MAAOA,EAAM,MAAQ,EACrB,UAAWA,EAAM,UAAY,EAC7B,aAAc,MAGlB,IAAK,eACH,MAAO,CACL,GAAGA,EACH,MAAOkB,EAAO,QAAQ,MACtB,MAAOlB,EAAM,MAAQkB,EAAO,QAAQ,MACpC,MAAOA,EAAO,QAAQ,OAG1B,IAAK,kBACH,MAAO,CACL,GAAGlB,EACH,WAAYkB,EAAO,QACnB,OAAQA,EAAO,UAAY,UAAY,UAAYA,EAAO,UAAY,MAAQ,MAAQA,EAAO,UAAY,OAAS,OAAS,UAG/H,IAAK,aACH,MAAO,CACL,GAAGlB,EACH,OAAQkB,EAAO,QACf,WAAYA,EAAO,UAAY,UAAY,UAAYA,EAAO,UAAY,MAAQ,MAAQA,EAAO,UAAY,OAAS,OAAS,WAGnI,IAAK,cACH,MAAMwX,EAAcxX,EAAO,QAAQ,KACnC,OAAIlB,EAAM,SAAS0Y,CAAW,EAAI,EACzB,CACL,GAAG1Y,EACH,SAAU,CACR,GAAGA,EAAM,SACT,CAAC0Y,CAAW,EAAG1Y,EAAM,SAAS0Y,CAAW,EAAI,EAC/C,EAGG1Y,EAET,IAAK,cACH,MAAO,CACL,GAAGA,EACH,SAAUkB,EAAO,SAGrB,IAAK,aACH,OAAOsX,GAET,QACE,OAAOxY,CAAA,CAEb,EAGM2Y,GAAU,CAEd,SAAU,MAAOC,GAAwC,CACvD,GAAI,CACF,MAAMlJ,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,eAAeiJ,CAAO,GAAI,CACrD,QAAS,CACP,cAAiB,UAAUlJ,CAAK,GAChC,eAAgB,mBAClB,CACD,EAED,GAAI,CAACC,EAAS,GACZ,MAAM,IAAI,MAAM,0BAA0B,EAG5C,OAAOA,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,8CAA8C,EACpD,CACL,GAAG2I,GACH,GAAIM,EACJ,YAAa,MAAMA,CAAO,YAC1B,SAAU,CAAC,SAAU,OAAQ,SAAS,EACtC,UAAW,CAAE,KAAMN,GAAU,UAAW,KAAMA,GAAU,UAAU,CAEtE,CACF,EAGA,UAAW,MAAOM,GAAsE,CACtF,GAAI,CACF,MAAMlJ,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,kBAAmB,CAC9C,OAAQ,OACR,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,QAAAkJ,EAAS,EACjC,EAED,GAAI,CAACjJ,EAAS,GACZ,MAAM,IAAI,MAAM,sBAAsB,EAGxC,OAAOA,EAAS,MAClB,MAAgB,CAEd,eAAQ,KAAK,6CAA6C,EACnD,CACL,MAAO4I,GAAkB,CAAC,EAC1B,SAAU,GAEd,CACF,EAGA,SAAU,MAAOM,EAAgB9B,EAAoCC,IAAqC,CACxG,MAAMtH,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,iBAAkB,CAC7C,OAAQ,OACR,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,OAAAmJ,EAAQ,KAAA9B,EAAM,GAAAC,EAAI,EAC1C,EAED,GAAI,CAACrH,EAAS,GACZ,MAAM,IAAI,MAAM,cAAc,EAGhC,OAAOA,EAAS,MAClB,EAGA,WAAY,MAAOkJ,EAAgBC,EAAcC,IAA4C,CAC3F,MAAMrJ,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,oBAAqB,CAChD,OAAQ,OACR,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,OAAAmJ,EAAQ,KAAAC,EAAM,SAAAC,EAAU,EAChD,EAED,GAAI,CAACpJ,EAAS,GACZ,MAAM,IAAI,MAAM,wBAAwB,EAG1C,OAAOA,EAAS,MAClB,EAGA,YAAa,MAAOkJ,EAAgBG,EAAeC,IAAkB,CACnE,MAAMvJ,EAAQ,aAAa,QAAQ,OAAO,EACpCC,EAAW,MAAM,MAAM,mBAAoB,CAC/C,OAAQ,OACR,QAAS,CACP,cAAiB,UAAUD,CAAK,GAChC,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,OAAAmJ,EAAQ,MAAAG,EAAO,MAAAC,EAAO,EAC9C,EAED,GAAI,CAACtJ,EAAS,GACZ,MAAM,IAAI,MAAM,wBAAwB,EAG1C,OAAOA,EAAS,MAClB,CACF,EAGMuJ,GAAcpJ,gBAA2C,MAAS,EAG3DqJ,GAAwD,CAAC,CAAE,SAAAtP,KAAe,CACrF,KAAM,CAACuP,EAAWC,CAAQ,EAAIC,aAAWb,GAAaD,EAAgB,EAChE,CAACe,EAAeC,CAAgB,EAAItJ,WAAwB,IAAI,EAIhE,CAAE,KAAMuJ,EAAc,UAAAtJ,CAAA,EAAchF,GACxC,CAAC,QAASiO,EAAU,KAAK,EACzB,IAAMT,GAAQ,SAASS,EAAU,KAAK,EACtC,CACE,QAASA,EAAU,MAAQ,EAC3B,MAAO,GACT,EAIIM,EAAoBnP,GAAYoO,GAAQ,UAAW,CACvD,UAAW,CAAC5Y,EAAM6Y,IAAY,CAC5BS,EAAS,CACP,KAAM,YACN,QAAS,CACP,MAAOT,EACP,MAAO7Y,EAAK,MACZ,SAAUA,EAAK,SACjB,CACD,EACDyZ,EAAiB,cAAc,EAC/BnJ,EAAM,QAAQ,OAAO,CACvB,EACA,QAAUjU,GAAiB,CACzBiU,EAAM,MAAMjU,EAAM,SAAW,QAAQ,CACvC,EACD,EAyHKzD,EAAyB,CAC7B,UAAAygB,EACA,aAAcK,GAAgB,KAC9B,QAAStJ,EACT,SAAAkJ,EACA,UAjGgB,MAAOT,GAAoB,CAC3C,MAAMc,EAAkB,YAAYd,CAAO,CAC7C,EAgGE,SA9Fe,MAAO7B,EAAoCC,EAAkC2C,IAA4C,CACxI,GAAI,CAACP,EAAU,MAAO,OAItB,MAAMne,EADS,IAAIib,GAAWkD,EAAU,MAAO,CAAC,EAC1B,SAASrC,EAAMC,CAAE,EAEnC/b,EAAO,SAEL0e,GAAgB1e,EAAO,SAAWA,EAAO,QAAQ,OAAS,GAC5D0e,EAAa1e,EAAO,OAAO,EAI7Boe,EAAS,CACP,KAAM,eACN,QAAS,CACP,MAAOpe,EAAO,SACd,MAAOA,EAAO,MACd,MAAOA,EAAO,QAAU,EAAI,EAC9B,CACD,EAEDoe,EAAS,CAAE,KAAM,YAAa,QAAS,CAAE,KAAAtC,EAAM,GAAAC,EAAI,MAAO/b,EAAO,MAAM,CAAG,EAGtEme,EAAU,WAAa,IACrBA,EAAU,QAAUA,EAAU,aAAe,IAC/CC,EAAS,CAAE,KAAM,kBAAmB,QAAS,MAAO,EACpDhJ,EAAM,QAAQ,OAAO,IAErBgJ,EAAS,CAAE,KAAM,kBAAmB,QAAS,OAAQ,EACrDhJ,EAAM,MAAM,MAAM,IAIlBpV,EAAO,SACToV,EAAM,QAAQ,KAAK,GAGrBA,EAAM,MAAM,MAAM,CAEtB,EAqDE,WAnDiB,MAAOyI,EAAmCC,IAA4C,CACvG,GAAKQ,EAEL,GAAI,CACF,MAAMte,EAAS,MAAM0d,GAAQ,WAAWY,EAAeT,EAAMC,CAAQ,EACrEM,EAAS,CAAE,KAAM,cAAe,QAAS,CAAE,KAAAP,EAAM,SAAAC,CAAA,EAAY,EAC7DM,EAAS,CACP,KAAM,eACN,QAAS,CACP,MAAOpe,EAAO,MACd,MAAOA,EAAO,aAAe,EAC7B,MAAO,EACT,CACD,EACDoV,EAAM,QAAQ,SAAS,CACzB,MAAgB,CACdA,EAAM,MAAM,QAAQ,CACtB,CACF,EAkCE,UAhCgB,IAAM,CACtBgJ,EAAS,CAAE,KAAM,kBAAmB,QAAS,SAAU,CACzD,EA+BE,WA7BiB,IAAM,CACvBA,EAAS,CAAE,KAAM,kBAAmB,QAAS,UAAW,CAC1D,EA4BE,UA1BgB,IAAM,CACtBA,EAAS,CAAE,KAAM,aAAc,EAC/BG,EAAiB,IAAI,CACvB,EAwBE,YAtBkB,SAAY,CAC9B,GAAKD,EAEL,GAAI,CACF,MAAMZ,GAAQ,YAAYY,EAAeH,EAAU,MAAOA,EAAU,KAAK,EACzE/I,EAAM,QAAQ,SAAS,CACzB,MAAgB,CACdA,EAAM,MAAM,QAAQ,CACtB,CACF,CAaE,EAGF,OAAOQ,MAACqI,GAAY,SAAZ,CAAqB,MAAAvgB,EAAe,SAAAkR,CAAA,CAAS,CACvD,EAGa+P,GAAU,IAAuB,CAC5C,MAAM7Y,EAAUgQ,aAAWmI,EAAW,EACtC,GAAInY,IAAY,OACd,MAAM,IAAI,MAAM,4CAA4C,EAE9D,OAAOA,CACT,ECveO,MAAM8Y,EAAa,CAWxB,aAAc,CAVNrF,EAAA,oBAAoC,MACpCA,EAAA,kBAAsC,MACtCA,EAAA,wBAAuD,KACvDA,EAAA,gBAA0B,CAChC,aAAc,GACd,YAAa,GACb,UAAW,GACX,MAAO,KAIP,KAAK,kBACL,KAAK,cACP,CAGQ,iBAAwB,CAC9B,GAAI,CAEF,KAAK,aAAe,IAAK,OAAO,cAAiB,OAAe,oBAGhE,KAAK,WAAa,IAAI,MACtB,KAAK,WAAW,KAAO,GACvB,KAAK,WAAW,QAAU,OAG1B,KAAK,qBAEP,OAASpY,EAAO,CACd,QAAQ,KAAK,+BAAgCA,CAAK,CACpD,CACF,CAGQ,qBAA4B,CAClC,MAAM0d,EAA+C,CACnD,WAAY,KAAK,aAAa,IAAK,EAAG,EACtC,SAAU,KAAK,aAAa,IAAK,EAAG,EACpC,UAAW,KAAK,cAAc,CAAC,IAAK,IAAK,GAAG,EAAG,EAAG,EAClD,YAAa,KAAK,iBAAiB,CAAC,IAAK,IAAK,IAAK,IAAI,EAAG,EAAG,EAC7D,eAAgB,KAAK,eAAe,CAAC,IAAK,IAAK,IAAK,KAAM,IAAI,EAAG,EAAG,EACpE,aAAc,KAAK,aAAa,IAAK,EAAG,EACxC,aAAc,KAAK,aAAa,IAAK,EAAG,EACxC,SAAU,KAAK,cAAc,IAAK,IAAK,EAAG,GAG5C,OAAO,QAAQA,CAAe,EAAE,QAAQ,CAAC,CAACC,EAAQC,CAAG,IAAM,CACzD,MAAMC,EAAQ,IAAI,MAAMD,CAAG,EAC3BC,EAAM,QAAU,OAChB,KAAK,aAAa,IAAIF,EAAuBE,CAAK,CACpD,CAAC,CACH,CAGQ,aAAaC,EAAmBC,EAA0B,CAChE,GAAI,CAAC,KAAK,aAAc,MAAO,GAE/B,MAAMC,EAAa,KAAK,aAAa,WAC/BC,EAAaD,EAAaD,EAC1BG,EAAS,KAAK,aAAa,aAAa,EAAGD,EAAYD,CAAU,EACjEG,EAAcD,EAAO,eAAe,CAAC,EAE3C,QAAS3e,EAAI,EAAGA,EAAI0e,EAAY1e,IAAK,CACnC,MAAMlE,EAAIkE,EAAIye,EACdG,EAAY5e,CAAC,EAAI,KAAK,IAAI,EAAI,KAAK,GAAKue,EAAYziB,CAAC,EAAI,KAAK,IAAI,CAACA,EAAI,CAAC,CAC1E,CAEA,OAAO,KAAK,gBAAgB6iB,CAAM,CACpC,CAGQ,cAAcE,EAAuBL,EAA0B,CACrE,GAAI,CAAC,KAAK,aAAc,MAAO,GAE/B,MAAMC,EAAa,KAAK,aAAa,WAC/BC,EAAaD,EAAaD,EAC1BG,EAAS,KAAK,aAAa,aAAa,EAAGD,EAAYD,CAAU,EACjEG,EAAcD,EAAO,eAAe,CAAC,EAE3C,QAAS3e,EAAI,EAAGA,EAAI0e,EAAY1e,IAAK,CACnC,MAAMlE,EAAIkE,EAAIye,EACd,IAAIK,EAAS,EACbD,EAAY,QAAQE,GAAQ,CAC1BD,GAAU,KAAK,IAAI,EAAI,KAAK,GAAKC,EAAOjjB,CAAC,EAAI+iB,EAAY,MAC3D,CAAC,EACDD,EAAY5e,CAAC,EAAI8e,EAAS,KAAK,IAAI,CAAChjB,EAAI,CAAC,CAC3C,CAEA,OAAO,KAAK,gBAAgB6iB,CAAM,CACpC,CAGQ,iBAAiBE,EAAuBL,EAA0B,CACxE,GAAI,CAAC,KAAK,aAAc,MAAO,GAE/B,MAAMC,EAAa,KAAK,aAAa,WAC/BC,EAAaD,EAAaD,EAC1BG,EAAS,KAAK,aAAa,aAAa,EAAGD,EAAYD,CAAU,EACjEG,EAAcD,EAAO,eAAe,CAAC,EACrCK,EAAaR,EAAWK,EAAY,OAE1C,QAAS7e,EAAI,EAAGA,EAAI0e,EAAY1e,IAAK,CACnC,MAAMlE,EAAIkE,EAAIye,EACRQ,EAAY,KAAK,MAAMnjB,EAAIkjB,CAAU,EACrCE,EAAWpjB,EAAImjB,EAAYD,EAEjC,GAAIC,EAAYJ,EAAY,OAAQ,CAClC,MAAME,EAAOF,EAAYI,CAAS,EAClCL,EAAY5e,CAAC,EAAI,KAAK,IAAI,EAAI,KAAK,GAAK+e,EAAOG,CAAQ,EAAI,KAAK,IAAI,CAACA,EAAW,CAAC,CACnF,CACF,CAEA,OAAO,KAAK,gBAAgBP,CAAM,CACpC,CAGQ,eAAeE,EAAuBL,EAA0B,CACtE,GAAI,CAAC,KAAK,aAAc,MAAO,GAE/B,MAAMC,EAAa,KAAK,aAAa,WAC/BC,EAAaD,EAAaD,EAC1BG,EAAS,KAAK,aAAa,aAAa,EAAGD,EAAYD,CAAU,EACjEG,EAAcD,EAAO,eAAe,CAAC,EACrCK,EAAaR,EAAWK,EAAY,OAE1C,QAAS7e,EAAI,EAAGA,EAAI0e,EAAY1e,IAAK,CACnC,MAAMlE,EAAIkE,EAAIye,EACRQ,EAAY,KAAK,MAAMnjB,EAAIkjB,CAAU,EACrCE,EAAWpjB,EAAImjB,EAAYD,EAEjC,GAAIC,EAAYJ,EAAY,OAAQ,CAClC,MAAME,EAAOF,EAAYI,CAAS,EAC5BE,EAAW,KAAK,IAAI,CAACD,EAAW,CAAC,EACvCN,EAAY5e,CAAC,EAAI,KAAK,IAAI,EAAI,KAAK,GAAK+e,EAAOG,CAAQ,EAAIC,CAC7D,CACF,CAEA,OAAO,KAAK,gBAAgBR,CAAM,CACpC,CAGQ,cAAcS,EAAmBC,EAAiBb,EAA0B,CAClF,GAAI,CAAC,KAAK,aAAc,MAAO,GAE/B,MAAMC,EAAa,KAAK,aAAa,WAC/BC,EAAaD,EAAaD,EAC1BG,EAAS,KAAK,aAAa,aAAa,EAAGD,EAAYD,CAAU,EACjEG,EAAcD,EAAO,eAAe,CAAC,EAE3C,QAAS3e,EAAI,EAAGA,EAAI0e,EAAY1e,IAAK,CACnC,MAAMlE,EAAIkE,EAAIye,EACRa,EAAWxjB,EAAI0iB,EACfO,EAAOK,GAAaC,EAAUD,GAAaE,EACjDV,EAAY5e,CAAC,EAAI,KAAK,IAAI,EAAI,KAAK,GAAK+e,EAAOjjB,CAAC,GAAK,EAAIwjB,EAC3D,CAEA,OAAO,KAAK,gBAAgBX,CAAM,CACpC,CAGQ,gBAAgBA,EAA6B,CACnD,MAAMY,EAASZ,EAAO,OAChBC,EAAcD,EAAO,eAAe,CAAC,EACrCa,EAAU,IAAI,WAAWD,CAAM,EAErC,QAASvf,EAAI,EAAGA,EAAIuf,EAAQvf,IAC1Bwf,EAAQxf,CAAC,EAAI4e,EAAY5e,CAAC,EAAI,MAIhC,MAAMyf,EAAY,IAAI,YAAY,EAAE,EAC9BC,EAAO,IAAI,SAASD,CAAS,EAG7BE,EAAc,CAACC,EAAgBC,IAAmB,CACtD,QAAS7f,EAAI,EAAGA,EAAI6f,EAAO,OAAQ7f,IACjC0f,EAAK,SAASE,EAAS5f,EAAG6f,EAAO,WAAW7f,CAAC,CAAC,CAElD,EAEA2f,EAAY,EAAG,MAAM,EACrBD,EAAK,UAAU,EAAG,GAAKF,EAAQ,OAAS,EAAG,EAAI,EAC/CG,EAAY,EAAG,MAAM,EACrBA,EAAY,GAAI,MAAM,EACtBD,EAAK,UAAU,GAAI,GAAI,EAAI,EAC3BA,EAAK,UAAU,GAAI,EAAG,EAAI,EAC1BA,EAAK,UAAU,GAAI,EAAG,EAAI,EAC1BA,EAAK,UAAU,GAAIf,EAAO,WAAY,EAAI,EAC1Ce,EAAK,UAAU,GAAIf,EAAO,WAAa,EAAG,EAAI,EAC9Ce,EAAK,UAAU,GAAI,EAAG,EAAI,EAC1BA,EAAK,UAAU,GAAI,GAAI,EAAI,EAC3BC,EAAY,GAAI,MAAM,EACtBD,EAAK,UAAU,GAAIF,EAAQ,OAAS,EAAG,EAAI,EAG3C,MAAMM,EAAU,IAAI,WAAW,GAAKN,EAAQ,OAAS,CAAC,EACtDM,EAAQ,IAAI,IAAI,WAAWL,CAAS,EAAG,CAAC,EACxCK,EAAQ,IAAI,IAAI,WAAWN,EAAQ,MAAM,EAAG,EAAE,EAE9C,MAAMO,EAAO,IAAI,KAAK,CAACD,CAAO,EAAG,CAAE,KAAM,YAAa,EACtD,OAAO,IAAI,gBAAgBC,CAAI,CACjC,CAGO,gBAAgB3B,EAA2B,CAChD,GAAI,KAAK,SAAS,MAAO,OAEzB,MAAME,EAAQ,KAAK,aAAa,IAAIF,CAAM,EACtCE,IACFA,EAAM,OAAS,KAAK,SAAS,UAAY,KAAK,SAAS,aACvDA,EAAM,YAAc,EACpBA,EAAM,OAAO,MAAM7d,GAAS,CAC1B,QAAQ,KAAK,+BAA+B2d,CAAM,IAAK3d,CAAK,CAC9D,CAAC,EAEL,CAGO,oBAAoB4d,EAAoB,CACzC,KAAK,SAAS,OAAS,CAAC,KAAK,aAE7BA,GAAO,KAAK,WAAW,MAAQA,IACjC,KAAK,WAAW,IAAMA,GAGxB,KAAK,WAAW,OAAS,KAAK,SAAS,YAAc,KAAK,SAAS,aACnE,KAAK,WAAW,OAAO,MAAM5d,GAAS,CACpC,QAAQ,KAAK,mCAAoCA,CAAK,CACxD,CAAC,EACH,CAGO,qBAA4B,CAC7B,KAAK,aACP,KAAK,WAAW,QAChB,KAAK,WAAW,YAAc,EAElC,CAGO,UAAU0c,EAA2B6C,EAAsB,CAC/D,KAAK,SAAiB7C,CAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,EAAG6C,CAAM,CAAC,EAC9D,KAAK,eAGD,KAAK,YAAc7C,IAAS,gBAC9B,KAAK,WAAW,OAAS,KAAK,SAAS,YAAc,KAAK,SAAS,aAEvE,CAGO,YAAmB,CACxB,KAAK,SAAS,MAAQ,CAAC,KAAK,SAAS,MACrC,KAAK,eAED,KAAK,SAAS,OAChB,KAAK,qBAET,CAGO,aAA6B,CAClC,MAAO,CAAE,GAAG,KAAK,SACnB,CAGQ,cAAqB,CAC3B,aAAa,QAAQ,gBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,CACrE,CAGQ,cAAqB,CAC3B,MAAM8C,EAAQ,aAAa,QAAQ,eAAe,EAClD,GAAIA,EACF,GAAI,CACF,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAG,KAAK,MAAMA,CAAK,EACzD,OAASxf,EAAO,CACd,QAAQ,KAAK,iCAAkCA,CAAK,CACtD,CAEJ,CAGO,SAAgB,CACrB,KAAK,sBAGL,KAAK,aAAa,QAAQ6d,GAAS,CAC7BA,EAAM,IAAI,WAAW,OAAO,GAC9B,IAAI,gBAAgBA,EAAM,GAAG,CAEjC,CAAC,EACD,KAAK,aAAa,QAGd,KAAK,cAAgB,KAAK,aAAa,QAAU,UACnD,KAAK,aAAa,OAEtB,CACF,CCpTA,MAAM4B,GAAe/L,gBAAuC,IAAI,EAEnDgM,GAAW,IAAwB,CAC9C,MAAM/a,EAAUgQ,aAAW8K,EAAY,EACvC,GAAI,CAAC9a,EACH,MAAM,IAAI,MAAM,+CAA+C,EAEjE,OAAOA,CACT,EAMagb,GAA8C,CAAC,CAAE,SAAAlS,KAAe,CAC3E,MAAMmS,EAAkBC,SAA4B,IAAI,EAExDrL,YAAU,KAERoL,EAAgB,QAAU,IAAInC,GAGvB,IAAM,CACPmC,EAAgB,SAClBA,EAAgB,QAAQ,SAE5B,GACC,EAAE,EAEL,MAAME,EAAanC,GAAwB,CACrCiC,EAAgB,SAClBA,EAAgB,QAAQ,gBAAgBjC,CAAM,CAElD,EAEMoC,EAAuBnC,GAAiB,CACxCgC,EAAgB,SAClBA,EAAgB,QAAQ,oBAAoBhC,CAAG,CAEnD,EAEMoC,EAAsB,IAAM,CAC5BJ,EAAgB,SAClBA,EAAgB,QAAQ,qBAE5B,EAEMrjB,EAA0B,CAC9B,aAAcqjB,EAAgB,QAC9B,UAAAE,EACA,oBAAAC,EACA,oBAAAC,CAAA,EAGF,OAAOvL,MAACgL,GAAa,SAAb,CAAsB,MAAAljB,EAAe,SAAAkR,CAAA,CAAS,CACxD,EAGawS,GAAe,IAAM,CAChC,KAAM,CAAE,UAAAH,CAAA,EAAcJ,GAAA,EAEtB,MAAO,CACL,YAAa,IAAMI,EAAU,YAAY,EACzC,UAAW,IAAMA,EAAU,UAAU,EACrC,WAAY,IAAMA,EAAU,WAAW,EACvC,aAAc,IAAMA,EAAU,aAAa,EAC3C,gBAAiB,IAAMA,EAAU,gBAAgB,EACjD,cAAe,IAAMA,EAAU,cAAc,EAC7C,cAAe,IAAMA,EAAU,cAAc,EAC7C,UAAW,IAAMA,EAAU,UAAU,EAEzC,ECpDMI,GAAerL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtBsL,GAActL,EAAO;AAAA;AAAA;AAAA;AAAA,EAMrBuL,GAAgBvL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWvBwL,GAAiBxL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBxByL,EAA0D,CAAC,CAAE,SAAA7S,KAAe,CAChF,KAAM,CAAE,KAAAmG,EAAM,QAAA0C,CAAA,EAAY5B,GAAA,EAE1B,OAAI4B,EAEA7B,MAAC2L,GAAA,CACC,SAAA3L,MAAC,OAAI,kBAAM,EACb,EAICb,oBAIK,SAAAnG,EAAS,QAHT8S,GAAA,CAAS,GAAG,SAAS,QAAO,GAAC,CAIzC,EAGMC,GAAuD,CAAC,CAAE,SAAA/S,KAAe,CAC7E,KAAM,CAAE,KAAAmG,EAAM,QAAA0C,CAAA,EAAY5B,GAAA,EAE1B,OAAI4B,EAEA7B,MAAC2L,GAAA,CACC,SAAA3L,MAAC,OAAI,kBAAM,EACb,EAIAb,QACM2M,GAAA,CAAS,GAAG,IAAI,QAAO,GAAC,oBAGxB,SAAA9S,EAAS,CACrB,EAGMgT,GAAuB,IAAM,CACjC,KAAM,CAACC,EAAsBC,CAAuB,EAAI7M,WAAS,EAAK,EAEtE,cACGoM,GAAA,CACC,UAAAzL,MAAC0E,GAAA,EAAO,QACPgH,GAAA,CACC,SAAA1K,OAACmL,GAAA,CAEC,UAAAnM,MAACoM,EAAA,CACC,KAAK,SACL,QACEpM,MAAC+L,GAAA,CACC,eAACvK,KAAU,EACb,IAGJxB,MAACoM,EAAA,CACC,KAAK,YACL,QACEpM,MAAC+L,GAAA,CACC,eAAC5J,KAAa,EAChB,IAKJnC,MAACoM,EAAA,CACC,KAAK,IACL,QACEpM,MAAC6L,EAAA,CACC,eAAC9K,KAAS,EACZ,IAGJf,MAACoM,EAAA,CACC,KAAK,UACL,QACEpM,MAAC6L,EAAA,CACC,eAACQ,WAAA,CAAS,gBAAWT,GAAA,CAAe,UAAA5L,MAAC,OAAI,UAAU,UAAU,EAAMA,MAAC,OAAI,mBAAO,GAAM,EACnF,SAAAA,MAAC2C,GAAA,EAAe,EAClB,EACF,IAGJ3C,MAACoM,EAAA,CACC,KAAK,QACL,QACEpM,MAAC6L,EAAA,CACC,eAACQ,WAAA,CAAS,gBAAWT,GAAA,CAAe,UAAA5L,MAAC,OAAI,UAAU,UAAU,EAAMA,MAAC,OAAI,mBAAO,GAAM,EACnF,SAAAA,MAACwC,GAAA,EAAa,EAChB,EACF,IAGJxC,MAACoM,EAAA,CACC,KAAK,WACL,QACEpM,MAAC6L,EAAA,CACC,eAACQ,WAAA,CAAS,gBAAWT,GAAA,CAAe,UAAA5L,MAAC,OAAI,UAAU,UAAU,EAAMA,MAAC,OAAI,qBAAS,GAAM,EACrF,SAAAA,MAAC8C,GAAA,EAAgB,EACnB,EACF,IAGJ9C,MAACoM,EAAA,CACC,KAAK,eACL,QACEpM,MAAC6L,EAAA,CACC,eAACQ,WAAA,CAAS,gBAAWT,GAAA,CAAe,UAAA5L,MAAC,OAAI,UAAU,UAAU,EAAMA,MAAC,OAAI,oBAAQ,GAAM,EACpF,SAAAA,MAAC4C,GAAA,EAAoB,EACvB,EACF,IAGJ5C,MAACoM,EAAA,CACC,KAAK,gBACL,QACEpM,MAAC6L,EAAA,CACC,eAACQ,WAAA,CAAS,gBAAWT,GAAA,CAAe,UAAA5L,MAAC,OAAI,UAAU,UAAU,EAAMA,MAAC,OAAI,mBAAO,GAAM,EACnF,SAAAA,MAAC6C,GAAA,EAAqB,EACxB,EACF,IAKJ7C,MAACoM,EAAA,CAAM,KAAK,IAAI,QAASpM,MAAC8L,GAAA,CAAS,GAAG,IAAI,QAAO,GAAC,EAAI,GACxD,EACF,QACC1G,GAAA,EAAO,EAGP,IAMH,CAEJ,EAEMkH,GAAgB,KAEpBvM,YAAU,KAERuD,GAAmB,gBAGnBA,GAAmB,2BAGZ,IAAM,CACXA,GAAmB,0BACrB,GACC,EAAE,EAGHtD,MAACd,GAAA,CACC,eAACgM,GAAA,CACC,SAAAlL,MAACsI,IACC,SAAAtI,MAACgM,GAAA,EAAW,EACd,EACF,EACF,GCjPSO,EAAQ,CACnB,OAAQ,CAEN,QAAS,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,WAIP,UAAW,CACT,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,WAIP,KAAM,CACJ,IAAK,UACL,KAAM,UACN,MAAO,UACP,OAAQ,UACR,OAAQ,UACR,OAAQ,WAIV,KAAM,CACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,WAIP,QAAS,UACT,QAAS,UACT,MAAO,UACP,KAAM,UAGN,WAAY,CACV,QAAS,iEACT,UAAW,oDACX,KAAM,2BACN,UAAW,4BACX,QAAS,qBACT,MAAO,6BAIT,KAAM,CACJ,QAAS,UACT,UAAW,UACX,MAAO,UACP,QAAS,UACX,EAIF,MAAO,CACL,QAAS,+EACT,KAAM,4CAIR,UAAW,CACT,GAAI,UACJ,GAAI,WACJ,KAAM,OACN,GAAI,WACJ,GAAI,UACJ,MAAO,SACP,MAAO,WACP,MAAO,UACP,MAAO,OACP,MAAO,WAIT,YAAa,CACX,MAAO,IACP,OAAQ,IACR,OAAQ,IACR,SAAU,IACV,KAAM,KAIR,QAAS,CACP,EAAG,IACH,EAAG,UACH,EAAG,SACH,EAAG,UACH,EAAG,OACH,EAAG,UACH,EAAG,SACH,EAAG,OACH,GAAI,SACJ,GAAI,OACJ,GAAI,OACJ,GAAI,OACJ,GAAI,OACJ,GAAI,OACJ,GAAI,QACJ,GAAI,QACJ,GAAI,QACJ,GAAI,SAIN,aAAc,CACZ,KAAM,IACN,GAAI,WACJ,KAAM,UACN,GAAI,WACJ,GAAI,SACJ,GAAI,UACJ,MAAO,OACP,MAAO,SACP,KAAM,UAIR,QAAS,CACP,GAAI,kCACJ,KAAM,kEACN,GAAI,wEACJ,GAAI,0EACJ,GAAI,4EACJ,MAAO,wCACP,KAAM,mCACN,SAAU,mCACV,WAAY,mCACZ,QAAS,mCACT,MAAO,wCACP,KAAM,gCAIR,YAAa,CACX,GAAI,QACJ,GAAI,QACJ,GAAI,SACJ,GAAI,SACJ,MAAO,UAIT,YAAa,CACX,KAAM,QACN,KAAM,QACN,KAAM,SAIR,OAAQ,CACN,SAAU,IACV,OAAQ,KACR,MAAO,KACP,MAAO,KACP,QAAS,KACT,QAAS,KAEb,EAKaC,GAAQ,CACnB,GAAI,sBAAsBD,EAAM,YAAY,EAAE,IAC9C,GAAI,sBAAsBA,EAAM,YAAY,EAAE,IAC9C,GAAI,sBAAsBA,EAAM,YAAY,EAAE,IAC9C,GAAI,sBAAsBA,EAAM,YAAY,EAAE,IAC9C,MAAO,sBAAsBA,EAAM,YAAY,KAAK,CAAC,IAGrD,MAAO,4BACP,MAAO,4BACP,MAAO,4BAET,EAGaE,GAAS,CASpB,cAAe;AAAA,kBACCF,EAAM,OAAO,WAAW,IAAI;AAAA;AAAA;AAAA,IAM5C,WAAY;AAAA;AAAA;AAAA;AAAA;AAAA,qBAKOA,EAAM,aAAa,EAAE;AAAA,mBACvBA,EAAM,YAAY,QAAQ;AAAA;AAAA,sBAEvBA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAwC5C,EC1QaG,GAAeC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,mBAeT,CAAC,CAAE,MAAAJ,CAAA,IAAYA,EAAM,MAAM,OAAO;AAAA,kBACnC,CAAC,CAAE,MAAAA,CAAA,IAAYA,EAAM,OAAO,WAAW,OAAO;AAAA,aACnD,CAAC,CAAE,MAAAA,CAAA,IAAYA,EAAM,OAAO,KAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA4BnC,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,KAAK,GAAG,CAAC;AAAA,qBAClC,CAAC,CAAE,MAAAA,CAAA,IAAYA,EAAM,aAAa,IAAI;AAAA;AAAA;AAAA;AAAA,kBAIzC,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,KAAK,GAAG,CAAC;AAAA,qBAClC,CAAC,CAAE,MAAAA,CAAA,IAAYA,EAAM,aAAa,IAAI;AAAA;AAAA;AAAA,oBAGvC,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAa9B,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,QAAQ,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAiBxC,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,QAAQ,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAOtD,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,QAAQ,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,eAItC,CAAC,CAAE,MAAAA,KAAYA,EAAM,OAAO,QAAQ,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECrFjD1T,GAAc,IAAI/E,GAAY,CAClC,eAAgB,CACd,QAAS,CACP,MAAO,EACP,UAAW,EAAI,GAAK,IACpB,UAAW,GAAK,GAAK,IACvB,CAEJ,CAAC,EAEDwE,GAAS,WAAW,SAAS,eAAe,MAAM,CAAE,EAAE,OACpD0H,MAACxH,EAAM,WAAN,CACC,SAAAwH,MAAC4M,GAAA,CACC,SAAA5M,MAAClH,GAAA,CAAoB,OAAQD,GAC3B,SAAAmI,OAAC6L,GAAA,CAAc,MAAAN,EACb,UAAAvM,MAAC0M,GAAA,EAAa,QACbJ,GAAA,EAAI,EACLtM,MAAC8M,GAAA,CACC,SAAS,aACT,aAAc,CACZ,SAAU,IACV,MAAO,CACL,WAAY,UACZ,MAAO,UACP,OAAQ,oBACR,aAAc,OACd,SAAU,QAEZ,QAAS,CACP,UAAW,CACT,QAAS,UACT,UAAW,UACb,EAEF,MAAO,CACL,UAAW,CACT,QAAS,UACT,UAAW,UACb,CACF,CACF,EACF,EACF,EACF,EACF,EACF,CACF", "names": ["f", "require$$0", "k", "l", "m", "n", "p", "q", "c", "a", "g", "b", "d", "e", "h", "reactJsxRuntime_production_min", "jsxRuntimeModule", "client", "_setPrototypeOf", "t", "_inherits<PERSON><PERSON>e", "o", "setPrototypeOf", "Subscribable", "_proto", "listener", "_this", "callback", "x", "_extends", "r", "isServer", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeUntilStale", "updatedAt", "staleTime", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "parseMutationArgs", "parseFilter<PERSON><PERSON>s", "mapQueryStatusFilter", "active", "inactive", "isActive", "matchQuery", "filters", "query", "exact", "fetching", "predicate", "query<PERSON><PERSON>", "stale", "hashQueryKeyByOptions", "partialMatchKey", "queryS<PERSON>us<PERSON><PERSON>er", "matchMutation", "mutation", "<PERSON><PERSON><PERSON>", "hashQuery<PERSON>ey", "options", "hashFn", "asArray", "stableValueHash", "_", "val", "isPlainObject", "result", "key", "partialDeepEqual", "replaceEqualDeep", "array", "aSize", "bItems", "bSize", "copy", "equalItems", "i", "shallowEqualObjects", "hasObjectPrototype", "ctor", "prot", "sleep", "timeout", "resolve", "scheduleMicrotask", "error", "getAbortController", "FocusManager", "_Subscribable", "onFocus", "_window", "_this$cleanup", "setup", "_this$cleanup2", "_this2", "focused", "focusManager", "OnlineManager", "onOnline", "online", "onlineManager", "defaultRetryDelay", "failureCount", "isCancelable", "CancelledError", "isCancelledError", "<PERSON><PERSON><PERSON>", "config", "cancelRetry", "cancelFn", "continueFn", "promiseResolve", "promiseReject", "cancelOptions", "outerResolve", "outerReject", "reject", "pause", "continueResolve", "run", "promiseOrValue", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "NotifyManager", "_len", "args", "_key", "_this3", "queue", "fn", "notify<PERSON><PERSON>ger", "logger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Query", "_this$options$cacheTi", "_this$options$isDataE", "_this$options", "prevData", "data", "state", "setStateOptions", "_this$retryer", "promise", "observer", "_this$retryer2", "_this$retryer3", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_abortController$abor", "_this$retryer4", "abortController", "queryFnContext", "fetchFn", "context", "_this$options$behavio2", "_context$fetchOptions2", "action", "hasInitialData", "initialDataUpdatedAt", "hasData", "_action$meta", "_action$dataUpdatedAt", "Query<PERSON>ache", "_options$queryHash", "queryHash", "queryInMap", "_parseFilterArgs", "_parseFilterArgs2", "event", "_this4", "_this5", "Mutation", "getDefaultState", "restored", "_this$options$retry", "reducer", "MutationCache", "pausedMutations", "infiniteQueryBehavior", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchMore", "pageParam", "isFetchingNextPage", "isFetchingPreviousPage", "oldPages", "oldPageParams", "abortSignal", "newPageParams", "cancelled", "queryFn", "buildNewPages", "pages", "param", "page", "previous", "fetchPage", "manual", "queryFnResult", "promiseAsAny", "getNextPageParam", "_manual", "_param", "getPreviousPageParam", "shouldFetchFirstPage", "_loop", "shouldFetchNextPage", "_param2", "finalPromise", "finalPromiseAsAny", "QueryClient", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "_this$queryCache$find", "query<PERSON>eyOrFilters", "_ref", "parsedOptions", "defaultedOptions", "_ref2", "_this$queryCache$find2", "queryCache", "_parseFilterArgs3", "refetchFilters", "_parseFilterArgs4", "_parseFilterArgs4$", "promises", "_ref3", "_filters$refetchActiv", "_filters$refetchInact", "_parseFilterArgs5", "_this6", "_parseFilterArgs6", "_this7", "_this$queryDefaults$f", "_this$mutationDefault", "QueryObserver", "shouldFetchOnMount", "shouldFetchOn", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "mounted", "shouldFetchOptionally", "nextRefetchInterval", "trackedResult", "trackProp", "unsubscribe", "time", "_this$options$refetch", "nextInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "prevQueryResult", "dataUpdatedAt", "errorUpdatedAt", "isFetching", "status", "isPreviousData", "isPlaceholderData", "fetchOnMount", "fetchOptionally", "selectError", "placeholderData", "isStale", "notifyOnChangeProps", "notifyOnChangePropsExclusions", "includedProps", "<PERSON><PERSON><PERSON>", "changed", "isIncluded", "isExcluded", "defaultNotifyOptions", "_this8", "shouldLoadOnMount", "field", "MutationObserver", "_this$currentMutation", "variables", "unstable_batchedUpdates", "ReactDOM", "defaultContext", "React", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "useQueryClient", "queryClient", "QueryClientProvider", "_ref$contextSharing", "children", "Context", "createValue", "_isReset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "shouldThrowError", "suspense", "_useErrorBoundary", "params", "useMutation", "mountedRef", "_React$useState", "forceUpdate", "obsRef", "currentResult", "mutate", "mutateOptions", "useBaseQuery", "Observer", "errorResetBoundary", "_React$useState2", "useQuery", "s", "u", "j", "W", "F", "A", "Y", "U", "P", "y", "D", "Q", "H", "J", "K", "X", "Z", "ee", "O", "$", "L", "v", "S", "E", "oe", "I", "re", "se", "te", "ne", "ie", "V", "ae", "pe", "N", "de", "ce", "ue", "w", "le", "fe", "me", "Te", "M", "g.createElement", "ye", "ge", "he", "xe", "be", "B", "Se", "Ae", "z", "C", "l.memo", "l.create<PERSON>lement", "l.<PERSON>ag<PERSON>", "Re", "<PERSON>.create<PERSON>", "ve", "<PERSON><PERSON>", "Ee", "De", "Pe", "R", "Oe", "Vt", "mockUser", "authAPI", "token", "response", "credentials", "AuthContext", "createContext", "<PERSON>th<PERSON><PERSON><PERSON>", "user", "setUser", "useState", "isLoading", "loginMutation", "toast", "registerMutation", "updateProfileMutation", "login", "register", "logout", "updateProfile", "useEffect", "jsx", "useAuth", "useContext", "Container", "styled", "Title", "Subtitle", "WelcomeMessage", "UserInfo", "ButtonGroup", "<PERSON><PERSON>", "Link", "SecondaryButton", "StatsGrid", "StatCard", "HomePage", "jsxs", "LoginCard", "Form", "InputGroup", "Label", "Input", "LinkText", "ErrorMessage", "LoginPage", "email", "setEmail", "password", "setPassword", "loading", "setLoading", "setError", "handleSubmit", "err", "RegisterCard", "RegisterPage", "username", "setUsername", "confirmPassword", "setConfirmPassword", "LazyGamePage", "lazy", "__vitePreload", "LazyLevelsPage", "LazyLeaderboardPage", "LazyAchievementsPage", "LazyProfilePage", "createAnimationManager", "module", "createSpecialGemManager", "createPowerUpManager", "createAchievementManager", "preloadGameComponents", "preloadAllComponents", "ComponentPreloader", "componentName", "loader", "events", "preloadOnce", "__publicField", "MemoryManager", "size", "firstKey", "now", "maxAge", "props", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nav", "Logo", "NavLinks", "NavLink", "UserStats", "LogoutButton", "MobileMenu", "Header", "location", "useLocation", "path", "Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FooterText", "FooterLinks", "FooterLink", "Footer", "GameEngine", "board", "row", "col", "pos1", "pos2", "rowDiff", "col<PERSON>iff", "temp", "matches", "count", "currentGem", "positions", "from", "to", "tempBoard", "originalBoard", "horizontalMatches", "verticalMatches", "totalScore", "toRemove", "match", "pos", "posStr", "has<PERSON><PERSON><PERSON>", "writePos", "allMatches", "hasCascade", "newMatches", "comboMultiplier", "baseScore", "lengthBonus", "gems", "gemIndex", "newBoard", "writeIndex", "mockLevel", "generateMockBoard", "initialGameState", "gameReducer", "powerUpType", "gameAPI", "levelId", "gameId", "type", "position", "score", "moves", "GameContext", "GameProvider", "gameState", "dispatch", "useReducer", "currentGameId", "setCurrentGameId", "currentLevel", "startGameMutation", "onMatchFound", "useGame", "AudioManager", "soundEffectUrls", "effect", "url", "audio", "frequency", "duration", "sampleRate", "numSamples", "buffer", "channelData", "frequencies", "sample", "freq", "noteLength", "noteIndex", "noteTime", "envelope", "startFreq", "endFreq", "progress", "length", "samples", "wavHeader", "view", "writeString", "offset", "string", "wavFile", "blob", "volume", "saved", "AudioContext", "useAudio", "AudioProvider", "audioManagerRef", "useRef", "playSound", "playBackgroundMusic", "stopBackgroundMusic", "useGameAudio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MainContent", "LoadingScreen", "SuspenseLoader", "ProtectedRoute", "Navigate", "PublicRoute", "A<PERSON><PERSON><PERSON>nt", "showPerformancePanel", "setShowPerformancePanel", "Routes", "Route", "Suspense", "App", "theme", "media", "mixins", "GlobalStyles", "createGlobalStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "Toaster"], "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], "sources": ["../../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../../node_modules/react/jsx-runtime.js", "../../../../node_modules/react-dom/client.js", "../../../../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../../../../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../../../../node_modules/react-query/es/core/subscribable.js", "../../../../node_modules/@babel/runtime/helpers/esm/extends.js", "../../../../node_modules/react-query/es/core/utils.js", "../../../../node_modules/react-query/es/core/focusManager.js", "../../../../node_modules/react-query/es/core/onlineManager.js", "../../../../node_modules/react-query/es/core/retryer.js", "../../../../node_modules/react-query/es/core/notifyManager.js", "../../../../node_modules/react-query/es/core/logger.js", "../../../../node_modules/react-query/es/core/query.js", "../../../../node_modules/react-query/es/core/queryCache.js", "../../../../node_modules/react-query/es/core/mutation.js", "../../../../node_modules/react-query/es/core/mutationCache.js", "../../../../node_modules/react-query/es/core/infiniteQueryBehavior.js", "../../../../node_modules/react-query/es/core/queryClient.js", "../../../../node_modules/react-query/es/core/queryObserver.js", "../../../../node_modules/react-query/es/core/mutationObserver.js", "../../../../node_modules/react-query/es/react/reactBatchedUpdates.js", "../../../../node_modules/react-query/es/react/setBatchUpdatesFn.js", "../../../../node_modules/react-query/es/react/logger.js", "../../../../node_modules/react-query/es/react/setLogger.js", "../../../../node_modules/react-query/es/react/QueryClientProvider.js", "../../../../node_modules/react-query/es/react/QueryErrorResetBoundary.js", "../../../../node_modules/react-query/es/react/utils.js", "../../../../node_modules/react-query/es/react/useMutation.js", "../../../../node_modules/react-query/es/react/useBaseQuery.js", "../../../../node_modules/react-query/es/react/useQuery.js", "../../../../node_modules/goober/dist/goober.modern.js", "../../../../node_modules/react-hot-toast/dist/index.mjs", "../../src/contexts/AuthContext.tsx", "../../src/pages/HomePage.tsx", "../../src/pages/LoginPage.tsx", "../../src/pages/RegisterPage.tsx", "../../src/utils/lazyComponents.ts", "../../src/components/Debug/PerformancePanel.tsx", "../../src/components/Layout/Header.tsx", "../../src/components/Layout/Footer.tsx", "../../src/utils/gameEngine.ts", "../../src/contexts/GameContext.tsx", "../../src/utils/audioManager.ts", "../../src/contexts/AudioContext.tsx", "../../src/App.tsx", "../../src/styles/theme.ts", "../../src/styles/GlobalStyles.ts", "../../src/main.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "export var Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// TYPES\n// UTILS\nexport var isServer = typeof window === 'undefined';\nexport function noop() {\n  return undefined;\n}\nexport function functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nexport function isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nexport function ensureQueryKeyArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport function difference(array1, array2) {\n  return array1.filter(function (x) {\n    return array2.indexOf(x) === -1;\n  });\n}\nexport function replaceAt(array, index, value) {\n  var copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nexport function timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nexport function parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQuery<PERSON>ey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return _extends({}, arg3, {\n      queryKey: arg1,\n      queryFn: arg2\n    });\n  }\n\n  return _extends({}, arg2, {\n    queryKey: arg1\n  });\n}\nexport function parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return _extends({}, arg3, {\n        mutationKey: arg1,\n        mutationFn: arg2\n      });\n    }\n\n    return _extends({}, arg2, {\n      mutationKey: arg1\n    });\n  }\n\n  if (typeof arg1 === 'function') {\n    return _extends({}, arg2, {\n      mutationFn: arg1\n    });\n  }\n\n  return _extends({}, arg1);\n}\nexport function parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [_extends({}, arg2, {\n    queryKey: arg1\n  }), arg3] : [arg1 || {}, arg2];\n}\nexport function parseMutationFilterArgs(arg1, arg2) {\n  return isQueryKey(arg1) ? _extends({}, arg2, {\n    mutationKey: arg1\n  }) : arg1;\n}\nexport function mapQueryStatusFilter(active, inactive) {\n  if (active === true && inactive === true || active == null && inactive == null) {\n    return 'all';\n  } else if (active === false && inactive === false) {\n    return 'none';\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    var isActive = active != null ? active : !inactive;\n    return isActive ? 'active' : 'inactive';\n  }\n}\nexport function matchQuery(filters, query) {\n  var active = filters.active,\n      exact = filters.exact,\n      fetching = filters.fetching,\n      inactive = filters.inactive,\n      predicate = filters.predicate,\n      queryKey = filters.queryKey,\n      stale = filters.stale;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  var queryStatusFilter = mapQueryStatusFilter(active, inactive);\n\n  if (queryStatusFilter === 'none') {\n    return false;\n  } else if (queryStatusFilter !== 'all') {\n    var isActive = query.isActive();\n\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false;\n    }\n\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nexport function matchMutation(filters, mutation) {\n  var exact = filters.exact,\n      fetching = filters.fetching,\n      predicate = filters.predicate,\n      mutationKey = filters.mutationKey;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nexport function hashQueryKeyByOptions(queryKey, options) {\n  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n */\n\nexport function hashQueryKey(queryKey) {\n  var asArray = ensureQueryKeyArray(queryKey);\n  return stableValueHash(asArray);\n}\n/**\n * Hashes the value into a stable hash.\n */\n\nexport function stableValueHash(value) {\n  return JSON.stringify(value, function (_, val) {\n    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {\n      result[key] = val[key];\n      return result;\n    }, {}) : val;\n  });\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nexport function partialMatchKey(a, b) {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nexport function partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(function (key) {\n      return !partialDeepEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nexport function replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  var array = Array.isArray(a) && Array.isArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    var aSize = array ? a.length : Object.keys(a).length;\n    var bItems = array ? b : Object.keys(b);\n    var bSize = bItems.length;\n    var copy = array ? [] : {};\n    var equalItems = 0;\n\n    for (var i = 0; i < bSize; i++) {\n      var key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nexport function shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (var key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nexport function isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  var ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  var prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nexport function isQueryKey(value) {\n  return typeof value === 'string' || Array.isArray(value);\n}\nexport function isError(value) {\n  return value instanceof Error;\n}\nexport function sleep(timeout) {\n  return new Promise(function (resolve) {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nexport function scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}\nexport function getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n}", "import _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var FocusManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(Subscribable);\nexport var focusManager = new FocusManager();", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(Subscribable);\nexport var onlineManager = new OnlineManager();", "import { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { sleep } from './utils';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nexport function isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nexport var CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nexport function isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nexport var Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};", "import { scheduleMicrotask } from './utils'; // TYPES\n\n// CLASS\nexport var NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      scheduleMicrotask(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      scheduleMicrotask(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nexport var notifyManager = new NotifyManager();", "// TYPES\n// FUNCTIONS\nvar logger = console;\nexport function getLogger() {\n  return logger;\n}\nexport function setLogger(newLogger) {\n  logger = newLogger;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getAbortController, functionalUpdate, isValidTimeout, noop, replaceEqualDeep, timeUntilStale, ensureQueryKeyArray } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { getLogger } from './logger';\nimport { Retryer, isCancelledError } from './retryer'; // TYPES\n\n// CLASS\nexport var Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = _extends({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = functionalUpdate(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = ensureQueryKeyArray(this.queryKey);\n    var abortController = getAbortController(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          getLogger().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return _extends({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return _extends({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return _extends({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return _extends({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return _extends({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return _extends({}, this.revertState);\n        }\n\n        return _extends({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return _extends({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return _extends({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils';\nimport { Query } from './query';\nimport { notifyManager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var QueryCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return matchQuery(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return matchQuery(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(Subscribable);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getLogger } from './logger';\nimport { notify<PERSON><PERSON>ger } from './notifyManager';\nimport { Retryer } from './retryer';\nimport { noop } from './utils'; // TYPES\n\n// CLASS\nexport var Mutation = /*#__PURE__*/function () {\n  function Mutation(config) {\n    this.options = _extends({}, config.defaultOptions, config.options);\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.meta = config.meta;\n  }\n\n  var _proto = Mutation.prototype;\n\n  _proto.setState = function setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state: state\n    });\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    this.observers = this.observers.filter(function (x) {\n      return x !== observer;\n    });\n  };\n\n  _proto.cancel = function cancel() {\n    if (this.retryer) {\n      this.retryer.cancel();\n      return this.retryer.promise.then(noop).catch(noop);\n    }\n\n    return Promise.resolve();\n  };\n\n  _proto.continue = function _continue() {\n    if (this.retryer) {\n      this.retryer.continue();\n      return this.retryer.promise;\n    }\n\n    return this.execute();\n  };\n\n  _proto.execute = function execute() {\n    var _this = this;\n\n    var data;\n    var restored = this.state.status === 'loading';\n    var promise = Promise.resolve();\n\n    if (!restored) {\n      this.dispatch({\n        type: 'loading',\n        variables: this.options.variables\n      });\n      promise = promise.then(function () {\n        // Notify cache callback\n        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);\n      }).then(function () {\n        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);\n      }).then(function (context) {\n        if (context !== _this.state.context) {\n          _this.dispatch({\n            type: 'loading',\n            context: context,\n            variables: _this.state.variables\n          });\n        }\n      });\n    }\n\n    return promise.then(function () {\n      return _this.executeMutation();\n    }).then(function (result) {\n      data = result; // Notify cache callback\n\n      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);\n    }).then(function () {\n      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);\n    }).then(function () {\n      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);\n    }).then(function () {\n      _this.dispatch({\n        type: 'success',\n        data: data\n      });\n\n      return data;\n    }).catch(function (error) {\n      // Notify cache callback\n      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error\n\n      getLogger().error(error);\n      return Promise.resolve().then(function () {\n        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        _this.dispatch({\n          type: 'error',\n          error: error\n        });\n\n        throw error;\n      });\n    });\n  };\n\n  _proto.executeMutation = function executeMutation() {\n    var _this2 = this,\n        _this$options$retry;\n\n    this.retryer = new Retryer({\n      fn: function fn() {\n        if (!_this2.options.mutationFn) {\n          return Promise.reject('No mutationFn found');\n        }\n\n        return _this2.options.mutationFn(_this2.state.variables);\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n      retryDelay: this.options.retryDelay\n    });\n    return this.retryer.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onMutationUpdate(action);\n      });\n\n      _this3.mutationCache.notify(_this3);\n    });\n  };\n\n  return Mutation;\n}();\nexport function getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'failed':\n      return _extends({}, state, {\n        failureCount: state.failureCount + 1\n      });\n\n    case 'pause':\n      return _extends({}, state, {\n        isPaused: true\n      });\n\n    case 'continue':\n      return _extends({}, state, {\n        isPaused: false\n      });\n\n    case 'loading':\n      return _extends({}, state, {\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables\n      });\n\n    case 'success':\n      return _extends({}, state, {\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false\n      });\n\n    case 'error':\n      return _extends({}, state, {\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error'\n      });\n\n    case 'setState':\n      return _extends({}, state, action.state);\n\n    default:\n      return state;\n  }\n}", "import _inherits<PERSON><PERSON>e from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { notifyManager } from './notifyManager';\nimport { Mutation } from './mutation';\nimport { matchMutation, noop } from './utils';\nimport { Subscribable } from './subscribable'; // TYPES\n\n// CLASS\nexport var MutationCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(Subscribable);", "import { isCancelable } from './retryer';\nimport { getAbortController } from './utils';\nexport function infiniteQueryBehavior() {\n  return {\n    onFetch: function onFetch(context) {\n      context.fetchFn = function () {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        var abortController = getAbortController();\n        var abortSignal = abortController == null ? void 0 : abortController.signal;\n        var newPageParams = oldPageParams;\n        var cancelled = false; // Get query function\n\n        var queryFn = context.options.queryFn || function () {\n          return Promise.reject('Missing queryFn');\n        };\n\n        var buildNewPages = function buildNewPages(pages, param, page, previous) {\n          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);\n          return previous ? [page].concat(pages) : [].concat(pages, [page]);\n        }; // Create function to fetch a page\n\n\n        var fetchPage = function fetchPage(pages, manual, param, previous) {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          var queryFnContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta\n          };\n          var queryFnResult = queryFn(queryFnContext);\n          var promise = Promise.resolve(queryFnResult).then(function (page) {\n            return buildNewPages(pages, param, page, previous);\n          });\n\n          if (isCancelable(queryFnResult)) {\n            var promiseAsAny = promise;\n            promiseAsAny.cancel = queryFnResult.cancel;\n          }\n\n          return promise;\n        };\n\n        var promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n            var manual = typeof pageParam !== 'undefined';\n            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n            promise = fetchPage(oldPages, manual, param);\n          } // Fetch previous page?\n          else if (isFetchingPreviousPage) {\n              var _manual = typeof pageParam !== 'undefined';\n\n              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n\n              promise = fetchPage(oldPages, _manual, _param, true);\n            } // Refetch pages\n            else {\n                (function () {\n                  newPageParams = [];\n                  var manual = typeof context.options.getNextPageParam === 'undefined';\n                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n                  var _loop = function _loop(i) {\n                    promise = promise.then(function (pages) {\n                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n                      if (shouldFetchNextPage) {\n                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n\n                        return fetchPage(pages, manual, _param2);\n                      }\n\n                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n                    });\n                  };\n\n                  for (var i = 1; i < oldPages.length; i++) {\n                    _loop(i);\n                  }\n                })();\n              }\n\n        var finalPromise = promise.then(function (pages) {\n          return {\n            pages: pages,\n            pageParams: newPageParams\n          };\n        });\n        var finalPromiseAsAny = finalPromise;\n\n        finalPromiseAsAny.cancel = function () {\n          cancelled = true;\n          abortController == null ? void 0 : abortController.abort();\n\n          if (isCancelable(promise)) {\n            promise.cancel();\n          }\n        };\n\n        return finalPromise;\n      };\n    }\n  };\n}\nexport function getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nexport function getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nexport function hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    var nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nexport function hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    var previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { hashQuery<PERSON>ey, noop, parseFilterArgs, parseQueryArgs, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils';\nimport { QueryCache } from './queryCache';\nimport { MutationCache } from './mutationCache';\nimport { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { notifyManager } from './notifyManager';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior';\n// CLASS\nexport var QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = focusManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll(_extends({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = parseQueryArgs(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = _extends({}, filters, {\n      active: true\n    });\n\n    return notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = _extends({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, _extends({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return hashQueryKey(queryKey) === hashQueryKey(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return partialMatchKey(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return partialMatchKey(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = _extends({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return _extends({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { isServer, isValidTimeout, noop, replaceEqualDeep, shallowEqualObjects, timeUntilStale } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { focusManager } from './focusManager';\nimport { Subscribable } from './subscribable';\nimport { getLogger } from './logger';\nimport { isCancelledError } from './retryer';\nexport var QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch(_extends({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    var time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify(_extends({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { getDefaultState } from './mutation';\nimport { notify<PERSON>anager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, _extends({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n\n    var result = _extends({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(Subscribable);", "import ReactDOM from 'react-dom';\nexport var unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates;", "import { notifyManager } from '../core';\nimport { unstable_batchedUpdates } from './reactBatchedUpdates';\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates);", "export var logger = console;", "import { setLogger } from '../core';\nimport { logger } from './logger';\nsetLogger(logger);", "import React from 'react';\nvar defaultContext = /*#__PURE__*/React.createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/React.createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nexport var useQueryClient = function useQueryClient() {\n  var queryClient = React.useContext(getQueryClientContext(React.useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nexport var QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  React.useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/React.createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: client\n  }, children));\n};", "import React from 'react'; // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/React.createContext(createValue()); // HOOK\n\nexport var useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return React.useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nexport var QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = React.useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/React.createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};", "export function shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { noop, parseMutationArgs } from '../core/utils';\nimport { MutationObserver } from '../core/mutationObserver';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils'; // HOOK\n\nexport function useMutation(arg1, arg2, arg3) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = parseMutationArgs(arg1, arg2, arg3);\n  var queryClient = useQueryClient();\n  var obsRef = React.useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = React.useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(noop);\n  }, []);\n\n  if (currentResult.error && shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return _extends({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}", "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils';\nexport function useBaseQuery(options, Observer) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = useQueryClient();\n  var errorResetBoundary = useQueryErrorResetBoundary();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = React.useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}", "import { QueryObserver } from '../core';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useQuery(arg1, arg2, arg3) {\n  var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(parsedOptions, QueryObserver);\n}", "let e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}export{u as css,r as extractCss,b as glob,h as keyframes,m as setup,j as styled};\n", "\"use client\";\nvar W=e=>typeof e==\"function\",f=(e,t)=>W(e)?e(t):e;var F=(()=>{let e=0;return()=>(++e).toString()})(),A=(()=>{let e;return()=>{if(e===void 0&&typeof window<\"u\"){let t=matchMedia(\"(prefers-reduced-motion: reduce)\");e=!t||t.matches}return e}})();import{useEffect as H,useState as j,useRef as Q}from\"react\";var Y=20;var U=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,Y)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return U(e,{type:e.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(o=>o.id===s||s===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+a}))}}},P=[],y={toasts:[],pausedAt:void 0},u=e=>{y=U(y,e),P.forEach(t=>{t(y)})},q={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},D=(e={})=>{let[t,r]=j(y),s=Q(y);H(()=>(s.current!==y&&r(y),P.push(r),()=>{let o=P.indexOf(r);o>-1&&P.splice(o,1)}),[]);let a=t.toasts.map(o=>{var n,i,p;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((n=e[o.type])==null?void 0:n.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((i=e[o.type])==null?void 0:i.duration)||(e==null?void 0:e.duration)||q[o.type],style:{...e.style,...(p=e[o.type])==null?void 0:p.style,...o.style}}});return{...t,toasts:a}};var J=(e,t=\"blank\",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:\"status\",\"aria-live\":\"polite\"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||F()}),x=e=>(t,r)=>{let s=J(t,e,r);return u({type:2,toast:s}),s.id},c=(e,t)=>x(\"blank\")(e,t);c.error=x(\"error\");c.success=x(\"success\");c.loading=x(\"loading\");c.custom=x(\"custom\");c.dismiss=e=>{u({type:3,toastId:e})};c.remove=e=>u({type:4,toastId:e});c.promise=(e,t,r)=>{let s=c.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e==\"function\"&&(e=e()),e.then(a=>{let o=t.success?f(t.success,a):void 0;return o?c.success(o,{id:s,...r,...r==null?void 0:r.success}):c.dismiss(s),a}).catch(a=>{let o=t.error?f(t.error,a):void 0;o?c.error(o,{id:s,...r,...r==null?void 0:r.error}):c.dismiss(s)}),e};import{useEffect as $,useCallback as L}from\"react\";var K=(e,t)=>{u({type:1,toast:{id:e,height:t}})},X=()=>{u({type:5,time:Date.now()})},b=new Map,Z=1e3,ee=(e,t=Z)=>{if(b.has(e))return;let r=setTimeout(()=>{b.delete(e),u({type:4,toastId:e})},t);b.set(e,r)},O=e=>{let{toasts:t,pausedAt:r}=D(e);$(()=>{if(r)return;let o=Date.now(),n=t.map(i=>{if(i.duration===1/0)return;let p=(i.duration||0)+i.pauseDuration-(o-i.createdAt);if(p<0){i.visible&&c.dismiss(i.id);return}return setTimeout(()=>c.dismiss(i.id),p)});return()=>{n.forEach(i=>i&&clearTimeout(i))}},[t,r]);let s=L(()=>{r&&u({type:6,time:Date.now()})},[r]),a=L((o,n)=>{let{reverseOrder:i=!1,gutter:p=8,defaultPosition:d}=n||{},h=t.filter(m=>(m.position||d)===(o.position||d)&&m.height),v=h.findIndex(m=>m.id===o.id),S=h.filter((m,E)=>E<v&&m.visible).length;return h.filter(m=>m.visible).slice(...i?[S+1]:[0,S]).reduce((m,E)=>m+(E.height||0)+p,0)},[t]);return $(()=>{t.forEach(o=>{if(o.dismissed)ee(o.id,o.removeDelay);else{let n=b.get(o.id);n&&(clearTimeout(n),b.delete(o.id))}})},[t]),{toasts:t,handlers:{updateHeight:K,startPause:X,endPause:s,calculateOffset:a}}};import*as l from\"react\";import{styled as B,keyframes as z}from\"goober\";import*as g from\"react\";import{styled as w,keyframes as me}from\"goober\";import{styled as te,keyframes as I}from\"goober\";var oe=I`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`,re=I`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`,se=I`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`,k=te(\"div\")`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${e=>e.primary||\"#ff4b4b\"};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${oe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${re} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${e=>e.secondary||\"#fff\"};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${se} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;import{styled as ae,keyframes as ie}from\"goober\";var ne=ie`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`,V=ae(\"div\")`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${e=>e.secondary||\"#e0e0e0\"};\n  border-right-color: ${e=>e.primary||\"#616161\"};\n  animation: ${ne} 1s linear infinite;\n`;import{styled as ce,keyframes as N}from\"goober\";var pe=N`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`,de=N`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`,_=ce(\"div\")`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${e=>e.primary||\"#61d345\"};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${pe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${de} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${e=>e.secondary||\"#fff\"};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;var ue=w(\"div\")`\n  position: absolute;\n`,le=w(\"div\")`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`,fe=me`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`,Te=w(\"div\")`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${fe} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`,M=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return t!==void 0?typeof t==\"string\"?g.createElement(Te,null,t):t:r===\"blank\"?null:g.createElement(le,null,g.createElement(V,{...s}),r!==\"loading\"&&g.createElement(ue,null,r===\"error\"?g.createElement(k,{...s}):g.createElement(_,{...s})))};var ye=e=>`\n0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,ge=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}\n`,he=\"0%{opacity:0;} 100%{opacity:1;}\",xe=\"0%{opacity:1;} 100%{opacity:0;}\",be=B(\"div\")`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`,Se=B(\"div\")`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`,Ae=(e,t)=>{let s=e.includes(\"top\")?1:-1,[a,o]=A()?[he,xe]:[ye(s),ge(s)];return{animation:t?`${z(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${z(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},C=l.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?Ae(e.position||t||\"top-center\",e.visible):{opacity:0},o=l.createElement(M,{toast:e}),n=l.createElement(Se,{...e.ariaProps},f(e.message,e));return l.createElement(be,{className:e.className,style:{...a,...r,...e.style}},typeof s==\"function\"?s({icon:o,message:n}):l.createElement(l.Fragment,null,o,n))});import{css as Pe,setup as Re}from\"goober\";import*as T from\"react\";Re(T.createElement);var ve=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let o=T.useCallback(n=>{if(n){let i=()=>{let p=n.getBoundingClientRect().height;s(e,p)};i(),new MutationObserver(i).observe(n,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return T.createElement(\"div\",{ref:o,className:t,style:r},a)},Ee=(e,t)=>{let r=e.includes(\"top\"),s=r?{top:0}:{bottom:0},a=e.includes(\"center\")?{justifyContent:\"center\"}:e.includes(\"right\")?{justifyContent:\"flex-end\"}:{};return{left:0,right:0,display:\"flex\",position:\"absolute\",transition:A()?void 0:\"all 230ms cubic-bezier(.21,1.02,.73,1)\",transform:`translateY(${t*(r?1:-1)}px)`,...s,...a}},De=Pe`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`,R=16,Oe=({reverseOrder:e,position:t=\"top-center\",toastOptions:r,gutter:s,children:a,containerStyle:o,containerClassName:n})=>{let{toasts:i,handlers:p}=O(r);return T.createElement(\"div\",{id:\"_rht_toaster\",style:{position:\"fixed\",zIndex:9999,top:R,left:R,right:R,bottom:R,pointerEvents:\"none\",...o},className:n,onMouseEnter:p.startPause,onMouseLeave:p.endPause},i.map(d=>{let h=d.position||t,v=p.calculateOffset(d,{reverseOrder:e,gutter:s,defaultPosition:t}),S=Ee(h,v);return T.createElement(ve,{id:d.id,key:d.id,onHeightUpdate:p.updateHeight,className:d.visible?De:\"\",style:S},d.type===\"custom\"?f(d.message,d):a?a(d):T.createElement(C,{toast:d,position:h}))}))};var Vt=c;export{_ as CheckmarkIcon,k as ErrorIcon,V as LoaderIcon,C as ToastBar,M as ToastIcon,Oe as Toaster,Vt as default,f as resolveValue,c as toast,O as useToaster,D as useToasterStore};\n//# sourceMappingURL=index.mjs.map", "import React, { createContext, useContext, useEffect, useState } from 'react'\nimport { useQuery, useMutation, useQueryClient } from 'react-query'\nimport toast from 'react-hot-toast'\n\n// 类型定义\ninterface User {\n  id: string\n  username: string\n  email: string\n  level: number\n  experience: number\n  coins: number\n  gems: number\n  avatar?: string\n  isActive?: boolean\n  isPremium?: boolean\n  createdAt: string\n  lastLoginAt: string\n}\n\ninterface LoginCredentials {\n  email: string\n  password: string\n}\n\ninterface RegisterCredentials {\n  username: string\n  email: string\n  password: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  login: (credentials: LoginCredentials) => Promise<void>\n  register: (credentials: RegisterCredentials) => Promise<void>\n  logout: () => void\n  updateProfile: (data: Partial<User>) => Promise<void>\n}\n\n// Mock data for development\nconst mockUser: User = {\n  id: '1',\n  username: '测试用户',\n  email: '<EMAIL>',\n  level: 5,\n  experience: 1250,\n  coins: 5000,\n  gems: 100,\n  isActive: true,\n  isPremium: false,\n  createdAt: new Date().toISOString(),\n  lastLoginAt: new Date().toISOString(),\n}\n\n// API 函数\nconst authAPI = {\n  // 获取当前用户信息\n  getCurrentUser: async (): Promise<User> => {\n    const token = localStorage.getItem('token')\n    if (!token) throw new Error('No token found')\n\n    try {\n      const response = await fetch('/api/auth/me', {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to get user info')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock data')\n      return mockUser\n    }\n  },\n\n  // 登录\n  login: async (credentials: LoginCredentials): Promise<{ user: User; token: string }> => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.message || 'Login failed')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock data')\n      return {\n        user: mockUser,\n        token: 'mock-token-' + Date.now(),\n      }\n    }\n  },\n\n  // 注册\n  register: async (credentials: RegisterCredentials): Promise<{ user: User; token: string }> => {\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.message || 'Registration failed')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock data')\n      return {\n        user: { ...mockUser, username: credentials.username, email: credentials.email },\n        token: 'mock-token-' + Date.now(),\n      }\n    }\n  },\n\n  // 更新用户信息\n  updateProfile: async (data: Partial<User>): Promise<User> => {\n    const token = localStorage.getItem('token')\n    if (!token) throw new Error('No token found')\n\n    try {\n      const response = await fetch('/api/users/profile', {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.message || 'Update failed')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock data')\n      return { ...mockUser, ...data }\n    }\n  },\n}\n\n// 创建 Context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\n// AuthProvider 组件\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const queryClient = useQueryClient()\n\n  // 获取用户信息查询\n  const { isLoading } = useQuery(\n    'currentUser',\n    authAPI.getCurrentUser,\n    {\n      enabled: !!localStorage.getItem('token'),\n      retry: false,\n      onSuccess: (data) => {\n        setUser(data)\n      },\n      onError: () => {\n        localStorage.removeItem('token')\n        setUser(null)\n      },\n    }\n  )\n\n  // 登录 mutation\n  const loginMutation = useMutation(authAPI.login, {\n    onSuccess: (data) => {\n      localStorage.setItem('token', data.token)\n      setUser(data.user)\n      queryClient.setQueryData('currentUser', data.user)\n      toast.success('登录成功！')\n    },\n    onError: (error: Error) => {\n      toast.error(error.message || '登录失败')\n    },\n  })\n\n  // 注册 mutation\n  const registerMutation = useMutation(authAPI.register, {\n    onSuccess: (data) => {\n      localStorage.setItem('token', data.token)\n      setUser(data.user)\n      queryClient.setQueryData('currentUser', data.user)\n      toast.success('注册成功！')\n    },\n    onError: (error: Error) => {\n      toast.error(error.message || '注册失败')\n    },\n  })\n\n  // 更新用户信息 mutation\n  const updateProfileMutation = useMutation(authAPI.updateProfile, {\n    onSuccess: (data) => {\n      setUser(data)\n      queryClient.setQueryData('currentUser', data)\n      toast.success('个人信息更新成功！')\n    },\n    onError: (error: Error) => {\n      toast.error(error.message || '更新失败')\n    },\n  })\n\n  // 登录函数\n  const login = async (credentials: LoginCredentials) => {\n    await loginMutation.mutateAsync(credentials)\n  }\n\n  // 注册函数\n  const register = async (credentials: RegisterCredentials) => {\n    await registerMutation.mutateAsync(credentials)\n  }\n\n  // 登出函数\n  const logout = () => {\n    localStorage.removeItem('token')\n    setUser(null)\n    queryClient.clear()\n    toast.success('已退出登录')\n  }\n\n  // 更新用户信息函数\n  const updateProfile = async (data: Partial<User>) => {\n    await updateProfileMutation.mutateAsync(data)\n  }\n\n  // 初始化时检查 token\n  useEffect(() => {\n    const token = localStorage.getItem('token')\n    if (!token) {\n      setUser(null)\n    }\n  }, [])\n\n  const value: AuthContextType = {\n    user,\n    loading: isLoading,\n    login,\n    register,\n    logout,\n    updateProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\n// useAuth hook\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n", "import React from 'react'\nimport { Link } from 'react-router-dom'\nimport styled from 'styled-components'\nimport { useAuth } from '../contexts/AuthContext'\n\nconst Container = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  text-align: center;\n`\n\nconst Title = styled.h1`\n  font-size: 3rem;\n  font-weight: bold;\n  color: white;\n  margin-bottom: 1rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n`\n\nconst Subtitle = styled.p`\n  font-size: 1.2rem;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 2rem;\n  max-width: 600px;\n`\n\nconst WelcomeMessage = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`\n\nconst UserInfo = styled.div`\n  color: white;\n  margin-bottom: 1rem;\n  \n  h2 {\n    font-size: 1.5rem;\n    margin-bottom: 0.5rem;\n  }\n  \n  p {\n    opacity: 0.8;\n    margin: 0.25rem 0;\n  }\n`\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  justify-content: center;\n`\n\nconst Button = styled(Link)`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n  color: white;\n  text-decoration: none;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1.1rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`\n\nconst SecondaryButton = styled(Button)`\n  background: linear-gradient(135deg, #4834d4, #686de0);\n`\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1rem;\n  margin-top: 2rem;\n  width: 100%;\n  max-width: 600px;\n`\n\nconst StatCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  \n  h3 {\n    color: white;\n    font-size: 2rem;\n    font-weight: bold;\n    margin-bottom: 0.5rem;\n  }\n  \n  p {\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 0.9rem;\n    margin: 0;\n  }\n`\n\nconst HomePage: React.FC = () => {\n  const { user } = useAuth()\n\n  if (!user) {\n    return (\n      <Container>\n        <Title>星光对对碰</Title>\n        <Subtitle>\n          欢迎来到最有趣的三消游戏！匹配星星，获得高分，挑战无限关卡！\n        </Subtitle>\n        <ButtonGroup>\n          <Button to=\"/login\">开始游戏</Button>\n        </ButtonGroup>\n      </Container>\n    )\n  }\n\n  return (\n    <Container>\n      <Title>星光对对碰</Title>\n      \n      <WelcomeMessage>\n        <UserInfo>\n          <h2>欢迎回来，{user.username}！</h2>\n          <p>等级：{user.level}</p>\n          <p>经验值：{user.experience}</p>\n          <p>金币：{user.coins}</p>\n          <p>宝石：{user.gems}</p>\n        </UserInfo>\n      </WelcomeMessage>\n\n      <ButtonGroup>\n        <Button to=\"/levels\">选择关卡</Button>\n        <SecondaryButton to=\"/game\">快速开始</SecondaryButton>\n        <SecondaryButton to=\"/leaderboard\">排行榜</SecondaryButton>\n        <SecondaryButton to=\"/profile\">个人资料</SecondaryButton>\n      </ButtonGroup>\n\n      <StatsGrid>\n        <StatCard>\n          <h3>{user.level}</h3>\n          <p>当前等级</p>\n        </StatCard>\n        <StatCard>\n          <h3>{user.experience}</h3>\n          <p>总经验值</p>\n        </StatCard>\n        <StatCard>\n          <h3>{user.coins}</h3>\n          <p>金币</p>\n        </StatCard>\n        <StatCard>\n          <h3>{user.gems}</h3>\n          <p>宝石</p>\n        </StatCard>\n      </StatsGrid>\n    </Container>\n  )\n}\n\nexport default HomePage\n", "import React, { useState } from 'react'\nimport { Link } from 'react-router-dom'\nimport styled from 'styled-components'\nimport { useAuth } from '../contexts/AuthContext'\n\nconst Container = styled.div`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n`\n\nconst LoginCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 3rem;\n  width: 100%;\n  max-width: 400px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n`\n\nconst Title = styled.h1`\n  color: white;\n  text-align: center;\n  margin-bottom: 2rem;\n  font-size: 2rem;\n  font-weight: bold;\n`\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n`\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`\n\nconst Label = styled.label`\n  color: white;\n  font-weight: 500;\n  font-size: 0.9rem;\n`\n\nconst Input = styled.input`\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: #4834d4;\n    box-shadow: 0 0 0 3px rgba(72, 52, 212, 0.3);\n  }\n`\n\nconst Button = styled.button`\n  padding: 1rem;\n  background: linear-gradient(135deg, #4834d4, #686de0);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`\n\nconst LinkText = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  \n  a {\n    color: #4834d4;\n    text-decoration: none;\n    font-weight: 600;\n    \n    &:hover {\n      text-decoration: underline;\n    }\n  }\n`\n\nconst ErrorMessage = styled.div`\n  background: rgba(239, 68, 68, 0.2);\n  border: 1px solid rgba(239, 68, 68, 0.5);\n  color: #fca5a5;\n  padding: 1rem;\n  border-radius: 8px;\n  text-align: center;\n  font-size: 0.9rem;\n`\n\nconst LoginPage: React.FC = () => {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  \n  const { login } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    try {\n      await login({ email, password })\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '登录失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Container>\n      <LoginCard>\n        <Title>登录</Title>\n        \n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        \n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <Label htmlFor=\"email\">邮箱</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder=\"请输入邮箱\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <InputGroup>\n            <Label htmlFor=\"password\">密码</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <Button type=\"submit\" disabled={loading}>\n            {loading ? '登录中...' : '登录'}\n          </Button>\n        </Form>\n        \n        <LinkText>\n          还没有账号？ <Link to=\"/register\">立即注册</Link>\n        </LinkText>\n      </LoginCard>\n    </Container>\n  )\n}\n\nexport default LoginPage\n", "import React, { useState } from 'react'\nimport { Link } from 'react-router-dom'\nimport styled from 'styled-components'\nimport { useAuth } from '../contexts/AuthContext'\n\nconst Container = styled.div`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n`\n\nconst RegisterCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 3rem;\n  width: 100%;\n  max-width: 400px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n`\n\nconst Title = styled.h1`\n  color: white;\n  text-align: center;\n  margin-bottom: 2rem;\n  font-size: 2rem;\n  font-weight: bold;\n`\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n`\n\nconst InputGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`\n\nconst Label = styled.label`\n  color: white;\n  font-weight: 500;\n  font-size: 0.9rem;\n`\n\nconst Input = styled.input`\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: #4834d4;\n    box-shadow: 0 0 0 3px rgba(72, 52, 212, 0.3);\n  }\n`\n\nconst Button = styled.button`\n  padding: 1rem;\n  background: linear-gradient(135deg, #4834d4, #686de0);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`\n\nconst LinkText = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  \n  a {\n    color: #4834d4;\n    text-decoration: none;\n    font-weight: 600;\n    \n    &:hover {\n      text-decoration: underline;\n    }\n  }\n`\n\nconst ErrorMessage = styled.div`\n  background: rgba(239, 68, 68, 0.2);\n  border: 1px solid rgba(239, 68, 68, 0.5);\n  color: #fca5a5;\n  padding: 1rem;\n  border-radius: 8px;\n  text-align: center;\n  font-size: 0.9rem;\n`\n\nconst RegisterPage: React.FC = () => {\n  const [username, setUsername] = useState('')\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  \n  const { register } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n\n    // 验证密码\n    if (password !== confirmPassword) {\n      setError('两次输入的密码不一致')\n      return\n    }\n\n    if (password.length < 6) {\n      setError('密码长度至少为6位')\n      return\n    }\n\n    setLoading(true)\n\n    try {\n      await register({ username, email, password })\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '注册失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Container>\n      <RegisterCard>\n        <Title>注册</Title>\n        \n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        \n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <Label htmlFor=\"username\">用户名</Label>\n            <Input\n              id=\"username\"\n              type=\"text\"\n              placeholder=\"请输入用户名\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <InputGroup>\n            <Label htmlFor=\"email\">邮箱</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder=\"请输入邮箱\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <InputGroup>\n            <Label htmlFor=\"password\">密码</Label>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              placeholder=\"请输入密码（至少6位）\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <InputGroup>\n            <Label htmlFor=\"confirmPassword\">确认密码</Label>\n            <Input\n              id=\"confirmPassword\"\n              type=\"password\"\n              placeholder=\"请再次输入密码\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              required\n            />\n          </InputGroup>\n          \n          <Button type=\"submit\" disabled={loading}>\n            {loading ? '注册中...' : '注册'}\n          </Button>\n        </Form>\n        \n        <LinkText>\n          已有账号？ <Link to=\"/login\">立即登录</Link>\n        </LinkText>\n      </RegisterCard>\n    </Container>\n  )\n}\n\nexport default RegisterPage\n", "import { lazy } from 'react'\n\n// 懒加载游戏相关组件\nexport const LazyGamePage = lazy(() => import('../pages/GamePage'))\nexport const LazyLevelsPage = lazy(() => import('../pages/LevelsPage'))\nexport const LazyLeaderboardPage = lazy(() => import('../pages/LeaderboardPage'))\nexport const LazyAchievementsPage = lazy(() => import('../pages/AchievementsPage'))\nexport const LazyProfilePage = lazy(() => import('../pages/ProfilePage'))\n\n// 懒加载游戏组件\nexport const LazyGameBoard = lazy(() => import('../components/Game/GameBoard'))\nexport const LazyPowerUpPanel = lazy(() => import('../components/Game/PowerUpPanel'))\nexport const LazyShareModal = lazy(() => import('../components/Game/ShareModal'))\nexport const LazyGameOverModal = lazy(() => import('../components/Game/GameOverModal'))\n\n// 懒加载管理工具 - 这些不是React组件，所以不应该使用lazy\n// 改为直接导入工厂函数\nexport const createAnimationManager = () => import('../utils/animationManager').then(module => module.AnimationManager)\nexport const createSpecialGemManager = () => import('../utils/specialGemManager').then(module => module.SpecialGemManager)\nexport const createPowerUpManager = () => import('../utils/powerUpManager').then(module => module.PowerUpManager)\nexport const createAchievementManager = () => import('../utils/achievementManager').then(module => module.AchievementManager)\n\n// 预加载函数\nexport const preloadGameComponents = () => {\n  // 预加载核心游戏组件\n  LazyGamePage\n  LazyGameBoard\n  LazyPowerUpPanel\n  \n  // 预加载游戏管理器\n  createAnimationManager()\n  createSpecialGemManager()\n  createPowerUpManager()\n}\n\nexport const preloadSocialComponents = () => {\n  // 预加载社交功能组件\n  LazyLeaderboardPage\n  LazyAchievementsPage\n  LazyShareModal\n}\n\nexport const preloadAllComponents = () => {\n  preloadGameComponents()\n  preloadSocialComponents()\n  LazyLevelsPage\n  LazyProfilePage\n  LazyGameOverModal\n  createAchievementManager()\n}\n\n// 组件预加载策略\nexport class ComponentPreloader {\n  private static preloadedComponents = new Set<string>()\n  \n  static async preloadComponent(componentName: string, loader: () => Promise<any>) {\n    if (this.preloadedComponents.has(componentName)) {\n      return\n    }\n    \n    try {\n      await loader()\n      this.preloadedComponents.add(componentName)\n      console.log(`预加载组件成功: ${componentName}`)\n    } catch (error) {\n      console.error(`预加载组件失败: ${componentName}`, error)\n    }\n  }\n  \n  static async preloadOnIdle() {\n    if ('requestIdleCallback' in window) {\n      return new Promise<void>((resolve) => {\n        requestIdleCallback(() => {\n          preloadAllComponents()\n          resolve()\n        })\n      })\n    } else {\n      // 降级方案\n      setTimeout(() => {\n        preloadAllComponents()\n      }, 100)\n    }\n  }\n  \n  static async preloadOnUserInteraction() {\n    const events = ['mousedown', 'touchstart', 'keydown']\n    \n    const preloadOnce = () => {\n      preloadGameComponents()\n      events.forEach(event => {\n        document.removeEventListener(event, preloadOnce)\n      })\n    }\n    \n    events.forEach(event => {\n      document.addEventListener(event, preloadOnce, { once: true, passive: true })\n    })\n  }\n  \n  static getPreloadedComponents() {\n    return Array.from(this.preloadedComponents)\n  }\n  \n  static clearPreloadedComponents() {\n    this.preloadedComponents.clear()\n  }\n}\n\n// 资源预加载\nexport class ResourcePreloader {\n  private static loadedResources = new Set<string>()\n  \n  static async preloadImages(imageUrls: string[]) {\n    const promises = imageUrls.map(url => this.preloadImage(url))\n    return Promise.allSettled(promises)\n  }\n  \n  static preloadImage(url: string): Promise<void> {\n    if (this.loadedResources.has(url)) {\n      return Promise.resolve()\n    }\n    \n    return new Promise((resolve, reject) => {\n      const img = new Image()\n      img.onload = () => {\n        this.loadedResources.add(url)\n        resolve()\n      }\n      img.onerror = reject\n      img.src = url\n    })\n  }\n  \n  static async preloadAudio(audioUrls: string[]) {\n    const promises = audioUrls.map(url => this.preloadAudioFile(url))\n    return Promise.allSettled(promises)\n  }\n  \n  static preloadAudioFile(url: string): Promise<void> {\n    if (this.loadedResources.has(url)) {\n      return Promise.resolve()\n    }\n    \n    return new Promise((resolve, reject) => {\n      const audio = new Audio()\n      audio.oncanplaythrough = () => {\n        this.loadedResources.add(url)\n        resolve()\n      }\n      audio.onerror = reject\n      audio.src = url\n      audio.load()\n    })\n  }\n  \n  static getLoadedResources() {\n    return Array.from(this.loadedResources)\n  }\n}\n\n// 内存管理\nexport class MemoryManager {\n  private static readonly MAX_CACHE_SIZE = 50 * 1024 * 1024 // 50MB\n  private static cache = new Map<string, any>()\n  private static cacheSize = 0\n  \n  static set(key: string, value: any, size: number = 0) {\n    // 如果缓存过大，清理最旧的条目\n    while (this.cacheSize + size > this.MAX_CACHE_SIZE && this.cache.size > 0) {\n      const firstKey = this.cache.keys().next().value\n      this.delete(firstKey!)\n    }\n    \n    this.cache.set(key, value)\n    this.cacheSize += size\n  }\n  \n  static get(key: string) {\n    return this.cache.get(key)\n  }\n  \n  static delete(key: string) {\n    if (this.cache.has(key)) {\n      this.cache.delete(key)\n      // 简化的大小计算\n      this.cacheSize = Math.max(0, this.cacheSize - 1024)\n    }\n  }\n  \n  static clear() {\n    this.cache.clear()\n    this.cacheSize = 0\n  }\n  \n  static getCacheInfo() {\n    return {\n      size: this.cache.size,\n      memoryUsage: this.cacheSize,\n      maxSize: this.MAX_CACHE_SIZE\n    }\n  }\n  \n  static cleanup() {\n    // 清理过期或不常用的缓存项\n    const now = Date.now()\n    const maxAge = 5 * 60 * 1000 // 5分钟\n    \n    for (const [key, value] of this.cache.entries()) {\n      if (value.timestamp && now - value.timestamp > maxAge) {\n        this.delete(key)\n      }\n    }\n  }\n}\n\n// 自动清理定时器\nsetInterval(() => {\n  MemoryManager.cleanup()\n}, 60000) // 每分钟清理一次\n", "import React, { useState, useEffect } from 'react'\nimport styled from 'styled-components'\nimport { globalPerformanceMonitor, PerformanceReport } from '../../utils/performanceMonitor'\nimport { MemoryManager } from '../../utils/lazyComponents'\n\ninterface PerformancePanelProps {\n  isVisible: boolean\n  onToggle: () => void\n}\n\nconst Panel = styled.div<{ $visible: boolean }>`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  width: 320px;\n  max-height: 80vh;\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  color: white;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  z-index: 10000;\n  transform: translateX(${props => props.$visible ? '0' : '100%'});\n  transition: transform 0.3s ease-in-out;\n  overflow: hidden;\n`\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  \n  h3 {\n    margin: 0;\n    font-size: 14px;\n    font-weight: bold;\n  }\n  \n  button {\n    background: none;\n    border: none;\n    color: white;\n    cursor: pointer;\n    font-size: 16px;\n    padding: 0;\n    width: 20px;\n    height: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    &:hover {\n      background: rgba(255, 255, 255, 0.1);\n      border-radius: 4px;\n    }\n  }\n`\n\nconst Content = styled.div`\n  padding: 12px;\n  max-height: calc(80vh - 50px);\n  overflow-y: auto;\n  \n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(255, 255, 255, 0.3);\n    border-radius: 3px;\n  }\n`\n\nconst Section = styled.div`\n  margin-bottom: 16px;\n  \n  h4 {\n    margin: 0 0 8px 0;\n    font-size: 13px;\n    color: #ffd700;\n    border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n    padding-bottom: 4px;\n  }\n`\n\nconst MetricRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 4px;\n  \n  .label {\n    color: #ccc;\n  }\n  \n  .value {\n    color: white;\n    font-weight: bold;\n  }\n  \n  &.warning .value {\n    color: #ff9500;\n  }\n  \n  &.error .value {\n    color: #ff3b30;\n  }\n  \n  &.good .value {\n    color: #30d158;\n  }\n`\n\nconst RecommendationList = styled.ul`\n  margin: 8px 0 0 0;\n  padding-left: 16px;\n  \n  li {\n    margin-bottom: 4px;\n    color: #ff9500;\n    font-size: 11px;\n    line-height: 1.3;\n  }\n`\n\nconst ToggleButton = styled.button<{ $visible: boolean }>`\n  position: fixed;\n  top: 20px;\n  right: ${props => props.$visible ? '340px' : '20px'};\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 4px;\n  color: white;\n  padding: 8px 12px;\n  cursor: pointer;\n  font-size: 12px;\n  z-index: 10001;\n  transition: right 0.3s ease-in-out;\n  \n  &:hover {\n    background: rgba(0, 0, 0, 0.9);\n  }\n`\n\nconst PerformancePanel: React.FC<PerformancePanelProps> = ({ isVisible, onToggle }) => {\n  const [report, setReport] = useState<PerformanceReport | null>(null)\n  const [memoryInfo, setMemoryInfo] = useState<any>(null)\n\n  useEffect(() => {\n    const updateReport = () => {\n      const newReport = globalPerformanceMonitor.getPerformanceReport()\n      setReport(newReport)\n      setMemoryInfo(MemoryManager.getCacheInfo())\n    }\n\n    // 初始更新\n    updateReport()\n\n    // 定期更新\n    const interval = setInterval(updateReport, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const formatValue = (value: number | undefined, unit: string = '', decimals: number = 1): string => {\n    if (value === undefined || value === null) return 'N/A'\n    return `${value.toFixed(decimals)}${unit}`\n  }\n\n  const getMetricClass = (value: number | undefined, thresholds: { good: number; warning: number }): string => {\n    if (value === undefined) return ''\n    if (value <= thresholds.good) return 'good'\n    if (value <= thresholds.warning) return 'warning'\n    return 'error'\n  }\n\n  const formatBytes = (bytes: number): string => {\n    if (bytes === 0) return '0 B'\n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`\n  }\n\n  if (!report) return null\n\n  return (\n    <>\n      <ToggleButton $visible={isVisible} onClick={onToggle}>\n        {isVisible ? '隐藏' : '性能'}\n      </ToggleButton>\n      \n      <Panel $visible={isVisible}>\n        <Header>\n          <h3>性能监控</h3>\n          <button onClick={onToggle}>×</button>\n        </Header>\n        \n        <Content>\n          <Section>\n            <h4>当前性能</h4>\n            {report.current && (\n              <>\n                <MetricRow className={getMetricClass(report.current.fps, { good: 55, warning: 30 })}>\n                  <span className=\"label\">FPS:</span>\n                  <span className=\"value\">{formatValue(report.current.fps, '', 0)}</span>\n                </MetricRow>\n                <MetricRow className={getMetricClass(report.current.frameTime, { good: 16, warning: 33 })}>\n                  <span className=\"label\">帧时间:</span>\n                  <span className=\"value\">{formatValue(report.current.frameTime, 'ms')}</span>\n                </MetricRow>\n                <MetricRow className={getMetricClass(report.current.renderTime, { good: 8, warning: 16 })}>\n                  <span className=\"label\">渲染时间:</span>\n                  <span className=\"value\">{formatValue(report.current.renderTime, 'ms')}</span>\n                </MetricRow>\n                <MetricRow className={getMetricClass(report.current.updateTime, { good: 4, warning: 8 })}>\n                  <span className=\"label\">更新时间:</span>\n                  <span className=\"value\">{formatValue(report.current.updateTime, 'ms')}</span>\n                </MetricRow>\n              </>\n            )}\n          </Section>\n\n          <Section>\n            <h4>平均性能 (5秒)</h4>\n            {report.recent && (\n              <>\n                <MetricRow className={getMetricClass(report.recent.fps, { good: 55, warning: 30 })}>\n                  <span className=\"label\">平均FPS:</span>\n                  <span className=\"value\">{formatValue(report.recent.fps, '', 0)}</span>\n                </MetricRow>\n                <MetricRow className={getMetricClass(report.recent.frameTime, { good: 16, warning: 33 })}>\n                  <span className=\"label\">平均帧时间:</span>\n                  <span className=\"value\">{formatValue(report.recent.frameTime, 'ms')}</span>\n                </MetricRow>\n                <MetricRow className={getMetricClass(report.recent.renderTime, { good: 8, warning: 16 })}>\n                  <span className=\"label\">平均渲染:</span>\n                  <span className=\"value\">{formatValue(report.recent.renderTime, 'ms')}</span>\n                </MetricRow>\n              </>\n            )}\n          </Section>\n\n          <Section>\n            <h4>内存使用</h4>\n            {report.current && (\n              <MetricRow className={getMetricClass(report.current.memoryUsage, { good: 50 * 1024 * 1024, warning: 100 * 1024 * 1024 })}>\n                <span className=\"label\">JS堆内存:</span>\n                <span className=\"value\">{formatBytes(report.current.memoryUsage)}</span>\n              </MetricRow>\n            )}\n            {memoryInfo && (\n              <>\n                <MetricRow>\n                  <span className=\"label\">缓存项目:</span>\n                  <span className=\"value\">{memoryInfo.size}</span>\n                </MetricRow>\n                <MetricRow>\n                  <span className=\"label\">缓存大小:</span>\n                  <span className=\"value\">{formatBytes(memoryInfo.memoryUsage)}</span>\n                </MetricRow>\n              </>\n            )}\n          </Section>\n\n          {report.recommendations.length > 0 && (\n            <Section>\n              <h4>优化建议</h4>\n              <RecommendationList>\n                {report.recommendations.map((rec, index) => (\n                  <li key={index}>{rec}</li>\n                ))}\n              </RecommendationList>\n            </Section>\n          )}\n        </Content>\n      </Panel>\n    </>\n  )\n}\n\nexport default PerformancePanel\n", "import React from 'react'\nimport { Link, useLocation } from 'react-router-dom'\nimport styled from 'styled-components'\nimport { useAuth } from '../../contexts/AuthContext'\n\nconst HeaderContainer = styled.header`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  padding: 1rem 2rem;\n`\n\nconst Nav = styled.nav`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n`\n\nconst Logo = styled(Link)`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: white;\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  &:hover {\n    opacity: 0.8;\n  }\n`\n\nconst NavLinks = styled.div`\n  display: flex;\n  gap: 2rem;\n  align-items: center;\n  \n  @media (max-width: 768px) {\n    gap: 1rem;\n  }\n`\n\nconst NavLink = styled(Link)<{ $active?: boolean }>`\n  color: ${props => props.$active ? '#4834d4' : 'rgba(255, 255, 255, 0.9)'};\n  text-decoration: none;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    color: white;\n  }\n`\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: white;\n  \n  @media (max-width: 768px) {\n    display: none;\n  }\n`\n\nconst UserStats = styled.div`\n  display: flex;\n  gap: 1rem;\n  font-size: 0.9rem;\n  \n  span {\n    background: rgba(255, 255, 255, 0.1);\n    padding: 0.25rem 0.5rem;\n    border-radius: 6px;\n  }\n`\n\nconst LogoutButton = styled.button`\n  background: rgba(239, 68, 68, 0.2);\n  border: 1px solid rgba(239, 68, 68, 0.5);\n  color: #fca5a5;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(239, 68, 68, 0.3);\n  }\n`\n\nconst MobileMenu = styled.div`\n  display: none;\n  \n  @media (max-width: 768px) {\n    display: block;\n    color: white;\n    font-size: 1.5rem;\n    cursor: pointer;\n  }\n`\n\nconst Header: React.FC = () => {\n  const { user, logout } = useAuth()\n  const location = useLocation()\n\n  const isActive = (path: string) => location.pathname === path\n\n  return (\n    <HeaderContainer>\n      <Nav>\n        <Logo to=\"/\">\n          ⭐ 星光对对碰\n        </Logo>\n        \n        {user ? (\n          <>\n            <NavLinks>\n              <NavLink to=\"/\" $active={isActive('/')}>\n                首页\n              </NavLink>\n              <NavLink to=\"/game\" $active={isActive('/game')}>\n                游戏\n              </NavLink>\n              <NavLink to=\"/leaderboard\" $active={isActive('/leaderboard')}>\n                排行榜\n              </NavLink>\n              <NavLink to=\"/achievements\" $active={isActive('/achievements')}>\n                成就\n              </NavLink>\n              <NavLink to=\"/profile\" $active={isActive('/profile')}>\n                个人资料\n              </NavLink>\n            </NavLinks>\n            \n            <UserInfo>\n              <UserStats>\n                <span>Lv.{user.level}</span>\n                <span>💰{user.coins}</span>\n                <span>💎{user.gems}</span>\n              </UserStats>\n              <LogoutButton onClick={logout}>\n                退出\n              </LogoutButton>\n            </UserInfo>\n          </>\n        ) : (\n          <NavLinks>\n            <NavLink to=\"/login\" $active={isActive('/login')}>\n              登录\n            </NavLink>\n            <NavLink to=\"/register\" $active={isActive('/register')}>\n              注册\n            </NavLink>\n          </NavLinks>\n        )}\n        \n        <MobileMenu>\n          ☰\n        </MobileMenu>\n      </Nav>\n    </HeaderContainer>\n  )\n}\n\nexport default Header\n", "import React from 'react'\nimport styled from 'styled-components'\n\nconst FooterContainer = styled.footer`\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 2rem;\n  margin-top: auto;\n`\n\nconst FooterContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  text-align: center;\n  color: rgba(255, 255, 255, 0.8);\n`\n\nconst FooterText = styled.p`\n  margin: 0;\n  font-size: 0.9rem;\n  line-height: 1.6;\n`\n\nconst FooterLinks = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  margin-bottom: 1rem;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    gap: 1rem;\n  }\n`\n\nconst FooterLink = styled.a`\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: none;\n  font-size: 0.9rem;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: white;\n  }\n`\n\nconst Footer: React.FC = () => {\n  return (\n    <FooterContainer>\n      <FooterContent>\n        <FooterLinks>\n          <FooterLink href=\"#about\">关于我们</FooterLink>\n          <FooterLink href=\"#privacy\">隐私政策</FooterLink>\n          <FooterLink href=\"#terms\">服务条款</FooterLink>\n          <FooterLink href=\"#contact\">联系我们</FooterLink>\n        </FooterLinks>\n        <FooterText>\n          © 2024 星光对对碰. 保留所有权利. | 一款有趣的三消游戏\n        </FooterText>\n      </FooterContent>\n    </FooterContainer>\n  )\n}\n\nexport default Footer\n", "// 前端游戏引擎工具类\nexport enum GemType {\n  EMPTY = 0,\n  RED = 1,\n  BLUE = 2,\n  GREEN = 3,\n  YELLOW = 4,\n  PURPLE = 5,\n  ORANGE = 6,\n}\n\nexport interface Position {\n  row: number\n  col: number\n}\n\nexport interface Match {\n  positions: Position[]\n  type: 'horizontal' | 'vertical' | 'L' | 'T'\n  score: number\n}\n\nexport interface MoveResult {\n  isValid: boolean\n  matches: Match[]\n  newBoard: number[][]\n  score: number\n  cascade: boolean\n}\n\nexport class GameEngine {\n  private board: number[][]\n  private size: number\n\n  constructor(board: number[][], size: number = 6) {\n    this.board = board.map(row => [...row]) // 深拷贝\n    this.size = size\n  }\n\n  // 检查位置是否有效\n  private isValidPosition(row: number, col: number): boolean {\n    return row >= 0 && row < this.size && col >= 0 && col < this.size\n  }\n\n  // 检查两个位置是否相邻\n  public areAdjacent(pos1: Position, pos2: Position): boolean {\n    const rowDiff = Math.abs(pos1.row - pos2.row)\n    const colDiff = Math.abs(pos1.col - pos2.col)\n    return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)\n  }\n\n  // 获取当前棋盘状态\n  public getBoard(): number[][] {\n    return this.board.map(row => [...row])\n  }\n\n  // 交换两个位置的宝石\n  private swapGems(pos1: Position, pos2: Position): void {\n    const temp = this.board[pos1.row][pos1.col]\n    this.board[pos1.row][pos1.col] = this.board[pos2.row][pos2.col]\n    this.board[pos2.row][pos2.col] = temp\n  }\n\n  // 检查水平匹配\n  private findHorizontalMatches(): Match[] {\n    const matches: Match[] = []\n    \n    for (let row = 0; row < this.size; row++) {\n      let count = 1\n      let currentGem = this.board[row][0]\n      \n      for (let col = 1; col < this.size; col++) {\n        if (this.board[row][col] === currentGem && currentGem !== GemType.EMPTY) {\n          count++\n        } else {\n          if (count >= 3) {\n            const positions: Position[] = []\n            for (let i = col - count; i < col; i++) {\n              positions.push({ row, col: i })\n            }\n            matches.push({\n              positions,\n              type: 'horizontal',\n              score: count * 10\n            })\n          }\n          count = 1\n          currentGem = this.board[row][col]\n        }\n      }\n      \n      // 检查行末尾的匹配\n      if (count >= 3) {\n        const positions: Position[] = []\n        for (let i = this.size - count; i < this.size; i++) {\n          positions.push({ row, col: i })\n        }\n        matches.push({\n          positions,\n          type: 'horizontal',\n          score: count * 10\n        })\n      }\n    }\n    \n    return matches\n  }\n\n  // 检查垂直匹配\n  private findVerticalMatches(): Match[] {\n    const matches: Match[] = []\n    \n    for (let col = 0; col < this.size; col++) {\n      let count = 1\n      let currentGem = this.board[0][col]\n      \n      for (let row = 1; row < this.size; row++) {\n        if (this.board[row][col] === currentGem && currentGem !== GemType.EMPTY) {\n          count++\n        } else {\n          if (count >= 3) {\n            const positions: Position[] = []\n            for (let i = row - count; i < row; i++) {\n              positions.push({ row: i, col })\n            }\n            matches.push({\n              positions,\n              type: 'vertical',\n              score: count * 10\n            })\n          }\n          count = 1\n          currentGem = this.board[row][col]\n        }\n      }\n      \n      // 检查列末尾的匹配\n      if (count >= 3) {\n        const positions: Position[] = []\n        for (let i = this.size - count; i < this.size; i++) {\n          positions.push({ row: i, col })\n        }\n        matches.push({\n          positions,\n          type: 'vertical',\n          score: count * 10\n        })\n      }\n    }\n    \n    return matches\n  }\n\n  // 生成新的游戏板\n  public generateBoard(size: number = 6): number[][] {\n    const board: number[][] = []\n    for (let row = 0; row < size; row++) {\n      board[row] = []\n      for (let col = 0; col < size; col++) {\n        board[row][col] = Math.floor(Math.random() * 6) + 1\n      }\n    }\n    return board\n  }\n\n  // 检查移动是否有效\n  public isValidMove(board: number[][], from: Position, to: Position): boolean {\n    // 检查位置是否有效\n    if (!this.isValidPosition(from.row, from.col) || !this.isValidPosition(to.row, to.col)) {\n      return false\n    }\n\n    // 检查是否相邻\n    if (!this.areAdjacent(from, to)) {\n      return false\n    }\n\n    // 模拟交换并检查是否产生匹配\n    const tempBoard = board.map(row => [...row])\n    const temp = tempBoard[from.row][from.col]\n    tempBoard[from.row][from.col] = tempBoard[to.row][to.col]\n    tempBoard[to.row][to.col] = temp\n\n    // 临时设置board来检查匹配\n    const originalBoard = this.board\n    this.board = tempBoard\n    const matches = this.findMatches()\n    this.board = originalBoard\n\n    return matches.length > 0\n  }\n\n  // 查找所有匹配\n  public findMatches(): Match[] {\n    const horizontalMatches = this.findHorizontalMatches()\n    const verticalMatches = this.findVerticalMatches()\n    return [...horizontalMatches, ...verticalMatches]\n  }\n\n  // 移除匹配的宝石\n  private removeMatches(matches: Match[]): number {\n    let totalScore = 0\n    const toRemove = new Set<string>()\n    \n    matches.forEach(match => {\n      totalScore += match.score\n      match.positions.forEach(pos => {\n        toRemove.add(`${pos.row},${pos.col}`)\n      })\n    })\n    \n    toRemove.forEach(posStr => {\n      const [row, col] = posStr.split(',').map(Number)\n      this.board[row][col] = GemType.EMPTY\n    })\n    \n    return totalScore\n  }\n\n  // 应用重力，让宝石下落\n  private applyGravity(): boolean {\n    let hasChanges = false\n    \n    for (let col = 0; col < this.size; col++) {\n      // 从底部开始，将非空宝石向下移动\n      let writePos = this.size - 1\n      \n      for (let row = this.size - 1; row >= 0; row--) {\n        if (this.board[row][col] !== GemType.EMPTY) {\n          if (row !== writePos) {\n            this.board[writePos][col] = this.board[row][col]\n            this.board[row][col] = GemType.EMPTY\n            hasChanges = true\n          }\n          writePos--\n        }\n      }\n    }\n    \n    return hasChanges\n  }\n\n  // 填充空位置\n  private fillEmptySpaces(): void {\n    for (let col = 0; col < this.size; col++) {\n      for (let row = 0; row < this.size; row++) {\n        if (this.board[row][col] === GemType.EMPTY) {\n          // 生成随机宝石类型 (1-6)\n          this.board[row][col] = Math.floor(Math.random() * 6) + 1\n        }\n      }\n    }\n  }\n\n  // 执行移动\n  public makeMove(from: Position, to: Position): MoveResult {\n    // 检查位置有效性\n    if (!this.isValidPosition(from.row, from.col) || !this.isValidPosition(to.row, to.col)) {\n      return {\n        isValid: false,\n        matches: [],\n        newBoard: this.board,\n        score: 0,\n        cascade: false\n      }\n    }\n\n    // 检查是否相邻\n    if (!this.areAdjacent(from, to)) {\n      return {\n        isValid: false,\n        matches: [],\n        newBoard: this.board,\n        score: 0,\n        cascade: false\n      }\n    }\n\n    // 临时交换\n    this.swapGems(from, to)\n    \n    // 检查是否产生匹配\n    const matches = this.findMatches()\n    \n    if (matches.length === 0) {\n      // 没有匹配，撤销交换\n      this.swapGems(from, to)\n      return {\n        isValid: false,\n        matches: [],\n        newBoard: this.board,\n        score: 0,\n        cascade: false\n      }\n    }\n\n    // 处理连锁反应\n    let totalScore = 0\n    let allMatches: Match[] = []\n    let hasCascade = false\n\n    while (matches.length > 0) {\n      allMatches.push(...matches)\n      totalScore += this.removeMatches(matches)\n      \n      // 应用重力\n      this.applyGravity()\n      \n      // 填充空位\n      this.fillEmptySpaces()\n      \n      // 检查新的匹配\n      const newMatches = this.findMatches()\n      if (newMatches.length > 0) {\n        hasCascade = true\n        matches.splice(0, matches.length, ...newMatches)\n      } else {\n        break\n      }\n    }\n\n    return {\n      isValid: true,\n      matches: allMatches,\n      newBoard: this.board.map(row => [...row]),\n      score: totalScore,\n      cascade: hasCascade\n    }\n  }\n\n\n\n  // 检查是否有可能的移动\n  public hasPossibleMoves(): boolean {\n    for (let row = 0; row < this.size; row++) {\n      for (let col = 0; col < this.size; col++) {\n        // 检查右边的交换\n        if (col < this.size - 1) {\n          this.swapGems({ row, col }, { row, col: col + 1 })\n          if (this.findMatches().length > 0) {\n            this.swapGems({ row, col }, { row, col: col + 1 }) // 撤销\n            return true\n          }\n          this.swapGems({ row, col }, { row, col: col + 1 }) // 撤销\n        }\n\n        // 检查下面的交换\n        if (row < this.size - 1) {\n          this.swapGems({ row, col }, { row: row + 1, col })\n          if (this.findMatches().length > 0) {\n            this.swapGems({ row, col }, { row: row + 1, col }) // 撤销\n            return true\n          }\n          this.swapGems({ row, col }, { row: row + 1, col }) // 撤销\n        }\n      }\n    }\n    return false\n  }\n\n  // 生成提示\n  public getHint(): { from: Position; to: Position } | null {\n    for (let row = 0; row < this.size; row++) {\n      for (let col = 0; col < this.size; col++) {\n        // 检查右边的交换\n        if (col < this.size - 1) {\n          const from = { row, col }\n          const to = { row, col: col + 1 }\n          this.swapGems(from, to)\n          if (this.findMatches().length > 0) {\n            this.swapGems(from, to) // 撤销\n            return { from, to }\n          }\n          this.swapGems(from, to) // 撤销\n        }\n\n        // 检查下面的交换\n        if (row < this.size - 1) {\n          const from = { row, col }\n          const to = { row: row + 1, col }\n          this.swapGems(from, to)\n          if (this.findMatches().length > 0) {\n            this.swapGems(from, to) // 撤销\n            return { from, to }\n          }\n          this.swapGems(from, to) // 撤销\n        }\n      }\n    }\n    return null\n  }\n\n  // 计算匹配分数（考虑连击和匹配长度）\n  public calculateScore(matches: Match[], comboMultiplier: number = 1): number {\n    let totalScore = 0\n    matches.forEach(match => {\n      const baseScore = match.positions.length * 10\n      const lengthBonus = Math.max(0, (match.positions.length - 3) * 5)\n      totalScore += (baseScore + lengthBonus) * comboMultiplier\n    })\n    return totalScore\n  }\n\n  // 重新洗牌（当没有可能移动时）\n  public shuffle(): void {\n    const gems: number[] = []\n\n    // 收集所有非空宝石\n    for (let row = 0; row < this.size; row++) {\n      for (let col = 0; col < this.size; col++) {\n        if (this.board[row][col] !== GemType.EMPTY) {\n          gems.push(this.board[row][col])\n        }\n      }\n    }\n\n    // 洗牌\n    for (let i = gems.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1))\n      ;[gems[i], gems[j]] = [gems[j], gems[i]]\n    }\n\n    // 重新分配到棋盘\n    let gemIndex = 0\n    for (let row = 0; row < this.size; row++) {\n      for (let col = 0; col < this.size; col++) {\n        if (this.board[row][col] !== GemType.EMPTY) {\n          this.board[row][col] = gems[gemIndex++]\n        }\n      }\n    }\n  }\n\n  // 洗牌棋盘（返回新的棋盘）\n  public shuffleBoard(board: number[][]): number[][] {\n    const newBoard = board.map(row => [...row])\n    const originalBoard = this.board\n    this.board = newBoard\n    this.shuffle()\n    const result = this.board.map(row => [...row])\n    this.board = originalBoard\n    return result\n  }\n\n  // 应用重力（公共方法用于测试）\n  public applyGravityPublic(): boolean {\n    let changed = false\n    for (let col = 0; col < this.size; col++) {\n      let writeIndex = this.size - 1\n      for (let row = this.size - 1; row >= 0; row--) {\n        if (this.board[row][col] !== GemType.EMPTY) {\n          if (writeIndex !== row) {\n            this.board[writeIndex][col] = this.board[row][col]\n            this.board[row][col] = GemType.EMPTY\n            changed = true\n          }\n          writeIndex--\n        }\n      }\n    }\n    return changed\n  }\n\n  // 获取提示（带参数版本用于测试）\n  public getHintWithBoard(board?: number[][]): { from: Position; to: Position } | null {\n    if (board) {\n      const originalBoard = this.board\n      this.board = board\n      const result = this.getHint()\n      this.board = originalBoard\n      return result\n    }\n    return this.getHint()\n  }\n}\n", "import React, { createContext, useContext, useReducer, useState } from 'react'\nimport { useQuery, useMutation } from 'react-query'\nimport toast from 'react-hot-toast'\nimport { GameEngine } from '../utils/gameEngine'\n\n// Mock data for development\nconst mockLevel = {\n  id: 1,\n  name: '第一关',\n  difficulty: 'easy' as const,\n  targetScore: 1000,\n  maxMoves: 20,\n  boardSize: 6,\n  objectives: [\n    { type: 'score' as const, target: 1000, description: '达到目标分数' },\n    { type: 'collect_items' as const, target: 10, description: '收集星星' }\n  ]\n}\n\nconst generateMockBoard = (size: number = 6): number[][] => {\n  const board: number[][] = []\n  for (let i = 0; i < size; i++) {\n    board[i] = []\n    for (let j = 0; j < size; j++) {\n      board[i][j] = Math.floor(Math.random() * 6) + 1 // 1-6 different gem types\n    }\n  }\n  return board\n}\n\n// 游戏状态类型定义\nexport interface GameState {\n  board: number[][]\n  score: number\n  moves: number\n  maxMoves: number\n  movesLeft: number\n  level: number\n  currentLevel?: number\n  targetScore?: number\n  status: 'idle' | 'loading' | 'playing' | 'won' | 'lost' | 'paused'\n  gameStatus: 'playing' | 'won' | 'lost' | 'paused'\n  selectedCell: { row: number; col: number } | null\n  combo: number\n  timeLeft?: number\n  powerUps: {\n    hammer: number\n    bomb: number\n    shuffle: number\n    extraMoves: number\n  }\n}\n\n// 游戏动作类型\ntype GameAction =\n  | { type: 'INIT_GAME'; payload: { level: number; board: number[][]; maxMoves: number; targetScore?: number } }\n  | { type: 'SELECT_CELL'; payload: { row: number; col: number } }\n  | { type: 'CLEAR_SELECTION' }\n  | { type: 'MAKE_MOVE'; payload: { from: { row: number; col: number }; to: { row: number; col: number }; score?: number } }\n  | { type: 'UPDATE_BOARD'; payload: { board: number[][]; score: number; combo: number } }\n  | { type: 'SET_GAME_STATUS'; payload: 'playing' | 'won' | 'lost' | 'paused' }\n  | { type: 'SET_STATUS'; payload: 'idle' | 'loading' | 'playing' | 'won' | 'lost' | 'paused' }\n  | { type: 'USE_POWERUP'; payload: { type: keyof GameState['powerUps']; position?: { row: number; col: number } } }\n  | { type: 'UPDATE_TIME'; payload: number }\n  | { type: 'RESET_GAME' }\n\n// 关卡信息类型\ninterface LevelInfo {\n  id: number\n  name: string\n  description: string\n  boardSize: { rows: number; cols: number }\n  maxMoves: number\n  targetScore: number\n  timeLimit?: number\n  objectives: {\n    type: 'score' | 'clear_color' | 'collect_items'\n    target: number\n    description: string\n  }[]\n  powerUps: string[]\n  difficulty: 'easy' | 'medium' | 'hard'\n}\n\n// Context 类型定义\ninterface GameContextType {\n  gameState: GameState\n  currentLevel: LevelInfo | null\n  loading: boolean\n  dispatch: React.Dispatch<GameAction>\n  startGame: (levelId: number) => Promise<void>\n  makeMove: (from: { row: number; col: number }, to: { row: number; col: number }, onMatchFound?: (matches: any[]) => void) => Promise<void>\n  usePowerUp: (type: keyof GameState['powerUps'], position?: { row: number; col: number }) => Promise<void>\n  pauseGame: () => void\n  resumeGame: () => void\n  resetGame: () => void\n  submitScore: () => Promise<void>\n}\n\n// 初始游戏状态\nconst initialGameState: GameState = {\n  board: [],\n  score: 0,\n  moves: 0,\n  maxMoves: 30,\n  movesLeft: 30,\n  level: 1,\n  currentLevel: 1,\n  targetScore: 1000,\n  status: 'idle',\n  gameStatus: 'playing',\n  selectedCell: null,\n  combo: 0,\n  powerUps: {\n    hammer: 3,\n    bomb: 2,\n    shuffle: 1,\n    extraMoves: 1,\n  },\n}\n\n// 游戏状态 reducer\nconst gameReducer = (state: GameState, action: GameAction): GameState => {\n  switch (action.type) {\n    case 'INIT_GAME':\n      return {\n        ...initialGameState,\n        board: action.payload.board,\n        level: action.payload.level,\n        currentLevel: action.payload.level,\n        maxMoves: action.payload.maxMoves,\n        movesLeft: action.payload.maxMoves,\n        targetScore: action.payload.targetScore || 1000,\n        status: 'playing',\n        gameStatus: 'playing',\n      }\n\n    case 'SELECT_CELL':\n      return {\n        ...state,\n        selectedCell: action.payload,\n      }\n\n    case 'CLEAR_SELECTION':\n      return {\n        ...state,\n        selectedCell: null,\n      }\n\n    case 'MAKE_MOVE':\n      return {\n        ...state,\n        moves: state.moves + 1,\n        movesLeft: state.movesLeft - 1,\n        selectedCell: null,\n      }\n\n    case 'UPDATE_BOARD':\n      return {\n        ...state,\n        board: action.payload.board,\n        score: state.score + action.payload.score,\n        combo: action.payload.combo,\n      }\n\n    case 'SET_GAME_STATUS':\n      return {\n        ...state,\n        gameStatus: action.payload,\n        status: action.payload === 'playing' ? 'playing' : action.payload === 'won' ? 'won' : action.payload === 'lost' ? 'lost' : 'paused',\n      }\n\n    case 'SET_STATUS':\n      return {\n        ...state,\n        status: action.payload,\n        gameStatus: action.payload === 'playing' ? 'playing' : action.payload === 'won' ? 'won' : action.payload === 'lost' ? 'lost' : 'playing',\n      }\n\n    case 'USE_POWERUP':\n      const powerUpType = action.payload.type\n      if (state.powerUps[powerUpType] > 0) {\n        return {\n          ...state,\n          powerUps: {\n            ...state.powerUps,\n            [powerUpType]: state.powerUps[powerUpType] - 1,\n          },\n        }\n      }\n      return state\n\n    case 'UPDATE_TIME':\n      return {\n        ...state,\n        timeLeft: action.payload,\n      }\n\n    case 'RESET_GAME':\n      return initialGameState\n\n    default:\n      return state\n  }\n}\n\n// API 函数\nconst gameAPI = {\n  // 获取关卡信息\n  getLevel: async (levelId: number): Promise<LevelInfo> => {\n    try {\n      const token = localStorage.getItem('token')\n      const response = await fetch(`/api/levels/${levelId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to get level info')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock level data')\n      return {\n        ...mockLevel,\n        id: levelId,\n        description: `关卡 ${levelId} - 挑战你的技巧`,\n        powerUps: ['hammer', 'bomb', 'shuffle'],\n        boardSize: { rows: mockLevel.boardSize, cols: mockLevel.boardSize }\n      }\n    }\n  },\n\n  // 开始游戏\n  startGame: async (levelId: number): Promise<{ board: number[][]; maxMoves: number }> => {\n    try {\n      const token = localStorage.getItem('token')\n      const response = await fetch('/api/game/start', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ levelId }),\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to start game')\n      }\n\n      return response.json()\n    } catch (error) {\n      // Mock response when backend is not available\n      console.warn('Backend not available, using mock game data')\n      return {\n        board: generateMockBoard(6),\n        maxMoves: 20\n      }\n    }\n  },\n\n  // 执行移动\n  makeMove: async (gameId: string, from: { row: number; col: number }, to: { row: number; col: number }) => {\n    const token = localStorage.getItem('token')\n    const response = await fetch('/api/game/move', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ gameId, from, to }),\n    })\n\n    if (!response.ok) {\n      throw new Error('Invalid move')\n    }\n\n    return response.json()\n  },\n\n  // 使用道具\n  usePowerUp: async (gameId: string, type: string, position?: { row: number; col: number }) => {\n    const token = localStorage.getItem('token')\n    const response = await fetch('/api/game/powerup', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ gameId, type, position }),\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to use power-up')\n    }\n\n    return response.json()\n  },\n\n  // 提交分数\n  submitScore: async (gameId: string, score: number, moves: number) => {\n    const token = localStorage.getItem('token')\n    const response = await fetch('/api/game/submit', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ gameId, score, moves }),\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to submit score')\n    }\n\n    return response.json()\n  },\n}\n\n// 创建 Context\nconst GameContext = createContext<GameContextType | undefined>(undefined)\n\n// GameProvider 组件\nexport const GameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [gameState, dispatch] = useReducer(gameReducer, initialGameState)\n  const [currentGameId, setCurrentGameId] = useState<string | null>(null)\n  // const queryClient = useQueryClient()\n\n  // 获取当前关卡信息\n  const { data: currentLevel, isLoading } = useQuery(\n    ['level', gameState.level],\n    () => gameAPI.getLevel(gameState.level),\n    {\n      enabled: gameState.level > 0,\n      retry: false,\n    }\n  )\n\n  // 开始游戏 mutation\n  const startGameMutation = useMutation(gameAPI.startGame, {\n    onSuccess: (data, levelId) => {\n      dispatch({\n        type: 'INIT_GAME',\n        payload: {\n          level: levelId,\n          board: data.board,\n          maxMoves: data.maxMoves,\n        },\n      })\n      setCurrentGameId('mock-game-id')\n      toast.success('游戏开始！')\n    },\n    onError: (error: Error) => {\n      toast.error(error.message || '游戏启动失败')\n    },\n  })\n\n  // 执行移动 mutation\n  // const makeMoveMutation = useMutation(\n  //   ({ from, to }: { from: { row: number; col: number }; to: { row: number; col: number } }) =>\n  //     gameAPI.makeMove(currentGameId!, from, to),\n  //   {\n  //     onSuccess: (data) => {\n  //       dispatch({ type: 'MAKE_MOVE', payload: data.move })\n  //       dispatch({\n  //         type: 'UPDATE_BOARD',\n  //         payload: {\n  //           board: data.board,\n  //           score: data.scoreGained,\n  //           combo: data.combo,\n  //         },\n  //       })\n\n  //       if (data.gameStatus) {\n  //         dispatch({ type: 'SET_GAME_STATUS', payload: data.gameStatus })\n  //       }\n  //     },\n  //     onError: (error: Error) => {\n  //       toast.error(error.message || '无效移动')\n  //     },\n  //   }\n  // )\n\n  // Context 方法实现\n  const startGame = async (levelId: number) => {\n    await startGameMutation.mutateAsync(levelId)\n  }\n\n  const makeMove = async (from: { row: number; col: number }, to: { row: number; col: number }, onMatchFound?: (matches: any[]) => void) => {\n    if (!gameState.board) return\n\n    // 使用前端游戏引擎处理移动\n    const engine = new GameEngine(gameState.board, 6)\n    const result = engine.makeMove(from, to)\n\n    if (result.isValid) {\n      // 如果有匹配回调，先调用它\n      if (onMatchFound && result.matches && result.matches.length > 0) {\n        onMatchFound(result.matches)\n      }\n\n      // 更新游戏状态\n      dispatch({\n        type: 'UPDATE_BOARD',\n        payload: {\n          board: result.newBoard,\n          score: result.score,\n          combo: result.cascade ? 1 : 0,\n        },\n      })\n\n      dispatch({ type: 'MAKE_MOVE', payload: { from, to, score: result.score } })\n\n      // 检查游戏结束条件\n      if (gameState.movesLeft <= 1) {\n        if (gameState.score >= (gameState.targetScore || 0)) {\n          dispatch({ type: 'SET_GAME_STATUS', payload: 'won' })\n          toast.success('恭喜过关！')\n        } else {\n          dispatch({ type: 'SET_GAME_STATUS', payload: 'lost' })\n          toast.error('游戏结束')\n        }\n      }\n\n      if (result.cascade) {\n        toast.success('连击！')\n      }\n    } else {\n      toast.error('无效移动')\n    }\n  }\n\n  const usePowerUp = async (type: keyof GameState['powerUps'], position?: { row: number; col: number }) => {\n    if (!currentGameId) return\n    \n    try {\n      const result = await gameAPI.usePowerUp(currentGameId, type, position)\n      dispatch({ type: 'USE_POWERUP', payload: { type, position } })\n      dispatch({\n        type: 'UPDATE_BOARD',\n        payload: {\n          board: result.board,\n          score: result.scoreGained || 0,\n          combo: 0,\n        },\n      })\n      toast.success('道具使用成功！')\n    } catch (error) {\n      toast.error('道具使用失败')\n    }\n  }\n\n  const pauseGame = () => {\n    dispatch({ type: 'SET_GAME_STATUS', payload: 'paused' })\n  }\n\n  const resumeGame = () => {\n    dispatch({ type: 'SET_GAME_STATUS', payload: 'playing' })\n  }\n\n  const resetGame = () => {\n    dispatch({ type: 'RESET_GAME' })\n    setCurrentGameId(null)\n  }\n\n  const submitScore = async () => {\n    if (!currentGameId) return\n    \n    try {\n      await gameAPI.submitScore(currentGameId, gameState.score, gameState.moves)\n      toast.success('分数提交成功！')\n    } catch (error) {\n      toast.error('分数提交失败')\n    }\n  }\n\n  const value: GameContextType = {\n    gameState,\n    currentLevel: currentLevel || null,\n    loading: isLoading,\n    dispatch,\n    startGame,\n    makeMove,\n    usePowerUp,\n    pauseGame,\n    resumeGame,\n    resetGame,\n    submitScore,\n  }\n\n  return <GameContext.Provider value={value}>{children}</GameContext.Provider>\n}\n\n// useGame hook\nexport const useGame = (): GameContextType => {\n  const context = useContext(GameContext)\n  if (context === undefined) {\n    throw new Error('useGame must be used within a GameProvider')\n  }\n  return context\n}\n", "export interface AudioSettings {\n  masterVolume: number\n  musicVolume: number\n  sfxVolume: number\n  muted: boolean\n}\n\nexport type SoundEffect = \n  | 'gem_select'\n  | 'gem_swap'\n  | 'gem_match'\n  | 'gem_cascade'\n  | 'level_complete'\n  | 'level_failed'\n  | 'button_click'\n  | 'power_up'\n\nexport class AudioManager {\n  private audioContext: AudioContext | null = null\n  private musicAudio: HTMLAudioElement | null = null\n  private soundEffects: Map<SoundEffect, HTMLAudioElement> = new Map()\n  private settings: AudioSettings = {\n    masterVolume: 0.7,\n    musicVolume: 0.5,\n    sfxVolume: 0.8,\n    muted: false\n  }\n\n  constructor() {\n    this.initializeAudio()\n    this.loadSettings()\n  }\n\n  // 初始化音频系统\n  private initializeAudio(): void {\n    try {\n      // 创建音频上下文（用于Web Audio API）\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()\n      \n      // 创建背景音乐音频元素\n      this.musicAudio = new Audio()\n      this.musicAudio.loop = true\n      this.musicAudio.preload = 'auto'\n      \n      // 预加载音效\n      this.preloadSoundEffects()\n      \n    } catch (error) {\n      console.warn('Audio initialization failed:', error)\n    }\n  }\n\n  // 预加载音效\n  private preloadSoundEffects(): void {\n    const soundEffectUrls: Record<SoundEffect, string> = {\n      gem_select: this.generateTone(440, 0.1), // A4 音符\n      gem_swap: this.generateTone(523, 0.2), // C5 音符\n      gem_match: this.generateChord([523, 659, 784], 0.3), // C大调和弦\n      gem_cascade: this.generateArpeggio([523, 659, 784, 1047], 0.5), // 琶音\n      level_complete: this.generateMelody([523, 659, 784, 1047, 1319], 0.8), // 胜利旋律\n      level_failed: this.generateTone(220, 0.5), // 低音A\n      button_click: this.generateTone(800, 0.1), // 高音点击\n      power_up: this.generateSweep(400, 800, 0.3) // 频率扫描\n    }\n\n    Object.entries(soundEffectUrls).forEach(([effect, url]) => {\n      const audio = new Audio(url)\n      audio.preload = 'auto'\n      this.soundEffects.set(effect as SoundEffect, audio)\n    })\n  }\n\n  // 生成单音调\n  private generateTone(frequency: number, duration: number): string {\n    if (!this.audioContext) return ''\n    \n    const sampleRate = this.audioContext.sampleRate\n    const numSamples = sampleRate * duration\n    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)\n    const channelData = buffer.getChannelData(0)\n\n    for (let i = 0; i < numSamples; i++) {\n      const t = i / sampleRate\n      channelData[i] = Math.sin(2 * Math.PI * frequency * t) * Math.exp(-t * 3)\n    }\n\n    return this.bufferToDataUrl(buffer)\n  }\n\n  // 生成和弦\n  private generateChord(frequencies: number[], duration: number): string {\n    if (!this.audioContext) return ''\n    \n    const sampleRate = this.audioContext.sampleRate\n    const numSamples = sampleRate * duration\n    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)\n    const channelData = buffer.getChannelData(0)\n\n    for (let i = 0; i < numSamples; i++) {\n      const t = i / sampleRate\n      let sample = 0\n      frequencies.forEach(freq => {\n        sample += Math.sin(2 * Math.PI * freq * t) / frequencies.length\n      })\n      channelData[i] = sample * Math.exp(-t * 2)\n    }\n\n    return this.bufferToDataUrl(buffer)\n  }\n\n  // 生成琶音\n  private generateArpeggio(frequencies: number[], duration: number): string {\n    if (!this.audioContext) return ''\n    \n    const sampleRate = this.audioContext.sampleRate\n    const numSamples = sampleRate * duration\n    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)\n    const channelData = buffer.getChannelData(0)\n    const noteLength = duration / frequencies.length\n\n    for (let i = 0; i < numSamples; i++) {\n      const t = i / sampleRate\n      const noteIndex = Math.floor(t / noteLength)\n      const noteTime = t - noteIndex * noteLength\n      \n      if (noteIndex < frequencies.length) {\n        const freq = frequencies[noteIndex]\n        channelData[i] = Math.sin(2 * Math.PI * freq * noteTime) * Math.exp(-noteTime * 4)\n      }\n    }\n\n    return this.bufferToDataUrl(buffer)\n  }\n\n  // 生成旋律\n  private generateMelody(frequencies: number[], duration: number): string {\n    if (!this.audioContext) return ''\n    \n    const sampleRate = this.audioContext.sampleRate\n    const numSamples = sampleRate * duration\n    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)\n    const channelData = buffer.getChannelData(0)\n    const noteLength = duration / frequencies.length\n\n    for (let i = 0; i < numSamples; i++) {\n      const t = i / sampleRate\n      const noteIndex = Math.floor(t / noteLength)\n      const noteTime = t - noteIndex * noteLength\n      \n      if (noteIndex < frequencies.length) {\n        const freq = frequencies[noteIndex]\n        const envelope = Math.exp(-noteTime * 3)\n        channelData[i] = Math.sin(2 * Math.PI * freq * noteTime) * envelope\n      }\n    }\n\n    return this.bufferToDataUrl(buffer)\n  }\n\n  // 生成频率扫描\n  private generateSweep(startFreq: number, endFreq: number, duration: number): string {\n    if (!this.audioContext) return ''\n    \n    const sampleRate = this.audioContext.sampleRate\n    const numSamples = sampleRate * duration\n    const buffer = this.audioContext.createBuffer(1, numSamples, sampleRate)\n    const channelData = buffer.getChannelData(0)\n\n    for (let i = 0; i < numSamples; i++) {\n      const t = i / sampleRate\n      const progress = t / duration\n      const freq = startFreq + (endFreq - startFreq) * progress\n      channelData[i] = Math.sin(2 * Math.PI * freq * t) * (1 - progress)\n    }\n\n    return this.bufferToDataUrl(buffer)\n  }\n\n  // 将音频缓冲区转换为Data URL\n  private bufferToDataUrl(buffer: AudioBuffer): string {\n    const length = buffer.length\n    const channelData = buffer.getChannelData(0)\n    const samples = new Int16Array(length)\n    \n    for (let i = 0; i < length; i++) {\n      samples[i] = channelData[i] * 32767\n    }\n    \n    // 创建WAV文件头\n    const wavHeader = new ArrayBuffer(44)\n    const view = new DataView(wavHeader)\n    \n    // WAV文件格式头\n    const writeString = (offset: number, string: string) => {\n      for (let i = 0; i < string.length; i++) {\n        view.setUint8(offset + i, string.charCodeAt(i))\n      }\n    }\n    \n    writeString(0, 'RIFF')\n    view.setUint32(4, 36 + samples.length * 2, true)\n    writeString(8, 'WAVE')\n    writeString(12, 'fmt ')\n    view.setUint32(16, 16, true)\n    view.setUint16(20, 1, true)\n    view.setUint16(22, 1, true)\n    view.setUint32(24, buffer.sampleRate, true)\n    view.setUint32(28, buffer.sampleRate * 2, true)\n    view.setUint16(32, 2, true)\n    view.setUint16(34, 16, true)\n    writeString(36, 'data')\n    view.setUint32(40, samples.length * 2, true)\n    \n    // 合并头部和数据\n    const wavFile = new Uint8Array(44 + samples.length * 2)\n    wavFile.set(new Uint8Array(wavHeader), 0)\n    wavFile.set(new Uint8Array(samples.buffer), 44)\n    \n    const blob = new Blob([wavFile], { type: 'audio/wav' })\n    return URL.createObjectURL(blob)\n  }\n\n  // 播放音效\n  public playSoundEffect(effect: SoundEffect): void {\n    if (this.settings.muted) return\n    \n    const audio = this.soundEffects.get(effect)\n    if (audio) {\n      audio.volume = this.settings.sfxVolume * this.settings.masterVolume\n      audio.currentTime = 0\n      audio.play().catch(error => {\n        console.warn(`Failed to play sound effect ${effect}:`, error)\n      })\n    }\n  }\n\n  // 播放背景音乐\n  public playBackgroundMusic(url?: string): void {\n    if (this.settings.muted || !this.musicAudio) return\n    \n    if (url && this.musicAudio.src !== url) {\n      this.musicAudio.src = url\n    }\n    \n    this.musicAudio.volume = this.settings.musicVolume * this.settings.masterVolume\n    this.musicAudio.play().catch(error => {\n      console.warn('Failed to play background music:', error)\n    })\n  }\n\n  // 停止背景音乐\n  public stopBackgroundMusic(): void {\n    if (this.musicAudio) {\n      this.musicAudio.pause()\n      this.musicAudio.currentTime = 0\n    }\n  }\n\n  // 设置音量\n  public setVolume(type: keyof AudioSettings, volume: number): void {\n    (this.settings as any)[type] = Math.max(0, Math.min(1, volume))\n    this.saveSettings()\n    \n    // 更新当前播放的音频音量\n    if (this.musicAudio && type === 'musicVolume') {\n      this.musicAudio.volume = this.settings.musicVolume * this.settings.masterVolume\n    }\n  }\n\n  // 切换静音\n  public toggleMute(): void {\n    this.settings.muted = !this.settings.muted\n    this.saveSettings()\n    \n    if (this.settings.muted) {\n      this.stopBackgroundMusic()\n    }\n  }\n\n  // 获取设置\n  public getSettings(): AudioSettings {\n    return { ...this.settings }\n  }\n\n  // 保存设置到本地存储\n  private saveSettings(): void {\n    localStorage.setItem('audioSettings', JSON.stringify(this.settings))\n  }\n\n  // 从本地存储加载设置\n  private loadSettings(): void {\n    const saved = localStorage.getItem('audioSettings')\n    if (saved) {\n      try {\n        this.settings = { ...this.settings, ...JSON.parse(saved) }\n      } catch (error) {\n        console.warn('Failed to load audio settings:', error)\n      }\n    }\n  }\n\n  // 清理资源\n  public dispose(): void {\n    this.stopBackgroundMusic()\n    \n    // 清理音效\n    this.soundEffects.forEach(audio => {\n      if (audio.src.startsWith('blob:')) {\n        URL.revokeObjectURL(audio.src)\n      }\n    })\n    this.soundEffects.clear()\n    \n    // 关闭音频上下文\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close()\n    }\n  }\n}\n", "import React, { createContext, useContext, useEffect, useRef, ReactNode } from 'react'\nimport { AudioManager, SoundEffect } from '../utils/audioManager'\n\ninterface AudioContextType {\n  audioManager: AudioManager\n  playSound: (effect: SoundEffect) => void\n  playBackgroundMusic: (url?: string) => void\n  stopBackgroundMusic: () => void\n}\n\nconst AudioContext = createContext<AudioContextType | null>(null)\n\nexport const useAudio = (): AudioContextType => {\n  const context = useContext(AudioContext)\n  if (!context) {\n    throw new Error('useAudio must be used within an AudioProvider')\n  }\n  return context\n}\n\ninterface AudioProviderProps {\n  children: ReactNode\n}\n\nexport const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {\n  const audioManagerRef = useRef<AudioManager | null>(null)\n\n  useEffect(() => {\n    // 初始化音频管理器\n    audioManagerRef.current = new AudioManager()\n\n    // 清理函数\n    return () => {\n      if (audioManagerRef.current) {\n        audioManagerRef.current.dispose()\n      }\n    }\n  }, [])\n\n  const playSound = (effect: SoundEffect) => {\n    if (audioManagerRef.current) {\n      audioManagerRef.current.playSoundEffect(effect)\n    }\n  }\n\n  const playBackgroundMusic = (url?: string) => {\n    if (audioManagerRef.current) {\n      audioManagerRef.current.playBackgroundMusic(url)\n    }\n  }\n\n  const stopBackgroundMusic = () => {\n    if (audioManagerRef.current) {\n      audioManagerRef.current.stopBackgroundMusic()\n    }\n  }\n\n  const value: AudioContextType = {\n    audioManager: audioManagerRef.current!,\n    playSound,\n    playBackgroundMusic,\n    stopBackgroundMusic,\n  }\n\n  return <AudioContext.Provider value={value}>{children}</AudioContext.Provider>\n}\n\n// 音频钩子，用于在游戏事件中播放音效\nexport const useGameAudio = () => {\n  const { playSound } = useAudio()\n\n  return {\n    onGemSelect: () => playSound('gem_select'),\n    onGemSwap: () => playSound('gem_swap'),\n    onGemMatch: () => playSound('gem_match'),\n    onGemCascade: () => playSound('gem_cascade'),\n    onLevelComplete: () => playSound('level_complete'),\n    onLevelFailed: () => playSound('level_failed'),\n    onButtonClick: () => playSound('button_click'),\n    onPowerUp: () => playSound('power_up'),\n  }\n}\n", "import React, { Suspense, useEffect, useState } from 'react'\nimport { Routes, Route, Navigate } from 'react-router-dom'\nimport styled from 'styled-components'\n\n// 页面组件 - 立即加载的核心页面\nimport HomePage from './pages/HomePage'\nimport LoginPage from './pages/LoginPage'\nimport RegisterPage from './pages/RegisterPage'\n\n// 懒加载的页面组件\nimport {\n  LazyGamePage,\n  LazyLevelsPage,\n  LazyLeaderboardPage,\n  LazyAchievementsPage,\n  LazyProfilePage,\n  ComponentPreloader\n} from './utils/lazyComponents'\nimport PerformancePanel from './components/Debug/PerformancePanel'\n\n// 布局组件\nimport Header from './components/Layout/Header'\nimport Footer from './components/Layout/Footer'\n\n// Context\nimport { AuthProvider, useAuth } from './contexts/AuthContext'\nimport { GameProvider } from './contexts/GameContext'\nimport { AudioProvider } from './contexts/AudioContext'\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`\n\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`\n\nconst LoadingScreen = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-size: 1.5rem;\n  font-weight: 600;\n`\n\nconst SuspenseLoader = styled.div`\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: white;\n\n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n    border-top: 4px solid white;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n    margin-bottom: 16px;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`\n\n// 受保护的路由组件\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <LoadingScreen>\n        <div>加载中...</div>\n      </LoadingScreen>\n    )\n  }\n\n  if (!user) {\n    return <Navigate to=\"/login\" replace />\n  }\n\n  return <>{children}</>\n}\n\n// 公开路由组件（已登录用户重定向到首页）\nconst PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <LoadingScreen>\n        <div>加载中...</div>\n      </LoadingScreen>\n    )\n  }\n\n  if (user) {\n    return <Navigate to=\"/\" replace />\n  }\n\n  return <>{children}</>\n}\n\n// 主应用内容组件\nconst AppContent: React.FC = () => {\n  const [showPerformancePanel, setShowPerformancePanel] = useState(false)\n\n  return (\n    <AppContainer>\n      <Header />\n      <MainContent>\n        <Routes>\n          {/* 公开路由 */}\n          <Route\n            path=\"/login\"\n            element={\n              <PublicRoute>\n                <LoginPage />\n              </PublicRoute>\n            }\n          />\n          <Route\n            path=\"/register\"\n            element={\n              <PublicRoute>\n                <RegisterPage />\n              </PublicRoute>\n            }\n          />\n\n          {/* 受保护的路由 */}\n          <Route\n            path=\"/\"\n            element={\n              <ProtectedRoute>\n                <HomePage />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/levels\"\n            element={\n              <ProtectedRoute>\n                <Suspense fallback={<SuspenseLoader><div className=\"spinner\"></div><div>加载关卡...</div></SuspenseLoader>}>\n                  <LazyLevelsPage />\n                </Suspense>\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/game\"\n            element={\n              <ProtectedRoute>\n                <Suspense fallback={<SuspenseLoader><div className=\"spinner\"></div><div>加载游戏...</div></SuspenseLoader>}>\n                  <LazyGamePage />\n                </Suspense>\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/profile\"\n            element={\n              <ProtectedRoute>\n                <Suspense fallback={<SuspenseLoader><div className=\"spinner\"></div><div>加载个人资料...</div></SuspenseLoader>}>\n                  <LazyProfilePage />\n                </Suspense>\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/leaderboard\"\n            element={\n              <ProtectedRoute>\n                <Suspense fallback={<SuspenseLoader><div className=\"spinner\"></div><div>加载排行榜...</div></SuspenseLoader>}>\n                  <LazyLeaderboardPage />\n                </Suspense>\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/achievements\"\n            element={\n              <ProtectedRoute>\n                <Suspense fallback={<SuspenseLoader><div className=\"spinner\"></div><div>加载成就...</div></SuspenseLoader>}>\n                  <LazyAchievementsPage />\n                </Suspense>\n              </ProtectedRoute>\n            }\n          />\n\n          {/* 404 重定向 */}\n          <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n        </Routes>\n      </MainContent>\n      <Footer />\n\n      {/* 开发环境性能监控面板 */}\n      {process.env.NODE_ENV === 'development' && (\n        <PerformancePanel\n          isVisible={showPerformancePanel}\n          onToggle={() => setShowPerformancePanel(!showPerformancePanel)}\n        />\n      )}\n    </AppContainer>\n  )\n}\n\nconst App: React.FC = () => {\n  // 组件预加载\n  useEffect(() => {\n    // 在空闲时预加载组件\n    ComponentPreloader.preloadOnIdle()\n\n    // 在用户交互时预加载游戏组件\n    ComponentPreloader.preloadOnUserInteraction()\n\n    // 清理函数\n    return () => {\n      ComponentPreloader.clearPreloadedComponents()\n    }\n  }, [])\n\n  return (\n    <AuthProvider>\n      <AudioProvider>\n        <GameProvider>\n          <AppContent />\n        </GameProvider>\n      </AudioProvider>\n    </AuthProvider>\n  )\n}\n\nexport default App\n", "export const theme = {\n  colors: {\n    // 主色调 - 星空主题\n    primary: {\n      50: '#eef2ff',\n      100: '#e0e7ff',\n      200: '#c7d2fe',\n      300: '#a5b4fc',\n      400: '#818cf8',\n      500: '#6366f1',\n      600: '#4f46e5',\n      700: '#4338ca',\n      800: '#3730a3',\n      900: '#312e81',\n    },\n    \n    // 辅助色 - 星光色彩\n    secondary: {\n      50: '#fefce8',\n      100: '#fef9c3',\n      200: '#fef08a',\n      300: '#fde047',\n      400: '#facc15',\n      500: '#eab308',\n      600: '#ca8a04',\n      700: '#a16207',\n      800: '#854d0e',\n      900: '#713f12',\n    },\n    \n    // 宝石颜色\n    gems: {\n      red: '#ef4444',\n      blue: '#3b82f6',\n      green: '#10b981',\n      yellow: '#f59e0b',\n      purple: '#8b5cf6',\n      orange: '#f97316',\n    },\n    \n    // 中性色\n    gray: {\n      50: '#f9fafb',\n      100: '#f3f4f6',\n      200: '#e5e7eb',\n      300: '#d1d5db',\n      400: '#9ca3af',\n      500: '#6b7280',\n      600: '#4b5563',\n      700: '#374151',\n      800: '#1f2937',\n      900: '#111827',\n    },\n    \n    // 状态色\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n    info: '#3b82f6',\n    \n    // 背景色\n    background: {\n      primary: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',\n      secondary: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',\n      card: 'rgba(255, 255, 255, 0.1)',\n      cardHover: 'rgba(255, 255, 255, 0.15)',\n      overlay: 'rgba(0, 0, 0, 0.8)',\n      glass: 'rgba(255, 255, 255, 0.05)',\n    },\n    \n    // 文字色\n    text: {\n      primary: '#f9fafb',\n      secondary: '#d1d5db',\n      muted: '#9ca3af',\n      inverse: '#111827',\n    },\n  },\n  \n  // 字体\n  fonts: {\n    primary: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif\",\n    mono: \"'JetBrains Mono', 'Fira Code', monospace\",\n  },\n  \n  // 字体大小\n  fontSizes: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem',\n    '4xl': '2.25rem',\n    '5xl': '3rem',\n    '6xl': '3.75rem',\n  },\n  \n  // 字重\n  fontWeights: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700,\n  },\n  \n  // 间距\n  spacing: {\n    0: '0',\n    1: '0.25rem',\n    2: '0.5rem',\n    3: '0.75rem',\n    4: '1rem',\n    5: '1.25rem',\n    6: '1.5rem',\n    8: '2rem',\n    10: '2.5rem',\n    12: '3rem',\n    16: '4rem',\n    20: '5rem',\n    24: '6rem',\n    32: '8rem',\n    40: '10rem',\n    48: '12rem',\n    56: '14rem',\n    64: '16rem',\n  },\n  \n  // 圆角\n  borderRadius: {\n    none: '0',\n    sm: '0.125rem',\n    base: '0.25rem',\n    md: '0.375rem',\n    lg: '0.5rem',\n    xl: '0.75rem',\n    '2xl': '1rem',\n    '3xl': '1.5rem',\n    full: '9999px',\n  },\n  \n  // 阴影\n  shadows: {\n    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n    glow: '0 0 20px rgba(99, 102, 241, 0.5)',\n    glowGold: '0 0 20px rgba(251, 191, 36, 0.4)',\n    glowPurple: '0 0 20px rgba(139, 92, 246, 0.4)',\n    gemGlow: '0 0 15px rgba(251, 191, 36, 0.6)',\n    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',\n    text: '0 1px 2px rgba(0, 0, 0, 0.5)',\n  },\n  \n  // 断点\n  breakpoints: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px',\n  },\n  \n  // 动画时长\n  transitions: {\n    fast: '150ms',\n    base: '300ms',\n    slow: '500ms',\n  },\n  \n  // Z-index\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060,\n  },\n}\n\nexport type Theme = typeof theme\n\n// 媒体查询助手\nexport const media = {\n  sm: `@media (min-width: ${theme.breakpoints.sm})`,\n  md: `@media (min-width: ${theme.breakpoints.md})`,\n  lg: `@media (min-width: ${theme.breakpoints.lg})`,\n  xl: `@media (min-width: ${theme.breakpoints.xl})`,\n  '2xl': `@media (min-width: ${theme.breakpoints['2xl']})`,\n\n  // 最大宽度查询\n  maxSm: `@media (max-width: 639px)`,\n  maxMd: `@media (max-width: 767px)`,\n  maxLg: `@media (max-width: 1023px)`,\n  maxXl: `@media (max-width: 1279px)`,\n}\n\n// 常用样式混合\nexport const mixins = {\n  // 居中\n  center: `\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  `,\n\n  // 毛玻璃效果\n  glassmorphism: `\n    background: ${theme.colors.background.card};\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  `,\n\n  // 按钮基础样式\n  buttonBase: `\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    border: none;\n    border-radius: ${theme.borderRadius.lg};\n    font-weight: ${theme.fontWeights.semibold};\n    cursor: pointer;\n    transition: all ${theme.transitions.base} ease-in-out;\n    user-select: none;\n\n    &:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n    }\n\n    &:hover:not(:disabled) {\n      transform: translateY(-1px);\n    }\n\n    &:active:not(:disabled) {\n      transform: translateY(0);\n    }\n  `,\n\n  // 卡片样式\n  card: `\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    border-radius: 1rem;\n    padding: 1.5rem;\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n    transition: all 0.2s ease-in-out;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.15);\n      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n      transform: translateY(-2px);\n    }\n  `,\n\n  // 文本截断\n  truncate: `\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  `,\n}\n", "import { createGlobalStyle } from 'styled-components'\n\nexport const GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html {\n    font-size: 16px;\n    -webkit-text-size-adjust: 100%;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  body {\n    font-family: ${({ theme }) => theme.fonts.primary};\n    background: ${({ theme }) => theme.colors.background.primary};\n    color: ${({ theme }) => theme.colors.text.primary};\n    line-height: 1.5;\n    overflow-x: hidden;\n    min-height: 100vh;\n    \n    /* 禁用移动端的选择和缩放 */\n    -webkit-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    \n    -webkit-touch-callout: none;\n    -webkit-tap-highlight-color: transparent;\n  }\n\n  #root {\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${({ theme }) => theme.colors.gray[800]};\n    border-radius: ${({ theme }) => theme.borderRadius.base};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${({ theme }) => theme.colors.gray[600]};\n    border-radius: ${({ theme }) => theme.borderRadius.base};\n    \n    &:hover {\n      background: ${({ theme }) => theme.colors.gray[500]};\n    }\n  }\n\n  /* 按钮和输入框重置 */\n  button {\n    border: none;\n    background: none;\n    cursor: pointer;\n    font-family: inherit;\n    font-size: inherit;\n    \n    &:focus {\n      outline: 2px solid ${({ theme }) => theme.colors.primary[500]};\n      outline-offset: 2px;\n    }\n    \n    &:disabled {\n      cursor: not-allowed;\n      opacity: 0.6;\n    }\n  }\n\n  input, textarea, select {\n    font-family: inherit;\n    font-size: inherit;\n    border: none;\n    background: none;\n    \n    &:focus {\n      outline: 2px solid ${({ theme }) => theme.colors.primary[500]};\n      outline-offset: 2px;\n    }\n  }\n\n  /* 链接样式 */\n  a {\n    color: ${({ theme }) => theme.colors.primary[400]};\n    text-decoration: none;\n    \n    &:hover {\n      color: ${({ theme }) => theme.colors.primary[300]};\n      text-decoration: underline;\n    }\n  }\n\n  /* 图片响应式 */\n  img {\n    max-width: 100%;\n    height: auto;\n  }\n\n  /* 隐藏元素 */\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n\n  /* 动画类 */\n  .fade-in {\n    animation: fadeIn 0.3s ease-in-out;\n  }\n\n  .slide-up {\n    animation: slideUp 0.3s ease-out;\n  }\n\n  .bounce {\n    animation: bounce 0.6s ease-in-out;\n  }\n\n  .pulse {\n    animation: pulse 2s infinite;\n  }\n\n  .glow {\n    animation: glow 2s ease-in-out infinite alternate;\n  }\n\n  /* 动画定义 */\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  @keyframes slideUp {\n    from {\n      transform: translateY(20px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes bounce {\n    0%, 20%, 53%, 80%, 100% {\n      transform: translate3d(0, 0, 0);\n    }\n    40%, 43% {\n      transform: translate3d(0, -10px, 0);\n    }\n    70% {\n      transform: translate3d(0, -5px, 0);\n    }\n    90% {\n      transform: translate3d(0, -2px, 0);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n    }\n    50% {\n      transform: scale(1.05);\n    }\n    100% {\n      transform: scale(1);\n    }\n  }\n\n  @keyframes glow {\n    from {\n      box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);\n    }\n    to {\n      box-shadow: 0 0 30px rgba(99, 102, 241, 0.8);\n    }\n  }\n\n  /* 移动端适配 */\n  @media (max-width: 768px) {\n    html {\n      font-size: 14px;\n    }\n    \n    body {\n      /* 防止移动端页面弹跳 */\n      position: fixed;\n      width: 100%;\n      height: 100%;\n      overflow: hidden;\n    }\n    \n    #root {\n      height: 100vh;\n      overflow-y: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n\n  /* 高对比度模式支持 */\n  @media (prefers-contrast: high) {\n    body {\n      background: #000;\n      color: #fff;\n    }\n  }\n\n  /* 减少动画模式支持 */\n  @media (prefers-reduced-motion: reduce) {\n    *,\n    *::before,\n    *::after {\n      animation-duration: 0.01ms !important;\n      animation-iteration-count: 1 !important;\n      transition-duration: 0.01ms !important;\n    }\n  }\n`\n", "import React from 'react'\nimport ReactD<PERSON> from 'react-dom/client'\nimport { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'\nimport { QueryClient, QueryClientProvider } from 'react-query'\nimport { Toaster } from 'react-hot-toast'\nimport { ThemeProvider } from 'styled-components'\n\nimport App from './App'\nimport { theme } from './styles/theme'\nimport { GlobalStyles } from './styles/GlobalStyles'\n\n// 创建 React Query 客户端\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 2,\n      staleTime: 5 * 60 * 1000, // 5分钟\n      cacheTime: 10 * 60 * 1000, // 10分钟\n    },\n  },\n})\n\nReactDOM.createRoot(document.getElementById('root')!).render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <QueryClientProvider client={queryClient}>\n        <ThemeProvider theme={theme}>\n          <GlobalStyles />\n          <App />\n          <Toaster\n            position=\"top-center\"\n            toastOptions={{\n              duration: 3000,\n              style: {\n                background: '#1f2937',\n                color: '#f9fafb',\n                border: '1px solid #374151',\n                borderRadius: '12px',\n                fontSize: '14px',\n              },\n              success: {\n                iconTheme: {\n                  primary: '#10b981',\n                  secondary: '#f9fafb',\n                },\n              },\n              error: {\n                iconTheme: {\n                  primary: '#ef4444',\n                  secondary: '#f9fafb',\n                },\n              },\n            }}\n          />\n        </ThemeProvider>\n      </QueryClientProvider>\n    </BrowserRouter>\n  </React.StrictMode>,\n)\n"], "file": "assets/index-CNCEp3EQ.js"}