import{j as s,t as o,m as n}from"./index-CNCEp3EQ.js";import{r as p}from"./vendor-Dneogk0_.js";import{d as t}from"./ui-ldAE8JkK.js";import{A as g}from"./achievements-BKGiRpq5.js";import{C as b}from"./Card-B65VmGcU.js";import{B as h}from"./Button-BlkTGlvm.js";import"./router-DMCr7QLp.js";const l=[{id:"score_1k",name:"初出茅庐",description:"单局得分达到1,000分",icon:"🌟",category:"score",rarity:"common",condition:{type:"single_game_score",target:1e3,current:850},rewards:{coins:50},unlocked:!1,progress:85,order:1},{id:"first_win",name:"首次胜利",description:"完成你的第一个关卡",icon:"🎉",category:"milestone",rarity:"common",condition:{type:"games_won",target:1},rewards:{coins:100},unlocked:!0,unlockedAt:new Date("2024-01-15"),progress:100,order:100},{id:"combo_10",name:"连击高手",description:"达成10连击",icon:"💥",category:"combo",rarity:"rare",condition:{type:"max_combo",target:10,current:7},rewards:{coins:100},unlocked:!1,progress:70,order:11}],y=t.div`
  min-height: 100vh;
  background: ${o.colors.background.primary};
  padding: ${o.spacing[6]} ${o.spacing[4]};
  
  ${n.maxMd} {
    padding: ${o.spacing[4]} ${o.spacing[3]};
  }
  
  ${n.maxSm} {
    padding: ${o.spacing[3]} ${o.spacing[2]};
  }
`,k=t.div`
  text-align: center;
  margin-bottom: ${o.spacing[8]};
  
  ${n.maxMd} {
    margin-bottom: ${o.spacing[6]};
  }
`,w=t.h1`
  color: ${o.colors.text.primary};
  font-size: ${o.fontSizes["4xl"]};
  font-weight: ${o.fontWeights.bold};
  margin-bottom: ${o.spacing[4]};
  text-shadow: ${o.shadows.text};
  
  ${n.maxMd} {
    font-size: ${o.fontSizes["3xl"]};
  }
  
  ${n.maxSm} {
    font-size: ${o.fontSizes["2xl"]};
  }
`,j=t.div`
  display: flex;
  justify-content: center;
  gap: ${o.spacing[6]};
  margin-bottom: ${o.spacing[6]};
  
  ${n.maxMd} {
    gap: ${o.spacing[4]};
    margin-bottom: ${o.spacing[4]};
  }
  
  ${n.maxSm} {
    flex-direction: column;
    align-items: center;
    gap: ${o.spacing[3]};
  }
`,x=t.div`
  text-align: center;
  
  .value {
    font-size: ${o.fontSizes["2xl"]};
    font-weight: ${o.fontWeights.bold};
    color: ${o.colors.secondary[400]};
    margin-bottom: ${o.spacing[1]};
  }
  
  .label {
    font-size: ${o.fontSizes.sm};
    color: ${o.colors.text.secondary};
  }
  
  ${n.maxMd} {
    .value {
      font-size: ${o.fontSizes.xl};
    }
    
    .label {
      font-size: ${o.fontSizes.xs};
    }
  }
`,z=t.div`
  display: flex;
  justify-content: center;
  gap: ${o.spacing[2]};
  margin-bottom: ${o.spacing[6]};
  flex-wrap: wrap;
  
  ${n.maxMd} {
    margin-bottom: ${o.spacing[4]};
  }
`,S=t(h)`
  ${r=>r.$active&&`
    background: ${o.colors.secondary[500]};
    color: white;
    
    &:hover {
      background: ${o.colors.secondary[600]};
    }
  `}
`,v=t.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: ${o.spacing[4]};
  max-width: 1200px;
  margin: 0 auto;
  
  ${n.maxMd} {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: ${o.spacing[3]};
  }
  
  ${n.maxSm} {
    grid-template-columns: 1fr;
    gap: ${o.spacing[2]};
  }
`,A=t(b)`
  ${r=>{const i=g[r.$rarity];return`
      border: 2px solid ${r.$unlocked?i.color:i.borderColor};
      background: ${r.$unlocked?i.bgColor:"rgba(255, 255, 255, 0.02)"};
      ${r.$unlocked?`box-shadow: ${i.glow}, ${o.shadows.lg};`:""}
    `}}
  
  opacity: ${r=>r.$unlocked?1:.7};
  transition: all ${o.transitions.base} ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    ${r=>{const i=g[r.$rarity];return r.$unlocked?`box-shadow: ${i.glow}, ${o.shadows.xl};`:""}}
  }
`,M=t.div`
  display: flex;
  align-items: center;
  gap: ${o.spacing[3]};
  margin-bottom: ${o.spacing[3]};
`,C=t.div`
  font-size: 48px;
  filter: ${r=>r.$unlocked?"none":"grayscale(100%)"};
  
  ${n.maxMd} {
    font-size: 40px;
  }
`,R=t.div`
  flex: 1;
`,N=t.h3`
  color: ${r=>g[r.$rarity].color};
  font-size: ${o.fontSizes.lg};
  font-weight: ${o.fontWeights.semibold};
  margin: 0 0 ${o.spacing[1]} 0;
  
  ${n.maxMd} {
    font-size: ${o.fontSizes.base};
  }
`,_=t.p`
  color: ${o.colors.text.secondary};
  font-size: ${o.fontSizes.sm};
  margin: 0;
  line-height: 1.4;
  
  ${n.maxMd} {
    font-size: ${o.fontSizes.xs};
  }
`,I=t.div`
  margin-bottom: ${o.spacing[3]};
  display: ${r=>r.$unlocked?"none":"block"};
`,E=t.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${o.borderRadius.full};
  height: 8px;
  overflow: hidden;
  margin-bottom: ${o.spacing[1]};
`,F=t.div`
  height: 100%;
  background: ${r=>g[r.$rarity].color};
  width: ${r=>r.$progress}%;
  transition: width ${o.transitions.base} ease-in-out;
  border-radius: ${o.borderRadius.full};
`,P=t.div`
  font-size: ${o.fontSizes.xs};
  color: ${o.colors.text.secondary};
  text-align: center;
`,B=t.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${o.spacing[1]};
`,d=t.span`
  background: rgba(255, 255, 255, 0.1);
  color: ${o.colors.secondary[400]};
  padding: 2px ${o.spacing[2]};
  border-radius: ${o.borderRadius.base};
  font-size: ${o.fontSizes.xs};
  font-weight: ${o.fontWeights.medium};
  border: 1px solid rgba(251, 191, 36, 0.3);
`,D=t.div`
  font-size: ${o.fontSizes.xs};
  color: ${o.colors.text.secondary};
  text-align: center;
  margin-top: ${o.spacing[2]};
  font-style: italic;
`,O=()=>{const[r,i]=p.useState("all"),f=p.useMemo(()=>r==="all"?l:l.filter(e=>e.category===r),[r]),$=p.useMemo(()=>{const e=l.length,a=l.filter(m=>m.unlocked).length,c=e>0?Math.round(a/e*100):0;return{total:e,unlocked:a,completionRate:c}},[]),u=[{key:"all",label:"全部"},{key:"score",label:"分数"},{key:"combo",label:"连击"},{key:"level",label:"关卡"},{key:"special",label:"特殊"},{key:"powerup",label:"道具"},{key:"milestone",label:"里程碑"}];return s.jsxs(y,{children:[s.jsxs(k,{children:[s.jsx(w,{children:"成就系统"}),s.jsxs(j,{children:[s.jsxs(x,{children:[s.jsx("div",{className:"value",children:$.unlocked}),s.jsx("div",{className:"label",children:"已解锁"})]}),s.jsxs(x,{children:[s.jsx("div",{className:"value",children:$.total}),s.jsx("div",{className:"label",children:"总成就"})]}),s.jsxs(x,{children:[s.jsxs("div",{className:"value",children:[$.completionRate,"%"]}),s.jsx("div",{className:"label",children:"完成率"})]})]}),s.jsx(z,{children:u.map(e=>s.jsx(S,{variant:"ghost",size:"sm",$active:r===e.key,onClick:()=>i(e.key),children:e.label},e.key))})]}),s.jsx(v,{children:f.map(e=>{var a;return s.jsxs(A,{$unlocked:e.unlocked,$rarity:e.rarity,children:[s.jsxs(M,{children:[s.jsx(C,{$unlocked:e.unlocked,children:e.icon}),s.jsxs(R,{children:[s.jsx(N,{$rarity:e.rarity,children:e.name}),s.jsx(_,{children:e.description})]})]}),s.jsxs(I,{$unlocked:e.unlocked,children:[s.jsx(E,{children:s.jsx(F,{$progress:e.progress,$rarity:e.rarity})}),s.jsxs(P,{children:[e.condition.current||0," / ",e.condition.target]})]}),s.jsxs(B,{children:[e.rewards.coins&&s.jsxs(d,{children:["💰 ",e.rewards.coins]}),(a=e.rewards.powerups)==null?void 0:a.map((c,m)=>s.jsxs(d,{children:["🔧 ",c.type," x",c.quantity]},m)),e.rewards.title&&s.jsxs(d,{children:["👑 ",e.rewards.title]}),e.rewards.badge&&s.jsxs(d,{children:["🏅 ",e.rewards.badge]})]}),e.unlocked&&e.unlockedAt&&s.jsxs(D,{children:["解锁于 ",e.unlockedAt.toLocaleDateString()]})]},e.id)})})]})};export{O as default};
//# sourceMappingURL=AchievementsPage-BeJEZHzx.js.map
