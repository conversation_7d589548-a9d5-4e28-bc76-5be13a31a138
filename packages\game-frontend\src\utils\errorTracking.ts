// 错误追踪和报告系统

export interface ErrorReport {
  id: string
  timestamp: number
  type: 'javascript' | 'network' | 'game' | 'performance'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  stack?: string
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  gameState?: any
  performanceMetrics?: any
  additionalData?: Record<string, any>
}

export interface ErrorTrackingConfig {
  apiEndpoint: string
  maxErrors: number
  batchSize: number
  flushInterval: number
  enableConsoleLogging: boolean
  enablePerformanceTracking: boolean
  enableGameStateCapture: boolean
}

class ErrorTracker {
  private config: ErrorTrackingConfig
  private errorQueue: ErrorReport[] = []
  private sessionId: string
  private flushTimer?: NodeJS.Timeout

  constructor(config: Partial<ErrorTrackingConfig> = {}) {
    this.config = {
      apiEndpoint: '/api/errors',
      maxErrors: 100,
      batchSize: 10,
      flushInterval: 30000, // 30秒
      enableConsoleLogging: true,
      enablePerformanceTracking: true,
      enableGameStateCapture: true,
      ...config
    }

    this.sessionId = this.generateSessionId()
    this.setupErrorHandlers()
    this.startFlushTimer()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setupErrorHandlers(): void {
    // JavaScript错误处理
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript',
        severity: 'high',
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        additionalData: {
          lineno: event.lineno,
          colno: event.colno
        }
      })
    })

    // Promise rejection处理
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'javascript',
        severity: 'high',
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        additionalData: {
          reason: event.reason
        }
      })
    })

    // 网络错误处理
    this.setupNetworkErrorTracking()

    // 性能监控
    if (this.config.enablePerformanceTracking) {
      this.setupPerformanceTracking()
    }
  }

  private setupNetworkErrorTracking(): void {
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args)
        if (!response.ok) {
          this.captureError({
            type: 'network',
            severity: response.status >= 500 ? 'high' : 'medium',
            message: `Network error: ${response.status} ${response.statusText}`,
            url: typeof args[0] === 'string' ? args[0] : (args[0] as any).url,
            additionalData: {
              status: response.status,
              statusText: response.statusText,
              method: typeof args[1] === 'object' ? args[1]?.method : 'GET'
            }
          })
        }
        return response
      } catch (error) {
        this.captureError({
          type: 'network',
          severity: 'high',
          message: `Network request failed: ${error}`,
          url: typeof args[0] === 'string' ? args[0] : (args[0] as any).url,
          additionalData: {
            error: error instanceof Error ? error.message : String(error)
          }
        })
        throw error
      }
    }
  }

  private setupPerformanceTracking(): void {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // 超过50ms的任务
            this.captureError({
              type: 'performance',
              severity: entry.duration > 100 ? 'medium' : 'low',
              message: `Long task detected: ${entry.duration}ms`,
              url: window.location.href,
              additionalData: {
                duration: entry.duration,
                startTime: entry.startTime,
                entryType: entry.entryType
              }
            })
          }
        }
      })

      try {
        observer.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        // longtask可能不被支持
      }
    }

    // 监控内存使用
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          this.captureError({
            type: 'performance',
            severity: 'high',
            message: 'High memory usage detected',
            url: window.location.href,
            additionalData: {
              usedJSHeapSize: memory.usedJSHeapSize,
              totalJSHeapSize: memory.totalJSHeapSize,
              jsHeapSizeLimit: memory.jsHeapSizeLimit
            }
          })
        }
      }, 60000) // 每分钟检查一次
    }
  }

  public captureError(errorData: Partial<ErrorReport>): void {
    const error: ErrorReport = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      type: 'javascript',
      severity: 'medium',
      message: 'Unknown error',
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      ...errorData
    }

    // 添加游戏状态（如果启用）
    if (this.config.enableGameStateCapture) {
      error.gameState = this.captureGameState()
    }

    // 添加性能指标
    if (this.config.enablePerformanceTracking) {
      error.performanceMetrics = this.capturePerformanceMetrics()
    }

    // 添加到队列
    this.errorQueue.push(error)

    // 控制台日志
    if (this.config.enableConsoleLogging) {
      console.error('[ErrorTracker]', error)
    }

    // 检查是否需要立即发送
    if (error.severity === 'critical' || this.errorQueue.length >= this.config.batchSize) {
      this.flush()
    }

    // 限制队列大小
    if (this.errorQueue.length > this.config.maxErrors) {
      this.errorQueue = this.errorQueue.slice(-this.config.maxErrors)
    }
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private captureGameState(): any {
    try {
      // 尝试从全局状态或localStorage获取游戏状态
      const gameState = localStorage.getItem('gameState')
      return gameState ? JSON.parse(gameState) : null
    } catch (e) {
      return null
    }
  }

  private capturePerformanceMetrics(): any {
    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const memory = (performance as any).memory

      return {
        loadTime: navigation?.loadEventEnd - navigation?.loadEventStart,
        domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime,
        memory: memory ? {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        } : null
      }
    } catch (e) {
      return null
    }
  }

  public captureGameError(message: string, gameData?: any): void {
    this.captureError({
      type: 'game',
      severity: 'medium',
      message,
      additionalData: gameData
    })
  }

  public captureUserAction(action: string, data?: any): void {
    // 记录用户行为，用于错误重现
    const userAction = {
      action,
      timestamp: Date.now(),
      data
    }

    // 存储最近的用户行为
    const recentActions = JSON.parse(localStorage.getItem('recentUserActions') || '[]')
    recentActions.push(userAction)
    
    // 只保留最近50个行为
    if (recentActions.length > 50) {
      recentActions.shift()
    }
    
    localStorage.setItem('recentUserActions', JSON.stringify(recentActions))
  }

  private async flush(): Promise<void> {
    if (this.errorQueue.length === 0) return

    const errors = [...this.errorQueue]
    this.errorQueue = []

    try {
      await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errors,
          sessionId: this.sessionId,
          userActions: JSON.parse(localStorage.getItem('recentUserActions') || '[]')
        })
      })
    } catch (e) {
      // 发送失败，重新加入队列
      this.errorQueue.unshift(...errors)
      console.error('Failed to send error reports:', e)
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flush() // 最后一次发送
  }

  public setUserId(userId: string): void {
    // 为所有后续错误设置用户ID
    this.errorQueue.forEach(error => {
      error.userId = userId
    })
  }
}

// 创建全局错误追踪器实例
export const globalErrorTracker = new ErrorTracker({
  apiEndpoint: process.env.REACT_APP_API_URL + '/api/errors',
  enableConsoleLogging: process.env.NODE_ENV === 'development'
})

// 导出便捷函数
export const captureError = (message: string, additionalData?: any) => {
  globalErrorTracker.captureError({
    type: 'javascript',
    severity: 'medium',
    message,
    additionalData
  })
}

export const captureGameError = (message: string, gameData?: any) => {
  globalErrorTracker.captureGameError(message, gameData)
}

export const captureUserAction = (action: string, data?: any) => {
  globalErrorTracker.captureUserAction(action, data)
}

export const setUserId = (userId: string) => {
  globalErrorTracker.setUserId(userId)
}
