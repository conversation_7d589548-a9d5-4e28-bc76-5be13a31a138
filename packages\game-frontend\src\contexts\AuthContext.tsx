import React, { createContext, useContext, useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

// 类型定义
interface User {
  id: string
  username: string
  email: string
  level: number
  experience: number
  coins: number
  gems: number
  avatar?: string
  isActive?: boolean
  isPremium?: boolean
  createdAt: string
  lastLoginAt: string
}

interface LoginCredentials {
  email: string
  password: string
}

interface RegisterCredentials {
  username: string
  email: string
  password: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<void>
}

// Mock data for development
const mockUser: User = {
  id: '1',
  username: '测试用户',
  email: '<EMAIL>',
  level: 5,
  experience: 1250,
  coins: 5000,
  gems: 100,
  isActive: true,
  isPremium: false,
  createdAt: new Date().toISOString(),
  lastLoginAt: new Date().toISOString(),
}

// API 函数
const authAPI = {
  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const token = localStorage.getItem('token')
    if (!token) throw new Error('No token found')

    try {
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to get user info')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock data')
      return mockUser
    }
  },

  // 登录
  login: async (credentials: LoginCredentials): Promise<{ user: User; token: string }> => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Login failed')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock data')
      return {
        user: mockUser,
        token: 'mock-token-' + Date.now(),
      }
    }
  },

  // 注册
  register: async (credentials: RegisterCredentials): Promise<{ user: User; token: string }> => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Registration failed')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock data')
      return {
        user: { ...mockUser, username: credentials.username, email: credentials.email },
        token: 'mock-token-' + Date.now(),
      }
    }
  },

  // 更新用户信息
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const token = localStorage.getItem('token')
    if (!token) throw new Error('No token found')

    try {
      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Update failed')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock data')
      return { ...mockUser, ...data }
    }
  },
}

// 创建 Context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// AuthProvider 组件
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const queryClient = useQueryClient()

  // 获取用户信息查询
  const { isLoading } = useQuery(
    'currentUser',
    authAPI.getCurrentUser,
    {
      enabled: !!localStorage.getItem('token'),
      retry: false,
      onSuccess: (data) => {
        setUser(data)
      },
      onError: () => {
        localStorage.removeItem('token')
        setUser(null)
      },
    }
  )

  // 登录 mutation
  const loginMutation = useMutation(authAPI.login, {
    onSuccess: (data) => {
      localStorage.setItem('token', data.token)
      setUser(data.user)
      queryClient.setQueryData('currentUser', data.user)
      toast.success('登录成功！')
    },
    onError: (error: Error) => {
      toast.error(error.message || '登录失败')
    },
  })

  // 注册 mutation
  const registerMutation = useMutation(authAPI.register, {
    onSuccess: (data) => {
      localStorage.setItem('token', data.token)
      setUser(data.user)
      queryClient.setQueryData('currentUser', data.user)
      toast.success('注册成功！')
    },
    onError: (error: Error) => {
      toast.error(error.message || '注册失败')
    },
  })

  // 更新用户信息 mutation
  const updateProfileMutation = useMutation(authAPI.updateProfile, {
    onSuccess: (data) => {
      setUser(data)
      queryClient.setQueryData('currentUser', data)
      toast.success('个人信息更新成功！')
    },
    onError: (error: Error) => {
      toast.error(error.message || '更新失败')
    },
  })

  // 登录函数
  const login = async (credentials: LoginCredentials) => {
    await loginMutation.mutateAsync(credentials)
  }

  // 注册函数
  const register = async (credentials: RegisterCredentials) => {
    await registerMutation.mutateAsync(credentials)
  }

  // 登出函数
  const logout = () => {
    localStorage.removeItem('token')
    setUser(null)
    queryClient.clear()
    toast.success('已退出登录')
  }

  // 更新用户信息函数
  const updateProfile = async (data: Partial<User>) => {
    await updateProfileMutation.mutateAsync(data)
  }

  // 初始化时检查 token
  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      setUser(null)
    }
  }, [])

  const value: AuthContextType = {
    user,
    loading: isLoading,
    login,
    register,
    logout,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

// useAuth hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
