const o={global_score:{name:"全球总分",icon:"🏆",description:"所有玩家的总分排行",valueLabel:"总分",refreshInterval:3e5,maxEntries:100},weekly_score:{name:"本周分数",icon:"📅",description:"本周获得分数排行",valueLabel:"周分数",refreshInterval:6e4,maxEntries:50},daily_score:{name:"今日分数",icon:"☀️",description:"今日获得分数排行",valueLabel:"日分数",refreshInterval:3e4,maxEntries:30},level_completion:{name:"关卡完成",icon:"🎯",description:"完成关卡数量排行",valueLabel:"关卡数",refreshInterval:3e5,maxEntries:100},combo_record:{name:"最高连击",icon:"⚡",description:"最高连击记录排行",valueLabel:"连击数",refreshInterval:3e5,maxEntries:50},speed_run:{name:"速通记录",icon:"🏃",description:"关卡完成时间排行",valueLabel:"时间",refreshInterval:3e5,maxEntries:50},achievement_count:{name:"成就大师",icon:"🏅",description:"解锁成就数量排行",valueLabel:"成就数",refreshInterval:3e5,maxEntries:100}},i={wechat:{name:"微信",icon:"💬",color:"#07c160",available:!0},weibo:{name:"微博",icon:"📱",color:"#e6162d",available:!0},qq:{name:"QQ",icon:"🐧",color:"#12b7f5",available:!0},copy_link:{name:"复制链接",icon:"🔗",color:"#6b7280",available:!0}},c=r=>{const e=o[r],t=[];for(let a=1;a<=Math.min(e.maxEntries,20);a++){const n=Math.floor(Math.random()*1e5)+1e4;t.push({id:`entry_${a}`,userId:`user_${a}`,username:`玩家${a.toString().padStart(3,"0")}`,avatar:`https://api.dicebear.com/7.x/avataaars/svg?seed=${a}`,rank:a,score:n,value:s(r,n,a),level:Math.floor(Math.random()*50)+1,combo:Math.floor(Math.random()*20)+1,time:Math.floor(Math.random()*300)+30,achievements:Math.floor(Math.random()*30)+1,createdAt:new Date(Date.now()-Math.random()*7*24*60*60*1e3),updatedAt:new Date,isOnline:Math.random()>.7,isFriend:Math.random()>.8,isCurrentUser:a===5})}return{type:r,entries:t,totalEntries:e.maxEntries,currentUserRank:5,currentUserEntry:t[4],lastUpdated:new Date}};function s(r,e,t){switch(r){case"global_score":case"weekly_score":case"daily_score":return e;case"level_completion":return Math.max(1,100-t*2);case"combo_record":return Math.max(1,50-t);case"speed_run":return 30+t*5;case"achievement_count":return Math.max(1,50-t);default:return e}}const l=()=>{const r=[];for(let e=1;e<=10;e++)r.push({id:`friend_${e}`,userId:`user_${e+100}`,username:`好友${e}`,avatar:`https://api.dicebear.com/7.x/avataaars/svg?seed=friend${e}`,level:Math.floor(Math.random()*30)+1,totalScore:Math.floor(Math.random()*5e4)+5e3,lastActive:new Date(Date.now()-Math.random()*24*60*60*1e3),isOnline:Math.random()>.6,friendshipId:`friendship_${e}`,friendedAt:new Date(Date.now()-Math.random()*30*24*60*60*1e3),status:"accepted",recentAchievements:[{id:`achievement_${e}_1`,name:"连击高手",icon:"💥",unlockedAt:new Date(Date.now()-Math.random()*7*24*60*60*1e3)}],stats:{gamesPlayed:Math.floor(Math.random()*100)+10,gamesWon:Math.floor(Math.random()*50)+5,maxCombo:Math.floor(Math.random()*20)+1,achievementsUnlocked:Math.floor(Math.random()*20)+1}});return r};export{o as L,i as S,l as a,c as g};
//# sourceMappingURL=leaderboard-Or32oZ16.js.map
