var u=Object.defineProperty;var h=(s,t,e)=>t in s?u(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var l=(s,t,e)=>h(s,typeof t!="symbol"?t+"":t,e);import{a,b as m}from"./specialGems-xbtj_zef.js";class b{constructor(){l(this,"currentComboCount",0);l(this,"lastComboTime",0);l(this,"comboTimeout",2e3)}checkSpecialGemCreation(t){const e=[];for(const o of t){const r=this.analyzeMatch(o);r.shouldCreate&&e.push(r)}return e}analyzeMatch(t){const{positions:e,type:o,length:r}=t,i=this.getCenterPosition(e);if(r>=6)return{shouldCreate:!0,type:"rainbow",position:i,specialGem:this.createSpecialGem("rainbow",t.color,i)};if(r===5)switch(o){case"square":return{shouldCreate:!0,type:"color_bomb",position:i,specialGem:this.createSpecialGem("color_bomb",t.color,i)};case"cross":return{shouldCreate:!0,type:"lightning",position:i,specialGem:this.createSpecialGem("lightning",t.color,i)};case"L":case"T":return{shouldCreate:!0,type:"bomb",position:i,specialGem:this.createSpecialGem("bomb",t.color,i)};default:const n=this.isHorizontalLine(e);return{shouldCreate:!0,type:n?"line_horizontal":"line_vertical",position:i,specialGem:this.createSpecialGem(n?"line_horizontal":"line_vertical",t.color,i)}}if(r===4){const n=this.isHorizontalLine(e);return{shouldCreate:!0,type:n?"line_horizontal":"line_vertical",position:i,specialGem:this.createSpecialGem(n?"line_horizontal":"line_vertical",t.color,i)}}return{shouldCreate:!1,position:i}}createSpecialGem(t,e,o){return{type:t,color:e,row:o.row,col:o.col,id:`special_${t}_${Date.now()}_${Math.random()}`}}getCenterPosition(t){const e=Math.round(t.reduce((r,i)=>r+i.row,0)/t.length),o=Math.round(t.reduce((r,i)=>r+i.col,0)/t.length);return{row:e,col:o}}isHorizontalLine(t){return new Set(t.map(o=>o.row)).size===1}activateSpecialGem(t,e){switch(t.type){case"line_horizontal":return this.activateHorizontalLine(t,e);case"line_vertical":return this.activateVerticalLine(t,e);case"bomb":return this.activateBomb(t,e);case"color_bomb":return this.activateColorBomb(t,e);case"lightning":return this.activateLightning(t,e);case"rainbow":return this.activateRainbow(t,e);default:return[]}}activateHorizontalLine(t,e){const o=[];for(let r=0;r<e[0].length;r++)o.push({row:t.row,col:r});return o}activateVerticalLine(t,e){const o=[];for(let r=0;r<e.length;r++)o.push({row:r,col:t.col});return o}activateBomb(t,e){const o=[],{row:r,col:i}=t;for(let n=Math.max(0,r-1);n<=Math.min(e.length-1,r+1);n++)for(let c=Math.max(0,i-1);c<=Math.min(e[0].length-1,i+1);c++)o.push({row:n,col:c});return o}activateColorBomb(t,e){const o=[],r=t.color;for(let i=0;i<e.length;i++)for(let n=0;n<e[0].length;n++)e[i][n]===r&&o.push({row:i,col:n});return o}activateLightning(t,e){const o=[],{row:r,col:i}=t;for(let n=0;n<e[0].length;n++)o.push({row:r,col:n});for(let n=0;n<e.length;n++)o.push({row:n,col:i});return o}activateRainbow(t,e){return[]}processCombo(t){const e=Date.now();if(e-this.lastComboTime<=this.comboTimeout?this.currentComboCount++:this.currentComboCount=1,this.lastComboTime=e,this.currentComboCount>=2){const o=this.getComboMultiplier(this.currentComboCount),r=t*10*o;return{type:"cascade_combo",multiplier:o,description:`${this.currentComboCount}连击！`,scoreBonus:r}}return null}getComboMultiplier(t){return t in a?a[t]:a.max}resetCombo(){this.currentComboCount=0,this.lastComboTime=0}getCurrentCombo(){return this.currentComboCount}checkComboRewards(t){const e=m.milestones;return t in e?e[t]:null}}export{b as SpecialGemManager};
//# sourceMappingURL=specialGemManager-S1dnzDs3.js.map
