"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.createError = exports.errorHandler = void 0;
const errorHandler = (err, req, res, next) => {
    console.error('API错误:', err);
    let statusCode = err.statusCode || 500;
    let errorCode = err.code || 'INTERNAL_SERVER_ERROR';
    let message = err.message || '服务器内部错误';
    if (err.name === 'ValidationError') {
        statusCode = 400;
        errorCode = 'VALIDATION_ERROR';
        message = '请求参数验证失败';
    }
    else if (err.name === 'JsonWebTokenError') {
        statusCode = 401;
        errorCode = 'AUTH_001';
        message = '无效的认证令牌';
    }
    else if (err.name === 'TokenExpiredError') {
        statusCode = 401;
        errorCode = 'AUTH_002';
        message = '认证令牌已过期';
    }
    else if (err.message.includes('UNIQUE constraint failed')) {
        statusCode = 409;
        errorCode = 'DUPLICATE_RESOURCE';
        message = '资源已存在';
    }
    res.status(statusCode).json({
        success: false,
        error: {
            code: errorCode,
            message: message
        }
    });
};
exports.errorHandler = errorHandler;
const createError = (statusCode, code, message) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    error.code = code;
    return error;
};
exports.createError = createError;
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map