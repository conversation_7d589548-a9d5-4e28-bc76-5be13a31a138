"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Database = void 0;
const sqlite3_1 = __importDefault(require("sqlite3"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
class Database {
    constructor() {
        this.db = null;
    }
    static getInstance() {
        if (!Database.instance) {
            Database.instance = new Database();
        }
        return Database.instance;
    }
    static async initialize() {
        const instance = Database.getInstance();
        await instance.connect();
        await instance.createTables();
        await instance.seedInitialData();
    }
    async connect() {
        return new Promise((resolve, reject) => {
            const dbPath = process.env.DATABASE_PATH || './database/starry_match.db';
            const dbDir = path_1.default.dirname(dbPath);
            if (!fs_1.default.existsSync(dbDir)) {
                fs_1.default.mkdirSync(dbDir, { recursive: true });
            }
            this.db = new sqlite3_1.default.Database(dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err);
                    reject(err);
                }
                else {
                    console.log('数据库连接成功');
                    resolve();
                }
            });
        });
    }
    getDb() {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }
        return this.db;
    }
    async run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.getDb().run(sql, params, function (err) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(this);
                }
            });
        });
    }
    async get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.getDb().get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(row);
                }
            });
        });
    }
    async all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.getDb().all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(rows);
                }
            });
        });
    }
    async createTables() {
        const tables = [
            `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        display_name VARCHAR(50),
        avatar_url VARCHAR(255),
        coins INTEGER DEFAULT 100,
        gems INTEGER DEFAULT 5,
        current_level INTEGER DEFAULT 1,
        total_score INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,
            `CREATE TABLE IF NOT EXISTS levels (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level_number INTEGER UNIQUE NOT NULL,
        board_size INTEGER DEFAULT 6,
        max_moves INTEGER NOT NULL,
        target_score INTEGER,
        target_type VARCHAR(20) NOT NULL,
        target_data TEXT,
        obstacles TEXT,
        special_gems TEXT,
        difficulty_level INTEGER DEFAULT 1,
        reward_coins INTEGER DEFAULT 10,
        reward_items TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
            `CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_code VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        type VARCHAR(20) NOT NULL,
        rarity VARCHAR(20) DEFAULT 'common',
        effect_data TEXT,
        price_coins INTEGER DEFAULT 0,
        price_gems INTEGER DEFAULT 0,
        max_stack INTEGER DEFAULT 99,
        icon_url VARCHAR(255),
        is_purchasable BOOLEAN DEFAULT 1,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
        ];
        for (const table of tables) {
            await this.run(table);
        }
    }
    async seedInitialData() {
        const itemCount = await this.get('SELECT COUNT(*) as count FROM items');
        if (itemCount && itemCount.count > 0) {
            return;
        }
        const items = [
            ['hammer', '魔法锤', '消除单个宝石', 'tool', 'common', '{"type":"single_destroy"}', 100, 0],
            ['color_bomb', '颜色炸弹', '清除所有同色宝石', 'tool', 'rare', '{"type":"color_clear"}', 200, 1],
            ['rainbow_star', '彩虹星', '消除整行或整列', 'tool', 'epic', '{"type":"line_clear"}', 0, 3],
            ['extra_moves', '额外步数', '增加5步移动机会', 'booster', 'common', '{"type":"add_moves","value":5}', 150, 0],
            ['star_burst', '星爆冲击', '清空所有障碍物', 'tool', 'legendary', '{"type":"clear_obstacles"}', 0, 5]
        ];
        for (const item of items) {
            await this.run(`INSERT INTO items (item_code, name, description, type, rarity, effect_data, price_coins, price_gems) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, item);
        }
        const levels = [
            [1, 6, 20, 1000, 'score', 1],
            [2, 6, 18, 1500, 'score', 1],
            [3, 6, 16, 2000, 'score', 1],
            [4, 6, 15, 2500, 'collect', 2],
            [5, 6, 14, 3000, 'score', 2]
        ];
        for (const level of levels) {
            await this.run(`INSERT INTO levels (level_number, board_size, max_moves, target_score, target_type, difficulty_level) 
         VALUES (?, ?, ?, ?, ?, ?)`, level);
        }
    }
    static close() {
        const instance = Database.getInstance();
        if (instance.db) {
            instance.db.close();
            instance.db = null;
        }
    }
}
exports.Database = Database;
//# sourceMappingURL=database.js.map