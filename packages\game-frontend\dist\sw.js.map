{"version": 3, "file": "sw.js", "sources": ["C:/Users/<USER>/AppData/Local/Temp/62808181a549226630bf23f8d2b15e66/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'D:/AICHAT/rrr/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'D:/AICHAT/rrr/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'D:/AICHAT/rrr/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'D:/AICHAT/rrr/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'D:/AICHAT/rrr/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'D:/AICHAT/rrr/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'D:/AICHAT/rrr/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'D:/AICHAT/rrr/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"assets/achievementManager-Bt3BvN3k.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/achievements-BKGiRpq5.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/AchievementsPage-BeJEZHzx.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/animationManager-DTsUvCq5.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/Button-BlkTGlvm.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/Card-B65VmGcU.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/GameBoard-DvXOxAln.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/GameOverModal-4jLpsjyZ.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/GamePage-h55HzyeI.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index-CNCEp3EQ.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/leaderboard-Or32oZ16.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/LeaderboardPage-x048hut8.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/LevelsPage-D220u2A8.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/powerUpManager-DAya6dWG.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/PowerUpPanel-Cy3BrM2g.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/powerups-DNw9s1Qv.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ProfilePage-CsLxkWtu.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/router-DMCr7QLp.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ShareModal-GIcMGaE5.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/specialGemManager-S1dnzDs3.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/specialGems-xbtj_zef.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ui-ldAE8JkK.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/utils-l0sNRNKZ.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/vendor-Dneogk0_.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"favicon.ico\",\n    \"revision\": \"3accab99ae8ff475e03148f47650621c\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"323a88f7e981d76e5558debad8f7b1b9\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"favicon.ico\",\n    \"revision\": \"3accab99ae8ff475e03148f47650621c\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"9cddc3465594cb2d95cfe94f965a5e71\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/api\\./, new workbox_strategies_NetworkFirst({ \"cacheName\":\"api-cache\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 100, maxAgeSeconds: 86400 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_NetworkFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds"], "mappings": "inBA0BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,wCACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,0BACPC,SAAY,MAEd,CACED,IAAO,+BACPC,SAAY,MAEd,CACED,IAAO,mCACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,iCACPC,SAAY,MAEd,CACED,IAAO,qCACPC,SAAY,MAEd,CACED,IAAO,gCACPC,SAAY,MAEd,CACED,IAAO,oCACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,iCACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,gCACPC,SAAY,MAEd,CACED,IAAO,uCACPC,SAAY,MAEd,CACED,IAAO,iCACPC,SAAY,MAEd,CACED,IAAO,wBACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,gBAG7GC,EAAAA,cAA8B,mBAAoB,IAAIC,eAAgC,CAAEC,UAAY,YAAaC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,IAAKC,cAAe,WAAc"}