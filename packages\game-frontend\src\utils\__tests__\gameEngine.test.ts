import { GameEngine } from '../gameEngine'

describe('GameEngine', () => {
  let gameEngine: GameEngine

  beforeEach(() => {
    const initialBoard = Array(6).fill(null).map(() => Array(6).fill(1))
    gameEngine = new GameEngine(initialBoard, 6)
  })

  describe('generateBoard', () => {
    it('should generate a board with correct dimensions', () => {
      const board = gameEngine.generateBoard(6)
      expect(board).toHaveLength(6)
      expect(board[0]).toHaveLength(6)
    })

    it('should generate a board with valid gem types', () => {
      const board = gameEngine.generateBoard(6)
      board.forEach(row => {
        row.forEach(gem => {
          expect(gem).toBeGreaterThanOrEqual(1)
          expect(gem).toBeLessThanOrEqual(6)
        })
      })
    })

    it('should not generate initial matches', () => {
      const board = gameEngine.generateBoard(6)
      gameEngine = new GameEngine(board, 6)
      const matches = gameEngine.findMatches()
      expect(matches).toHaveLength(0)
    })
  })

  describe('findMatches', () => {
    it('should find horizontal matches', () => {
      const board = [
        [1, 1, 1, 2, 3, 4],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const matches = gameEngine.findMatches()
      expect(matches).toHaveLength(1)
      expect(matches[0].positions).toHaveLength(3)
      expect(matches[0].type).toBe('horizontal')
    })

    it('should find vertical matches', () => {
      const board = [
        [1, 2, 3, 4, 5, 6],
        [1, 3, 4, 5, 6, 1],
        [1, 4, 5, 6, 1, 2],
        [2, 5, 6, 1, 2, 3],
        [3, 6, 1, 2, 3, 4],
        [4, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const matches = gameEngine.findMatches()
      expect(matches).toHaveLength(1)
      expect(matches[0].positions).toHaveLength(3)
      expect(matches[0].type).toBe('vertical')
    })

    it('should find L-shaped matches', () => {
      const board = [
        [1, 1, 1, 4, 5, 6],
        [2, 3, 1, 5, 6, 1],
        [3, 4, 1, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const matches = gameEngine.findMatches()
      expect(matches.length).toBeGreaterThan(0)
      const lMatch = matches.find(match => match.type === 'L')
      expect(lMatch).toBeDefined()
    })

    it('should find T-shaped matches', () => {
      const board = [
        [1, 1, 1, 4, 5, 6],
        [2, 1, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const matches = gameEngine.findMatches()
      expect(matches.length).toBeGreaterThan(0)
      const tMatch = matches.find(match => match.type === 'T')
      expect(tMatch).toBeDefined()
    })
  })

  describe('isValidMove', () => {
    it('should validate adjacent horizontal moves', () => {
      const board = [
        [1, 2, 3, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      expect(gameEngine.isValidMove(board, { row: 0, col: 0 }, { row: 0, col: 1 })).toBe(true)
      expect(gameEngine.isValidMove(board, { row: 0, col: 0 }, { row: 0, col: 2 })).toBe(false)
    })

    it('should validate adjacent vertical moves', () => {
      const board = [
        [1, 2, 3, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      expect(gameEngine.isValidMove(board, { row: 0, col: 0 }, { row: 1, col: 0 })).toBe(true)
      expect(gameEngine.isValidMove(board, { row: 0, col: 0 }, { row: 2, col: 0 })).toBe(false)
    })

    it('should only allow moves that create matches', () => {
      const board = [
        [1, 2, 1, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      // This move should create a match
      expect(gameEngine.isValidMove(board, { row: 0, col: 1 }, { row: 0, col: 2 })).toBe(true)
      
      // This move should not create a match
      expect(gameEngine.isValidMove(board, { row: 1, col: 0 }, { row: 1, col: 1 })).toBe(false)
    })
  })

  describe('applyGravity', () => {
    it('should make gems fall down', () => {
      const board = [
        [0, 2, 0, 4, 5, 6],
        [0, 0, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const result = gameEngine.applyGravityPublic()
      
      // Check that gems have fallen
      expect(result).toBe(true)
      // 检查重力应用后的棋盘状态
      const boardAfterGravity = gameEngine.getBoard()
      expect(boardAfterGravity[5][0]).toBe(3)
      expect(boardAfterGravity[4][0]).toBe(4)
      expect(boardAfterGravity[3][0]).toBe(5)
      expect(boardAfterGravity[2][0]).toBe(6)

      expect(boardAfterGravity[5][1]).toBe(2)
      expect(boardAfterGravity[4][1]).toBe(4)
      expect(boardAfterGravity[3][1]).toBe(5)
      expect(boardAfterGravity[2][1]).toBe(6)
      expect(boardAfterGravity[1][1]).toBe(1)
    })

    it('should fill empty spaces with new gems', () => {
      const board = [
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      gameEngine.applyGravityPublic()

      // Check that all positions are filled
      const boardAfterGravity = gameEngine.getBoard()
      boardAfterGravity.forEach(row => {
        row.forEach(gem => {
          expect(gem).toBeGreaterThan(0)
        })
      })
    })
  })

  describe('getHint', () => {
    it('should return a valid move hint', () => {
      const board = [
        [1, 2, 1, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const hint = gameEngine.getHintWithBoard()
      expect(hint).toBeDefined()
      
      if (hint) {
        expect(gameEngine.isValidMove(board, hint.from, hint.to)).toBe(true)
      }
    })

    it('should return null when no moves are available', () => {
      // Create a board with no possible moves
      const board = [
        [1, 2, 1, 2, 1, 2],
        [2, 1, 2, 1, 2, 1],
        [1, 2, 1, 2, 1, 2],
        [2, 1, 2, 1, 2, 1],
        [1, 2, 1, 2, 1, 2],
        [2, 1, 2, 1, 2, 1]
      ]
      
      gameEngine = new GameEngine(board, 6)
      const hint = gameEngine.getHintWithBoard()
      expect(hint).toBeNull()
    })
  })

  describe('shuffleBoard', () => {
    it('should shuffle the board while maintaining gem counts', () => {
      const board = [
        [1, 2, 3, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      const originalCounts = new Map<number, number>()
      board.forEach(row => {
        row.forEach(gem => {
          originalCounts.set(gem, (originalCounts.get(gem) || 0) + 1)
        })
      })
      
      const shuffled = gameEngine.shuffleBoard(board)
      
      const shuffledCounts = new Map<number, number>()
      shuffled.forEach(row => {
        row.forEach(gem => {
          shuffledCounts.set(gem, (shuffledCounts.get(gem) || 0) + 1)
        })
      })
      
      // Check that gem counts are preserved
      expect(shuffledCounts).toEqual(originalCounts)
    })

    it('should not have initial matches after shuffle', () => {
      const board = [
        [1, 1, 1, 4, 5, 6],
        [2, 3, 4, 5, 6, 1],
        [3, 4, 5, 6, 1, 2],
        [4, 5, 6, 1, 2, 3],
        [5, 6, 1, 2, 3, 4],
        [6, 1, 2, 3, 4, 5]
      ]
      
      const shuffled = gameEngine.shuffleBoard(board)
      gameEngine = new GameEngine(shuffled, 6)
      const matches = gameEngine.findMatches()
      expect(matches).toHaveLength(0)
    })
  })

  describe('calculateScore', () => {
    it('should calculate score based on match size', () => {
      const matches = [
        {
          positions: [{ row: 0, col: 0 }, { row: 0, col: 1 }, { row: 0, col: 2 }],
          type: 'horizontal' as const,
          score: 30
        }
      ]

      const score = gameEngine.calculateScore(matches, 1)
      expect(score).toBeGreaterThan(0)
    })

    it('should give bonus for larger matches', () => {
      const smallMatch = [
        {
          positions: [{ row: 0, col: 0 }, { row: 0, col: 1 }, { row: 0, col: 2 }],
          type: 'horizontal' as const,
          score: 30
        }
      ]

      const largeMatch = [
        {
          positions: [
            { row: 0, col: 0 }, { row: 0, col: 1 }, { row: 0, col: 2 },
            { row: 0, col: 3 }, { row: 0, col: 4 }
          ],
          type: 'horizontal' as const,
          score: 50
        }
      ]

      const smallScore = gameEngine.calculateScore(smallMatch, 1)
      const largeScore = gameEngine.calculateScore(largeMatch, 1)
      
      expect(largeScore).toBeGreaterThan(smallScore)
    })

    it('should give combo bonus', () => {
      const matches = [
        {
          positions: [{ row: 0, col: 0 }, { row: 0, col: 1 }, { row: 0, col: 2 }],
          type: 'horizontal' as const,
          score: 30
        }
      ]

      const score1 = gameEngine.calculateScore(matches, 1)
      const score2 = gameEngine.calculateScore(matches, 2)
      
      expect(score2).toBeGreaterThan(score1)
    })
  })
})
