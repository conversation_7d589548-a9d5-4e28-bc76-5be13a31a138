import React, { useRef, useEffect, useState, useCallback } from 'react'
import styled from 'styled-components'
import { useGame } from '../../contexts/GameContext'
import { useGameAudio } from '../../contexts/AudioContext'
import { AnimationManager } from '../../utils/animationManager'
// import { SpecialGemManager } from '../../utils/specialGemManager'
import { ComboEffect } from '../../types/specialGems'
import { globalPerformanceMonitor } from '../../utils/performanceMonitor'
import ComboDisplay from './ComboDisplay'

const BoardContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 10px;
    gap: 15px;
  }

  @media (max-width: 480px) {
    padding: 5px;
    gap: 10px;
  }
`

const CanvasContainer = styled.div`
  position: relative;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 10px;
  width: 100%;
  max-width: 480px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    padding: 8px;
    border-radius: 8px;
  }

  @media (max-width: 480px) {
    padding: 5px;
    border-radius: 6px;
    max-width: 90vw;
  }
`

const GameCanvas = styled.canvas`
  border-radius: 8px;
  cursor: pointer;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  width: 100% !important;
  height: 100% !important;
  max-width: 100%;
  max-height: 100%;
  touch-action: none; /* 防止触摸时的默认行为 */
  user-select: none; /* 防止选择 */

  @media (max-width: 768px) {
    border-radius: 6px;
  }

  @media (max-width: 480px) {
    border-radius: 4px;
  }
`

const GameInfo = styled.div`
  display: flex;
  gap: 30px;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 20px;
    font-size: 16px;
  }

  @media (max-width: 480px) {
    gap: 15px;
    font-size: 14px;
    justify-content: space-around;
  }
`

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  min-width: 80px;

  .label {
    font-size: 14px;
    opacity: 0.8;
  }

  .value {
    font-size: 24px;
    color: #fbbf24;
  }

  @media (max-width: 768px) {
    min-width: 70px;
    gap: 3px;

    .label {
      font-size: 12px;
    }

    .value {
      font-size: 20px;
    }
  }

  @media (max-width: 480px) {
    min-width: 60px;
    gap: 2px;

    .label {
      font-size: 11px;
    }

    .value {
      font-size: 18px;
    }
  }
`

// 宝石颜色配置
const GEM_COLORS = {
  1: '#ef4444', // 红色
  2: '#3b82f6', // 蓝色
  3: '#10b981', // 绿色
  4: '#fbbf24', // 黄色
  5: '#8b5cf6', // 紫色
  6: '#f97316', // 橙色
}

interface Position {
  row: number
  col: number
}

interface GameBoardProps {
  size?: number
}

const GameBoard: React.FC<GameBoardProps> = ({ size = 6 }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const { gameState, makeMove } = useGame()
  const gameAudio = useGameAudio()
  const [selectedGem, setSelectedGem] = useState<Position | null>(null)
  const [isAnimating, setIsAnimating] = useState(false)

  // 特殊宝石系统
  // const [specialGemManager] = useState(() => new SpecialGemManager())
  // const [specialGems, setSpecialGems] = useState<SpecialGem[]>([])
  const [currentCombo, setCurrentCombo] = useState<ComboEffect | null>(null)
  const [comboCount, setComboCount] = useState(0)

  // 性能优化相关
  const [isDirty, setIsDirty] = useState(true)
  const offscreenCanvasRef = useRef<HTMLCanvasElement | null>(null)
  const [devicePixelRatio] = useState(() => window.devicePixelRatio || 1)
  const animationManagerRef = useRef<AnimationManager | null>(null)
  
  // 响应式计算单元格大小
  const [cellSize, setCellSize] = useState(60)
  const CANVAS_SIZE = size * cellSize

  // 响应式调整单元格大小
  useEffect(() => {
    const updateCellSize = () => {
      const screenWidth = window.innerWidth
      let newCellSize = 60

      if (screenWidth <= 480) {
        newCellSize = Math.min(50, (screenWidth * 0.9) / size)
      } else if (screenWidth <= 768) {
        newCellSize = Math.min(55, (screenWidth * 0.8) / size)
      }

      setCellSize(Math.max(30, newCellSize)) // 最小30px
    }

    updateCellSize()
    window.addEventListener('resize', updateCellSize)

    return () => window.removeEventListener('resize', updateCellSize)
  }, [size])

  // 初始化动画管理器
  useEffect(() => {
    if (!animationManagerRef.current) {
      animationManagerRef.current = new AnimationManager(() => {
        // 不需要在这里重新绘制，游戏循环会处理
      })
    }

    return () => {
      animationManagerRef.current?.clear()
    }
  }, [])
  
  // 绘制宝石
  const drawGem = useCallback((ctx: CanvasRenderingContext2D, type: number, x: number, y: number, isSelected: boolean = false) => {
    const centerX = x + cellSize / 2
    const centerY = y + cellSize / 2
    const radius = cellSize * 0.35
    
    // 绘制选中状态的光晕
    if (isSelected) {
      ctx.save()
      ctx.shadowColor = '#fbbf24'
      ctx.shadowBlur = 20
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius + 5, 0, Math.PI * 2)
      ctx.fillStyle = 'rgba(251, 191, 36, 0.3)'
      ctx.fill()
      ctx.restore()
    }
    
    // 绘制宝石主体
    ctx.save()
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)
    
    // 创建渐变效果
    const gradient = ctx.createRadialGradient(
      centerX - radius * 0.3, centerY - radius * 0.3, 0,
      centerX, centerY, radius
    )
    
    const color = GEM_COLORS[type as keyof typeof GEM_COLORS] || '#666666'
    gradient.addColorStop(0, color)
    gradient.addColorStop(0.7, color)
    gradient.addColorStop(1, '#000000')
    
    ctx.fillStyle = gradient
    ctx.fill()
    
    // 添加高光效果
    ctx.beginPath()
    ctx.arc(centerX - radius * 0.3, centerY - radius * 0.3, radius * 0.3, 0, Math.PI * 2)
    ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'
    ctx.fill()
    
    ctx.restore()
  }, [cellSize])

  // 获取离屏Canvas
  const getOffscreenCanvas = useCallback(() => {
    if (!offscreenCanvasRef.current) {
      const offscreenCanvas = document.createElement('canvas')
      offscreenCanvas.width = CANVAS_SIZE * devicePixelRatio
      offscreenCanvas.height = CANVAS_SIZE * devicePixelRatio
      const ctx = offscreenCanvas.getContext('2d')
      if (ctx) {
        ctx.scale(devicePixelRatio, devicePixelRatio)
      }
      offscreenCanvasRef.current = offscreenCanvas
    }
    return offscreenCanvasRef.current
  }, [CANVAS_SIZE, devicePixelRatio])

  // 渲染静态内容（背景和静态宝石）
  const renderStaticContent = useCallback((ctx: CanvasRenderingContext2D) => {
    // 绘制网格背景
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.lineWidth = 1

    // 批量绘制网格线
    ctx.beginPath()
    for (let i = 0; i <= size; i++) {
      // 垂直线
      ctx.moveTo(i * cellSize, 0)
      ctx.lineTo(i * cellSize, CANVAS_SIZE)
      // 水平线
      ctx.moveTo(0, i * cellSize)
      ctx.lineTo(CANVAS_SIZE, i * cellSize)
    }
    ctx.stroke()

    // 绘制静态宝石
    if (gameState.board) {
      for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
          const gemType = gameState.board[row][col]
          if (gemType > 0) {
            const isSelected = selectedGem?.row === row && selectedGem?.col === col
            drawGem(ctx, gemType, col * cellSize, row * cellSize, isSelected)
          }
        }
      }
    }
  }, [size, cellSize, CANVAS_SIZE, gameState.board, selectedGem, drawGem])

  // 优化的渲染函数
  const drawBoard = useCallback(() => {
    const renderStart = performance.now()

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 如果没有变化且不是动画帧，跳过渲染
    if (!isDirty && !animationManagerRef.current?.hasActiveAnimations()) {
      return
    }
    
    // 清空画布
    ctx.clearRect(0, 0, CANVAS_SIZE, CANVAS_SIZE)

    // 使用离屏Canvas进行预渲染（仅在静态内容变化时）
    const hasActiveAnimations = animationManagerRef.current?.hasActiveAnimations()

    if (!hasActiveAnimations && isDirty) {
      const offscreenCanvas = getOffscreenCanvas()
      const offscreenCtx = offscreenCanvas.getContext('2d')
      if (offscreenCtx) {
        offscreenCtx.clearRect(0, 0, CANVAS_SIZE, CANVAS_SIZE)
        renderStaticContent(offscreenCtx)
      }
      setIsDirty(false)
    }

    // 如果有离屏Canvas且没有动画，直接绘制
    if (offscreenCanvasRef.current && !hasActiveAnimations) {
      ctx.drawImage(offscreenCanvasRef.current, 0, 0)
    } else {
      // 直接渲染（用于动画帧）
      renderStaticContent(ctx)

      // 绘制动画中的宝石（跳过静态位置的宝石）
      if (hasActiveAnimations && gameState.board) {
        for (let row = 0; row < size; row++) {
          for (let col = 0; col < size; col++) {
            const gemType = gameState.board[row][col]
            if (gemType > 0) {
              // 检查是否有动画中的宝石在这个位置
              const hasAnimation = animationManagerRef.current?.getActiveAnimations()
                .some(anim => anim.from.row === row && anim.from.col === col)

              if (!hasAnimation) {
                const isSelected = selectedGem?.row === row && selectedGem?.col === col
                drawGem(ctx, gemType, col * cellSize, row * cellSize, isSelected)
              }
            }
          }
        }
      }
    }

    // 绘制动画中的宝石
    if (animationManagerRef.current) {
      const animations = animationManagerRef.current.getActiveAnimations()
      animations.forEach(animation => {
        const pos = animationManagerRef.current!.getInterpolatedPosition(animation)
        const x = pos.col * cellSize
        const y = pos.row * cellSize

        // 根据动画类型应用不同效果
        if (animation.type === 'eliminate') {
          const progress = animationManagerRef.current!.getAnimationProgress(animation.id)
          const scale = 1 - progress
          const alpha = 1 - progress

          ctx.save()
          ctx.globalAlpha = alpha
          ctx.translate(x + cellSize / 2, y + cellSize / 2)
          ctx.scale(scale, scale)
          ctx.translate(-cellSize / 2, -cellSize / 2)
          drawGem(ctx, animation.gemType, 0, 0)
          ctx.restore()
        } else {
          drawGem(ctx, animation.gemType, x, y)
        }
      })
    }

    // 绘制粒子效果
    if (animationManagerRef.current) {
      const particles = animationManagerRef.current.getParticles()
      particles.forEach(particle => {
        const alpha = particle.life / particle.maxLife
        ctx.save()
        ctx.globalAlpha = alpha
        ctx.fillStyle = particle.color
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()
        ctx.restore()
      })
    }

    // 性能监控
    const renderEnd = performance.now()
    const renderTime = renderEnd - renderStart
    globalPerformanceMonitor.recordFrame(renderTime, 0)
  }, [gameState.board, selectedGem, size, cellSize, CANVAS_SIZE, drawGem, isDirty, getOffscreenCanvas, renderStaticContent])
  
  // 获取触摸/点击位置
  const getPositionFromEvent = useCallback((clientX: number, clientY: number) => {
    const canvas = canvasRef.current
    if (!canvas) return null

    const rect = canvas.getBoundingClientRect()
    const x = clientX - rect.left
    const y = clientY - rect.top

    const col = Math.floor(x / cellSize)
    const row = Math.floor(y / cellSize)

    if (row < 0 || row >= size || col < 0 || col >= size) return null

    return { row, col }
  }, [cellSize, size])

  // 处理宝石选择逻辑
  const handleGemSelection = useCallback((position: Position) => {
    if (isAnimating || gameState.status !== 'playing') return
    
    if (!selectedGem) {
      // 选择第一个宝石
      if (gameState.board && gameState.board[position.row][position.col] > 0) {
        setSelectedGem(position)
        gameAudio.onGemSelect()
      }
    } else {
      // 处理第二次点击
      if (selectedGem.row === position.row && selectedGem.col === position.col) {
        // 取消选择
        setSelectedGem(null)
      } else {
        // 检查是否是相邻位置
        const isAdjacent =
          (Math.abs(selectedGem.row - position.row) === 1 && selectedGem.col === position.col) ||
          (Math.abs(selectedGem.col - position.col) === 1 && selectedGem.row === position.row)

        if (isAdjacent) {
          // 执行移动动画
          setIsAnimating(true)

          if (animationManagerRef.current && gameState.board) {
            const gem1 = {
              pos: selectedGem,
              type: gameState.board[selectedGem.row][selectedGem.col]
            }
            const gem2 = {
              pos: position,
              type: gameState.board[position.row][position.col]
            }

            // 播放交换音效
            gameAudio.onGemSwap()

            // 添加交换动画
            animationManagerRef.current.addSwapAnimation(
              gem1,
              gem2,
              300,
              () => {
                // 动画完成后执行实际移动
                makeMove(selectedGem, position, (matches) => {
                  // 如果有匹配，添加消除动画和音效
                  if (matches && matches.length > 0 && animationManagerRef.current) {
                    const positions = matches.flatMap(match => match.positions)
                    animationManagerRef.current.addEliminateAnimation(positions, 500)
                    gameAudio.onGemMatch()

                    // 检查是否有连击
                    if (matches.some(match => match.cascade)) {
                      gameAudio.onGemCascade()
                    }
                  }
                })
                  .finally(() => {
                    setIsAnimating(false)
                    setSelectedGem(null)
                  })
              }
            )
          } else {
            // 降级处理：直接执行移动
            makeMove(selectedGem, position)
              .finally(() => {
                setIsAnimating(false)
                setSelectedGem(null)
              })
          }
        } else {
          // 选择新的宝石
          if (gameState.board && gameState.board[position.row][position.col] > 0) {
            setSelectedGem(position)
            gameAudio.onGemSelect()
          } else {
            setSelectedGem(null)
          }
        }
      }
    }
  }, [selectedGem, isAnimating, gameState.status, gameState.board, size, cellSize, makeMove])

  // 处理鼠标点击事件
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const position = getPositionFromEvent(event.clientX, event.clientY)
    if (position) {
      handleGemSelection(position)
    }
  }, [getPositionFromEvent, handleGemSelection])

  // 处理触摸事件
  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {
    event.preventDefault() // 防止滚动
    const touch = event.touches[0]
    if (touch) {
      const position = getPositionFromEvent(touch.clientX, touch.clientY)
      if (position) {
        handleGemSelection(position)
      }
    }
  }, [getPositionFromEvent, handleGemSelection])

  // 防止触摸时的默认行为
  const handleTouchMove = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {
    event.preventDefault()
  }, [])

  const handleTouchEnd = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {
    event.preventDefault()
  }, [])
  
  // 监听状态变化，标记需要重新渲染
  useEffect(() => {
    setIsDirty(true)
  }, [gameState.board, selectedGem, size])

  // 游戏循环和性能监控
  useEffect(() => {
    globalPerformanceMonitor.startMonitoring()
    let animationId: number

    const gameLoop = () => {
      const frameStart = performance.now()

      if (animationManagerRef.current) {
        const updateStart = performance.now()
        animationManagerRef.current.update()
        const updateEnd = performance.now()

        drawBoard()
        const renderEnd = performance.now()

        // 记录性能数据：渲染时间和更新时间
        globalPerformanceMonitor.recordFrame(renderEnd - updateEnd, updateEnd - updateStart)
      } else {
        drawBoard()
        const renderEnd = performance.now()

        // 只记录渲染时间
        globalPerformanceMonitor.recordFrame(renderEnd - frameStart, 0)
      }

      animationId = requestAnimationFrame(gameLoop)
    }

    animationId = requestAnimationFrame(gameLoop)

    return () => {
      globalPerformanceMonitor.stopMonitoring()
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [drawBoard])

  // 设置canvas尺寸和高DPI支持
  useEffect(() => {
    const canvas = canvasRef.current
    if (canvas) {
      // 设置实际尺寸
      canvas.width = CANVAS_SIZE * devicePixelRatio
      canvas.height = CANVAS_SIZE * devicePixelRatio

      // 设置CSS尺寸
      canvas.style.width = `${CANVAS_SIZE}px`
      canvas.style.height = `${CANVAS_SIZE}px`

      // 缩放上下文以支持高DPI
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.scale(devicePixelRatio, devicePixelRatio)
      }

      setIsDirty(true)
    }
  }, [CANVAS_SIZE, devicePixelRatio])
  
  return (
    <BoardContainer>
      <GameInfo>
        <InfoItem>
          <div className="label">分数</div>
          <div className="value">{gameState.score.toLocaleString()}</div>
        </InfoItem>
        <InfoItem>
          <div className="label">剩余步数</div>
          <div className="value">{gameState.movesLeft}</div>
        </InfoItem>
        <InfoItem>
          <div className="label">目标</div>
          <div className="value">{gameState.targetScore?.toLocaleString() || 0}</div>
        </InfoItem>
      </GameInfo>
      
      <CanvasContainer>
        <GameCanvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          width={CANVAS_SIZE}
          height={CANVAS_SIZE}
        />
      </CanvasContainer>

      <ComboDisplay
        combo={currentCombo}
        comboCount={comboCount}
        onComboEnd={() => {
          setCurrentCombo(null)
          setComboCount(0)
        }}
      />
    </BoardContainer>
  )
}

export default GameBoard
