import { PerformanceMonitor, PerformanceOptimizer } from '../performanceMonitor'

// Mock performance.now()
const mockPerformanceNow = jest.fn()
Object.defineProperty(global, 'performance', {
  value: {
    now: mockPerformanceNow,
    memory: {
      usedJSHeapSize: 50 * 1024 * 1024 // 50MB
    }
  }
})

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor
  let performanceIssues: any[] = []

  beforeEach(() => {
    performanceIssues = []
    monitor = new PerformanceMonitor((issue) => {
      performanceIssues.push(issue)
    })
    mockPerformanceNow.mockClear()
  })

  describe('startMonitoring and stopMonitoring', () => {
    it('should start and stop monitoring', () => {
      expect(monitor['isMonitoring']).toBe(false)
      
      monitor.startMonitoring()
      expect(monitor['isMonitoring']).toBe(true)
      
      monitor.stopMonitoring()
      expect(monitor['isMonitoring']).toBe(false)
    })
  })

  describe('recordFrame', () => {
    beforeEach(() => {
      monitor.startMonitoring()
    })

    it('should record frame metrics', () => {
      mockPerformanceNow
        .mockReturnValueOnce(0)    // Initial frame time
        .mockReturnValueOnce(16.67) // Current frame time
        .mockReturnValueOnce(16.67) // FPS calculation time

      monitor.recordFrame(8, 4)

      const metrics = monitor.getCurrentMetrics()
      expect(metrics).toBeDefined()
      expect(metrics?.frameTime).toBe(16.67)
      expect(metrics?.renderTime).toBe(8)
      expect(metrics?.updateTime).toBe(4)
      expect(metrics?.totalTime).toBe(12)
    })

    it('should not record when not monitoring', () => {
      monitor.stopMonitoring()
      
      mockPerformanceNow.mockReturnValue(16.67)
      monitor.recordFrame(8, 4)

      const metrics = monitor.getCurrentMetrics()
      expect(metrics).toBeNull()
    })

    it('should detect low FPS issues', () => {
      // Simulate low FPS scenario
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(50)    // 50ms frame time = 20 FPS
        .mockReturnValueOnce(1000)  // 1 second later for FPS calculation

      monitor['frameCount'] = 20 // Simulate 20 frames in 1 second
      monitor.recordFrame(8, 4)

      expect(performanceIssues).toHaveLength(1)
      expect(performanceIssues[0].type).toBe('high_frame_time')
    })

    it('should detect high render time issues', () => {
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(16.67)
        .mockReturnValueOnce(16.67)

      monitor.recordFrame(25, 4) // High render time

      expect(performanceIssues).toHaveLength(1)
      expect(performanceIssues[0].type).toBe('high_render_time')
    })
  })

  describe('getAverageMetrics', () => {
    beforeEach(() => {
      monitor.startMonitoring()
    })

    it('should calculate average metrics over time window', () => {
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(16)
        .mockReturnValueOnce(16)
        .mockReturnValueOnce(32)
        .mockReturnValueOnce(32)
        .mockReturnValueOnce(48)
        .mockReturnValueOnce(48)

      monitor.recordFrame(8, 4)
      monitor.recordFrame(10, 6)
      monitor.recordFrame(12, 8)

      const averages = monitor.getAverageMetrics(1000)
      expect(averages).toBeDefined()
      expect(averages?.renderTime).toBe(10) // (8 + 10 + 12) / 3
      expect(averages?.updateTime).toBe(6)  // (4 + 6 + 8) / 3
    })

    it('should return null when no metrics available', () => {
      const averages = monitor.getAverageMetrics(1000)
      expect(averages).toBeNull()
    })
  })

  describe('getPerformanceReport', () => {
    beforeEach(() => {
      monitor.startMonitoring()
    })

    it('should generate performance report with recommendations', () => {
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(50) // High frame time
        .mockReturnValueOnce(50)

      monitor.recordFrame(25, 10) // High render and update times

      const report = monitor.getPerformanceReport()
      expect(report).toBeDefined()
      expect(report.current).toBeDefined()
      expect(report.recommendations.length).toBeGreaterThan(0)
    })
  })

  describe('clearMetrics', () => {
    it('should clear all recorded metrics', () => {
      monitor.startMonitoring()
      
      mockPerformanceNow
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(16)
        .mockReturnValueOnce(16)

      monitor.recordFrame(8, 4)
      expect(monitor.getCurrentMetrics()).toBeDefined()

      monitor.clearMetrics()
      expect(monitor.getCurrentMetrics()).toBeNull()
    })
  })
})

describe('PerformanceOptimizer', () => {
  describe('debounce', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('should debounce function calls', () => {
      const mockFn = jest.fn()
      const debouncedFn = PerformanceOptimizer.debounce(mockFn, 100)

      debouncedFn('arg1')
      debouncedFn('arg2')
      debouncedFn('arg3')

      expect(mockFn).not.toHaveBeenCalled()

      jest.advanceTimersByTime(100)

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('arg3')
    })
  })

  describe('throttle', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('should throttle function calls', () => {
      const mockFn = jest.fn()
      const throttledFn = PerformanceOptimizer.throttle(mockFn, 100)

      throttledFn('arg1')
      throttledFn('arg2')
      throttledFn('arg3')

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('arg1')

      jest.advanceTimersByTime(100)

      throttledFn('arg4')
      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenCalledWith('arg4')
    })
  })

  describe('createObjectPool', () => {
    it('should create and manage object pool', () => {
      const createFn = jest.fn(() => ({ value: 0 }))
      const resetFn = jest.fn((obj) => { obj.value = 0 })

      const pool = PerformanceOptimizer.createObjectPool(createFn, resetFn, 2)

      expect(createFn).toHaveBeenCalledTimes(2)
      expect(pool.size()).toBe(2)

      const obj1 = pool.get()
      expect(pool.size()).toBe(1)

      pool.get()
      expect(pool.size()).toBe(0)

      pool.get() // Should create new object
      expect(createFn).toHaveBeenCalledTimes(3)

      pool.release(obj1)
      expect(resetFn).toHaveBeenCalledWith(obj1)
      expect(pool.size()).toBe(1)
    })
  })

  describe('createBatchProcessor', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('should process items in batches', () => {
      const processFn = jest.fn()
      const processor = PerformanceOptimizer.createBatchProcessor(processFn, 3, 100)

      processor.add('item1')
      processor.add('item2')
      expect(processFn).not.toHaveBeenCalled()

      processor.add('item3') // Should trigger batch processing
      expect(processFn).toHaveBeenCalledTimes(1)
      expect(processFn).toHaveBeenCalledWith(['item1', 'item2', 'item3'])
    })

    it('should process items after delay', () => {
      const processFn = jest.fn()
      const processor = PerformanceOptimizer.createBatchProcessor(processFn, 5, 100)

      processor.add('item1')
      processor.add('item2')

      expect(processFn).not.toHaveBeenCalled()

      jest.advanceTimersByTime(100)

      expect(processFn).toHaveBeenCalledTimes(1)
      expect(processFn).toHaveBeenCalledWith(['item1', 'item2'])
    })

    it('should flush remaining items', () => {
      const processFn = jest.fn()
      const processor = PerformanceOptimizer.createBatchProcessor(processFn, 5, 100)

      processor.add('item1')
      processor.add('item2')
      processor.flush()

      expect(processFn).toHaveBeenCalledTimes(1)
      expect(processFn).toHaveBeenCalledWith(['item1', 'item2'])
    })
  })
})
