import React, { Suspense, useEffect, useState } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import styled from 'styled-components'

// 页面组件 - 立即加载的核心页面
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'

// 懒加载的页面组件
import {
  LazyGamePage,
  LazyLevelsPage,
  LazyLeaderboardPage,
  LazyAchievementsPage,
  LazyProfilePage,
  ComponentPreloader
} from './utils/lazyComponents'
import PerformancePanel from './components/Debug/PerformancePanel'

// 布局组件
import Header from './components/Layout/Header'
import Footer from './components/Layout/Footer'

// Context
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { GameProvider } from './contexts/GameContext'
import { AudioProvider } from './contexts/AudioContext'

const AppContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`

const LoadingScreen = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
`

const SuspenseLoader = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <LoadingScreen>
        <div>加载中...</div>
      </LoadingScreen>
    )
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// 公开路由组件（已登录用户重定向到首页）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <LoadingScreen>
        <div>加载中...</div>
      </LoadingScreen>
    )
  }

  if (user) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

// 主应用内容组件
const AppContent: React.FC = () => {
  const [showPerformancePanel, setShowPerformancePanel] = useState(false)

  return (
    <AppContainer>
      <Header />
      <MainContent>
        <Routes>
          {/* 公开路由 */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/register"
            element={
              <PublicRoute>
                <RegisterPage />
              </PublicRoute>
            }
          />

          {/* 受保护的路由 */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <HomePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/levels"
            element={
              <ProtectedRoute>
                <Suspense fallback={<SuspenseLoader><div className="spinner"></div><div>加载关卡...</div></SuspenseLoader>}>
                  <LazyLevelsPage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/game"
            element={
              <ProtectedRoute>
                <Suspense fallback={<SuspenseLoader><div className="spinner"></div><div>加载游戏...</div></SuspenseLoader>}>
                  <LazyGamePage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Suspense fallback={<SuspenseLoader><div className="spinner"></div><div>加载个人资料...</div></SuspenseLoader>}>
                  <LazyProfilePage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/leaderboard"
            element={
              <ProtectedRoute>
                <Suspense fallback={<SuspenseLoader><div className="spinner"></div><div>加载排行榜...</div></SuspenseLoader>}>
                  <LazyLeaderboardPage />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="/achievements"
            element={
              <ProtectedRoute>
                <Suspense fallback={<SuspenseLoader><div className="spinner"></div><div>加载成就...</div></SuspenseLoader>}>
                  <LazyAchievementsPage />
                </Suspense>
              </ProtectedRoute>
            }
          />

          {/* 404 重定向 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </MainContent>
      <Footer />

      {/* 开发环境性能监控面板 */}
      {process.env.NODE_ENV === 'development' && (
        <PerformancePanel
          isVisible={showPerformancePanel}
          onToggle={() => setShowPerformancePanel(!showPerformancePanel)}
        />
      )}
    </AppContainer>
  )
}

const App: React.FC = () => {
  // 组件预加载
  useEffect(() => {
    // 在空闲时预加载组件
    ComponentPreloader.preloadOnIdle()

    // 在用户交互时预加载游戏组件
    ComponentPreloader.preloadOnUserInteraction()

    // 清理函数
    return () => {
      ComponentPreloader.clearPreloadedComponents()
    }
  }, [])

  return (
    <AuthProvider>
      <AudioProvider>
        <GameProvider>
          <AppContent />
        </GameProvider>
      </AudioProvider>
    </AuthProvider>
  )
}

export default App
