const o={bomb:{id:"bomb",type:"bomb",name:"💣 炸弹",description:"消除选中位置周围3x3区域的所有宝石",icon:"💣",cost:100,uses:1,rarity:"common"},rainbow:{id:"rainbow",type:"rainbow",name:"🌈 彩虹宝石",description:"消除棋盘上所有与选中宝石相同颜色的宝石",icon:"🌈",cost:200,uses:1,rarity:"rare"},hammer:{id:"hammer",type:"hammer",name:"🔨 锤子",description:"直接消除选中的单个宝石",icon:"🔨",cost:50,uses:1,rarity:"common"},shuffle:{id:"shuffle",type:"shuffle",name:"🔄 洗牌",description:"重新随机排列棋盘上的所有宝石",icon:"🔄",cost:150,uses:1,rarity:"rare"},time_extend:{id:"time_extend",type:"time_extend",name:"⏰ 时间延长",description:"为当前关卡增加30秒游戏时间",icon:"⏰",cost:80,uses:1,rarity:"common"},score_boost:{id:"score_boost",type:"score_boost",name:"⭐ 分数加成",description:"接下来60秒内获得的分数翻倍",icon:"⭐",cost:120,duration:60,rarity:"rare"},hint:{id:"hint",type:"hint",name:"💡 提示",description:"高亮显示一个可能的移动组合",icon:"💡",cost:30,uses:1,rarity:"common"},freeze:{id:"freeze",type:"freeze",name:"❄️ 时间冰冻",description:"暂停时间流逝15秒",icon:"❄️",cost:180,duration:15,rarity:"epic"}},r={common:{color:"#9ca3af",bgColor:"rgba(156, 163, 175, 0.1)",borderColor:"rgba(156, 163, 175, 0.3)",name:"普通"},rare:{color:"#3b82f6",bgColor:"rgba(59, 130, 246, 0.1)",borderColor:"rgba(59, 130, 246, 0.3)",name:"稀有"},epic:{color:"#8b5cf6",bgColor:"rgba(139, 92, 246, 0.1)",borderColor:"rgba(139, 92, 246, 0.3)",name:"史诗"},legendary:{color:"#f59e0b",bgColor:"rgba(245, 158, 11, 0.1)",borderColor:"rgba(245, 158, 11, 0.3)",name:"传说"}};export{o as P,r as R};
//# sourceMappingURL=powerups-DNw9s1Qv.js.map
