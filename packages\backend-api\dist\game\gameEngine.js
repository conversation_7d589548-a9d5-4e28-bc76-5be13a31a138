"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameEngine = exports.GemType = void 0;
var GemType;
(function (GemType) {
    GemType[GemType["RED"] = 1] = "RED";
    GemType[GemType["BLUE"] = 2] = "BLUE";
    GemType[GemType["GREEN"] = 3] = "GREEN";
    GemType[GemType["YELLOW"] = 4] = "YELLOW";
    GemType[GemType["PURPLE"] = 5] = "PURPLE";
    GemType[GemType["ORANGE"] = 6] = "ORANGE";
    GemType[GemType["EMPTY"] = 0] = "EMPTY";
    GemType[GemType["OBSTACLE"] = -1] = "OBSTACLE";
})(GemType || (exports.GemType = GemType = {}));
class GameEngine {
    constructor(size = 6) {
        this.size = size;
        this.board = this.generateBoard();
    }
    generateBoard() {
        const board = [];
        for (let y = 0; y < this.size; y++) {
            board[y] = [];
            for (let x = 0; x < this.size; x++) {
                let gem;
                let attempts = 0;
                do {
                    gem = Math.floor(Math.random() * 6) + 1;
                    attempts++;
                } while (attempts < 10 && this.wouldCreateMatch(x, y, gem, board));
                board[y][x] = gem;
            }
        }
        return board;
    }
    wouldCreateMatch(x, y, gem, board) {
        let horizontalCount = 1;
        for (let i = x - 1; i >= 0 && board[y] && board[y][i] === gem; i--) {
            horizontalCount++;
        }
        for (let i = x + 1; i < this.size && board[y] && board[y][i] === gem; i++) {
            horizontalCount++;
        }
        if (horizontalCount >= 3)
            return true;
        let verticalCount = 1;
        for (let i = y - 1; i >= 0 && board[i] && board[i][x] === gem; i--) {
            verticalCount++;
        }
        for (let i = y + 1; i < this.size && board[i] && board[i][x] === gem; i++) {
            verticalCount++;
        }
        return verticalCount >= 3;
    }
    swapGems(from, to) {
        if (!this.areAdjacent(from, to)) {
            return false;
        }
        const temp = this.board[from.y][from.x];
        this.board[from.y][from.x] = this.board[to.y][to.x];
        this.board[to.y][to.x] = temp;
        const matches = this.findMatches();
        if (matches.length === 0) {
            this.board[to.y][to.x] = this.board[from.y][from.x];
            this.board[from.y][from.x] = temp;
            return false;
        }
        return true;
    }
    areAdjacent(pos1, pos2) {
        const dx = Math.abs(pos1.x - pos2.x);
        const dy = Math.abs(pos1.y - pos2.y);
        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }
    findMatches() {
        const matches = [];
        const visited = Array(this.size).fill(null).map(() => Array(this.size).fill(false));
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size - 2; x++) {
                if (visited[y][x])
                    continue;
                const gem = this.board[y][x];
                if (gem <= 0)
                    continue;
                const matchGems = [{ x, y }];
                for (let i = x + 1; i < this.size && this.board[y][i] === gem; i++) {
                    matchGems.push({ x: i, y });
                }
                if (matchGems.length >= 3) {
                    matches.push({
                        gems: matchGems,
                        type: 'horizontal',
                        score: this.calculateScore(matchGems.length)
                    });
                    matchGems.forEach(pos => visited[pos.y][pos.x] = true);
                }
            }
        }
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size - 2; y++) {
                if (visited[y][x])
                    continue;
                const gem = this.board[y][x];
                if (gem <= 0)
                    continue;
                const matchGems = [{ x, y }];
                for (let i = y + 1; i < this.size && this.board[i][x] === gem; i++) {
                    matchGems.push({ x, y: i });
                }
                if (matchGems.length >= 3) {
                    matches.push({
                        gems: matchGems,
                        type: 'vertical',
                        score: this.calculateScore(matchGems.length)
                    });
                    matchGems.forEach(pos => visited[pos.y][pos.x] = true);
                }
            }
        }
        return matches;
    }
    calculateScore(matchLength) {
        const baseScore = 10;
        return baseScore * matchLength * matchLength;
    }
    removeMatches(matches) {
        matches.forEach(match => {
            match.gems.forEach(pos => {
                this.board[pos.y][pos.x] = GemType.EMPTY;
            });
        });
    }
    applyGravity() {
        for (let x = 0; x < this.size; x++) {
            let writeIndex = this.size - 1;
            for (let y = this.size - 1; y >= 0; y--) {
                if (this.board[y][x] !== GemType.EMPTY) {
                    this.board[writeIndex][x] = this.board[y][x];
                    if (writeIndex !== y) {
                        this.board[y][x] = GemType.EMPTY;
                    }
                    writeIndex--;
                }
            }
        }
    }
    fillEmpty() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (this.board[y][x] === GemType.EMPTY) {
                    this.board[y][x] = Math.floor(Math.random() * 6) + 1;
                }
            }
        }
    }
    processCascade() {
        let totalMatches = [];
        let totalScore = 0;
        let cascadeCount = 0;
        while (true) {
            const matches = this.findMatches();
            if (matches.length === 0)
                break;
            this.removeMatches(matches);
            this.applyGravity();
            this.fillEmpty();
            const cascadeMultiplier = 1 + (cascadeCount * 0.5);
            matches.forEach(match => {
                match.score = Math.floor(match.score * cascadeMultiplier);
                totalScore += match.score;
            });
            totalMatches.push(...matches);
            cascadeCount++;
        }
        return { matches: totalMatches, totalScore };
    }
    getBoard() {
        return this.board.map(row => [...row]);
    }
    setBoard(board) {
        this.board = board.map(row => [...row]);
    }
    hasPossibleMoves() {
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size; x++) {
                if (x < this.size - 1) {
                    const temp = this.board[y][x];
                    this.board[y][x] = this.board[y][x + 1];
                    this.board[y][x + 1] = temp;
                    if (this.findMatches().length > 0) {
                        this.board[y][x + 1] = this.board[y][x];
                        this.board[y][x] = temp;
                        return true;
                    }
                    this.board[y][x + 1] = this.board[y][x];
                    this.board[y][x] = temp;
                }
                if (y < this.size - 1) {
                    const temp = this.board[y][x];
                    this.board[y][x] = this.board[y + 1][x];
                    this.board[y + 1][x] = temp;
                    if (this.findMatches().length > 0) {
                        this.board[y + 1][x] = this.board[y][x];
                        this.board[y][x] = temp;
                        return true;
                    }
                    this.board[y + 1][x] = this.board[y][x];
                    this.board[y][x] = temp;
                }
            }
        }
        return false;
    }
}
exports.GameEngine = GameEngine;
//# sourceMappingURL=gameEngine.js.map