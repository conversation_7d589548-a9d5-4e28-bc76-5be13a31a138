"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const database_1 = require("../database/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.userRoutes = router;
const db = database_1.Database.getInstance();
router.use(auth_1.authenticateToken);
router.get('/profile', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const user = await db.get(`SELECT id, username, email, display_name, avatar_url, coins, gems, 
            current_level, total_score, created_at, last_login 
     FROM users WHERE id = ?`, [userId]);
    if (!user) {
        throw (0, errorHandler_1.createError)(404, 'USER_002', '用户不存在');
    }
    res.json({
        success: true,
        data: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.display_name,
            avatarUrl: user.avatar_url,
            coins: user.coins,
            gems: user.gems,
            currentLevel: user.current_level,
            totalScore: user.total_score,
            createdAt: user.created_at,
            lastLogin: user.last_login
        }
    });
}));
const updateProfileSchema = joi_1.default.object({
    displayName: joi_1.default.string().max(50).optional(),
    avatarUrl: joi_1.default.string().uri().optional(),
    email: joi_1.default.string().email().optional()
});
router.put('/profile', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = updateProfileSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', error.details[0].message);
    }
    const userId = req.user.id;
    const { displayName, avatarUrl, email } = value;
    if (email) {
        const existingEmail = await db.get('SELECT id FROM users WHERE email = ? AND id != ?', [email, userId]);
        if (existingEmail) {
            throw (0, errorHandler_1.createError)(409, 'USER_003', '邮箱已被使用');
        }
    }
    const updates = [];
    const params = [];
    if (displayName !== undefined) {
        updates.push('display_name = ?');
        params.push(displayName);
    }
    if (avatarUrl !== undefined) {
        updates.push('avatar_url = ?');
        params.push(avatarUrl);
    }
    if (email !== undefined) {
        updates.push('email = ?');
        params.push(email);
    }
    if (updates.length === 0) {
        throw (0, errorHandler_1.createError)(400, 'VALIDATION_ERROR', '没有提供要更新的字段');
    }
    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(userId);
    await db.run(`UPDATE users SET ${updates.join(', ')} WHERE id = ?`, params);
    const updatedUser = await db.get(`SELECT id, username, email, display_name, avatar_url, coins, gems, 
            current_level, total_score 
     FROM users WHERE id = ?`, [userId]);
    res.json({
        success: true,
        data: {
            id: updatedUser.id,
            username: updatedUser.username,
            email: updatedUser.email,
            displayName: updatedUser.display_name,
            avatarUrl: updatedUser.avatar_url,
            coins: updatedUser.coins,
            gems: updatedUser.gems,
            currentLevel: updatedUser.current_level,
            totalScore: updatedUser.total_score
        },
        message: '用户信息更新成功'
    });
}));
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const stats = await db.get(`SELECT 
       COUNT(*) as totalLevelsPlayed,
       COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completedLevels,
       MAX(level_number) as highestLevel,
       SUM(best_score) as totalScore,
       AVG(stars) as averageStars
     FROM user_progress WHERE user_id = ?`, [userId]);
    const recentGames = await db.all(`SELECT level_number, best_score, stars, last_played_at 
     FROM user_progress 
     WHERE user_id = ? 
     ORDER BY last_played_at DESC 
     LIMIT 10`, [userId]);
    res.json({
        success: true,
        data: {
            totalLevelsPlayed: stats.totalLevelsPlayed || 0,
            completedLevels: stats.completedLevels || 0,
            highestLevel: stats.highestLevel || 0,
            totalScore: stats.totalScore || 0,
            averageStars: parseFloat((stats.averageStars || 0).toFixed(2)),
            recentGames: recentGames.map(game => ({
                levelNumber: game.level_number,
                bestScore: game.best_score,
                stars: game.stars,
                lastPlayedAt: game.last_played_at
            }))
        }
    });
}));
//# sourceMappingURL=user.js.map