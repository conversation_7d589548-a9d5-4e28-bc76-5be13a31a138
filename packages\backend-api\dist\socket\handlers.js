"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSocketHandlers = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const setupSocketHandlers = (io) => {
    io.use((socket, next) => {
        const token = socket.handshake.auth.token;
        if (!token) {
            return next(new Error('Authentication error'));
        }
        try {
            const secret = process.env.JWT_SECRET;
            if (!secret) {
                return next(new Error('JWT secret not configured'));
            }
            const decoded = jsonwebtoken_1.default.verify(token, secret);
            socket.userId = decoded.id;
            socket.username = decoded.username;
            next();
        }
        catch (err) {
            next(new Error('Authentication error'));
        }
    });
    io.on('connection', (socket) => {
        console.log(`用户 ${socket.username} (${socket.userId}) 已连接`);
        socket.join(`user_${socket.userId}`);
        socket.on('sync_game_state', (data) => {
            socket.to(`user_${socket.userId}`).emit('game_state_updated', data);
        });
        socket.on('join_leaderboard', (levelNumber) => {
            socket.join(`leaderboard_${levelNumber}`);
        });
        socket.on('leave_leaderboard', (levelNumber) => {
            socket.leave(`leaderboard_${levelNumber}`);
        });
        socket.on('disconnect', () => {
            console.log(`用户 ${socket.username} (${socket.userId}) 已断开连接`);
        });
    });
    const broadcastNewHighScore = (levelNumber, userInfo, score) => {
        io.to(`leaderboard_${levelNumber}`).emit('new_high_score', {
            levelNumber,
            user: userInfo,
            score,
            timestamp: new Date().toISOString()
        });
    };
    const broadcastSystemNotification = (message, type = 'info') => {
        io.emit('system_notification', {
            message,
            type,
            timestamp: new Date().toISOString()
        });
    };
    return {
        broadcastNewHighScore,
        broadcastSystemNotification
    };
};
exports.setupSocketHandlers = setupSocketHandlers;
//# sourceMappingURL=handlers.js.map