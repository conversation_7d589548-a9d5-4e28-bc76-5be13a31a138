if(!self.define){let s,e={};const l=(l,r)=>(l=new URL(l+".js",r).href,e[l]||new Promise(e=>{if("document"in self){const s=document.createElement("script");s.src=l,s.onload=e,document.head.appendChild(s)}else s=l,importScripts(l),e()}).then(()=>{let s=e[l];if(!s)throw new Error(`Module ${l} didn’t register its module`);return s}));self.define=(r,i)=>{const n=s||("document"in self?document.currentScript.src:"")||location.href;if(e[n])return;let a={};const u=s=>l(s,n),o={module:{uri:n},exports:a,require:u};e[n]=Promise.all(r.map(s=>o[s]||u(s))).then(s=>(i(...s),a))}}define(["./workbox-09c47557"],function(s){"use strict";self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"assets/achievementManager-Bt3BvN3k.js",revision:null},{url:"assets/achievements-BKGiRpq5.js",revision:null},{url:"assets/AchievementsPage-BeJEZHzx.js",revision:null},{url:"assets/animationManager-DTsUvCq5.js",revision:null},{url:"assets/Button-BlkTGlvm.js",revision:null},{url:"assets/Card-B65VmGcU.js",revision:null},{url:"assets/GameBoard-DvXOxAln.js",revision:null},{url:"assets/GameOverModal-4jLpsjyZ.js",revision:null},{url:"assets/GamePage-h55HzyeI.js",revision:null},{url:"assets/index-CNCEp3EQ.js",revision:null},{url:"assets/leaderboard-Or32oZ16.js",revision:null},{url:"assets/LeaderboardPage-x048hut8.js",revision:null},{url:"assets/LevelsPage-D220u2A8.js",revision:null},{url:"assets/powerUpManager-DAya6dWG.js",revision:null},{url:"assets/PowerUpPanel-Cy3BrM2g.js",revision:null},{url:"assets/powerups-DNw9s1Qv.js",revision:null},{url:"assets/ProfilePage-CsLxkWtu.js",revision:null},{url:"assets/router-DMCr7QLp.js",revision:null},{url:"assets/ShareModal-GIcMGaE5.js",revision:null},{url:"assets/specialGemManager-S1dnzDs3.js",revision:null},{url:"assets/specialGems-xbtj_zef.js",revision:null},{url:"assets/ui-ldAE8JkK.js",revision:null},{url:"assets/utils-l0sNRNKZ.js",revision:null},{url:"assets/vendor-Dneogk0_.js",revision:null},{url:"favicon.ico",revision:"3accab99ae8ff475e03148f47650621c"},{url:"index.html",revision:"323a88f7e981d76e5558debad8f7b1b9"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"favicon.ico",revision:"3accab99ae8ff475e03148f47650621c"},{url:"manifest.webmanifest",revision:"9cddc3465594cb2d95cfe94f965a5e71"}],{}),s.cleanupOutdatedCaches(),s.registerRoute(new s.NavigationRoute(s.createHandlerBoundToURL("index.html"))),s.registerRoute(/^https:\/\/api\./,new s.NetworkFirst({cacheName:"api-cache",plugins:[new s.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET")});
//# sourceMappingURL=sw.js.map
