{"version": 3, "file": "GameBoard-DvXOxAln.js", "sources": ["../../src/utils/performanceMonitor.ts", "../../src/components/Game/ComboDisplay.tsx", "../../src/components/Game/GameBoard.tsx"], "sourcesContent": ["export interface PerformanceMetrics {\n  fps: number\n  frameTime: number\n  memoryUsage: number\n  renderTime: number\n  updateTime: number\n  totalTime: number\n}\n\nexport interface PerformanceEntry {\n  timestamp: number\n  metrics: PerformanceMetrics\n}\n\nexport class PerformanceMonitor {\n  private metrics: PerformanceEntry[] = []\n  private lastFrameTime = 0\n  private frameCount = 0\n  private fpsUpdateInterval = 1000 // 1秒更新一次FPS\n  private lastFpsUpdate = 0\n  private currentFps = 0\n  private isMonitoring = false\n  \n  // 性能阈值\n  private readonly thresholds = {\n    minFps: 30,\n    maxFrameTime: 33.33, // 30fps = 33.33ms per frame\n    maxMemoryUsage: 100 * 1024 * 1024, // 100MB\n    maxRenderTime: 16.67 // 60fps = 16.67ms per frame\n  }\n\n  private onPerformanceIssue?: (issue: PerformanceIssue) => void\n\n  constructor(onPerformanceIssue?: (issue: PerformanceIssue) => void) {\n    this.onPerformanceIssue = onPerformanceIssue\n  }\n\n  public startMonitoring(): void {\n    this.isMonitoring = true\n    this.lastFrameTime = performance.now()\n    this.lastFpsUpdate = performance.now()\n    this.frameCount = 0\n  }\n\n  public stopMonitoring(): void {\n    this.isMonitoring = false\n  }\n\n  public recordFrame(renderTime: number = 0, updateTime: number = 0): void {\n    if (!this.isMonitoring) return\n\n    const now = performance.now()\n    const frameTime = now - this.lastFrameTime\n    this.lastFrameTime = now\n    this.frameCount++\n\n    // 更新FPS\n    if (now - this.lastFpsUpdate >= this.fpsUpdateInterval) {\n      this.currentFps = (this.frameCount * 1000) / (now - this.lastFpsUpdate)\n      this.frameCount = 0\n      this.lastFpsUpdate = now\n    }\n\n    // 获取内存使用情况\n    const memoryUsage = this.getMemoryUsage()\n\n    const metrics: PerformanceMetrics = {\n      fps: this.currentFps,\n      frameTime,\n      memoryUsage,\n      renderTime,\n      updateTime,\n      totalTime: renderTime + updateTime\n    }\n\n    // 记录性能数据\n    this.metrics.push({\n      timestamp: now,\n      metrics\n    })\n\n    // 保持最近1000个记录\n    if (this.metrics.length > 1000) {\n      this.metrics.shift()\n    }\n\n    // 检查性能问题\n    this.checkPerformanceIssues(metrics)\n  }\n\n  private getMemoryUsage(): number {\n    if ('memory' in performance) {\n      const memory = (performance as any).memory\n      return memory.usedJSHeapSize || 0\n    }\n    return 0\n  }\n\n  private checkPerformanceIssues(metrics: PerformanceMetrics): void {\n    const issues: PerformanceIssue[] = []\n\n    if (metrics.fps < this.thresholds.minFps) {\n      issues.push({\n        type: 'low_fps',\n        severity: 'warning',\n        message: `FPS过低: ${metrics.fps.toFixed(1)}`,\n        value: metrics.fps,\n        threshold: this.thresholds.minFps\n      })\n    }\n\n    if (metrics.frameTime > this.thresholds.maxFrameTime) {\n      issues.push({\n        type: 'high_frame_time',\n        severity: 'warning',\n        message: `帧时间过长: ${metrics.frameTime.toFixed(2)}ms`,\n        value: metrics.frameTime,\n        threshold: this.thresholds.maxFrameTime\n      })\n    }\n\n    if (metrics.memoryUsage > this.thresholds.maxMemoryUsage) {\n      issues.push({\n        type: 'high_memory_usage',\n        severity: 'error',\n        message: `内存使用过高: ${(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`,\n        value: metrics.memoryUsage,\n        threshold: this.thresholds.maxMemoryUsage\n      })\n    }\n\n    if (metrics.renderTime > this.thresholds.maxRenderTime) {\n      issues.push({\n        type: 'high_render_time',\n        severity: 'warning',\n        message: `渲染时间过长: ${metrics.renderTime.toFixed(2)}ms`,\n        value: metrics.renderTime,\n        threshold: this.thresholds.maxRenderTime\n      })\n    }\n\n    // 报告性能问题\n    issues.forEach(issue => {\n      if (this.onPerformanceIssue) {\n        this.onPerformanceIssue(issue)\n      }\n    })\n  }\n\n  public getAverageMetrics(timeWindow: number = 5000): PerformanceMetrics | null {\n    const now = performance.now()\n    const recentMetrics = this.metrics.filter(\n      entry => now - entry.timestamp <= timeWindow\n    )\n\n    if (recentMetrics.length === 0) return null\n\n    const sum = recentMetrics.reduce(\n      (acc, entry) => ({\n        fps: acc.fps + entry.metrics.fps,\n        frameTime: acc.frameTime + entry.metrics.frameTime,\n        memoryUsage: acc.memoryUsage + entry.metrics.memoryUsage,\n        renderTime: acc.renderTime + entry.metrics.renderTime,\n        updateTime: acc.updateTime + entry.metrics.updateTime,\n        totalTime: acc.totalTime + entry.metrics.totalTime\n      }),\n      { fps: 0, frameTime: 0, memoryUsage: 0, renderTime: 0, updateTime: 0, totalTime: 0 }\n    )\n\n    const count = recentMetrics.length\n    return {\n      fps: sum.fps / count,\n      frameTime: sum.frameTime / count,\n      memoryUsage: sum.memoryUsage / count,\n      renderTime: sum.renderTime / count,\n      updateTime: sum.updateTime / count,\n      totalTime: sum.totalTime / count\n    }\n  }\n\n  public getCurrentMetrics(): PerformanceMetrics | null {\n    if (this.metrics.length === 0) return null\n    return this.metrics[this.metrics.length - 1].metrics\n  }\n\n  public getPerformanceReport(): PerformanceReport {\n    const recent = this.getAverageMetrics(5000)\n    const overall = this.getAverageMetrics(60000)\n\n    return {\n      current: this.getCurrentMetrics(),\n      recent,\n      overall,\n      recommendations: this.generateRecommendations(recent)\n    }\n  }\n\n  private generateRecommendations(metrics: PerformanceMetrics | null): string[] {\n    if (!metrics) return []\n\n    const recommendations: string[] = []\n\n    if (metrics.fps < 30) {\n      recommendations.push('考虑降低游戏质量设置或优化渲染逻辑')\n    }\n\n    if (metrics.renderTime > 16) {\n      recommendations.push('优化Canvas渲染，考虑使用离屏Canvas或减少绘制调用')\n    }\n\n    if (metrics.memoryUsage > 50 * 1024 * 1024) {\n      recommendations.push('检查内存泄漏，及时清理不需要的对象')\n    }\n\n    if (metrics.frameTime > 20) {\n      recommendations.push('优化游戏逻辑，考虑将复杂计算分帧处理')\n    }\n\n    return recommendations\n  }\n\n  public clearMetrics(): void {\n    this.metrics = []\n  }\n}\n\nexport interface PerformanceIssue {\n  type: 'low_fps' | 'high_frame_time' | 'high_memory_usage' | 'high_render_time'\n  severity: 'warning' | 'error'\n  message: string\n  value: number\n  threshold: number\n}\n\nexport interface PerformanceReport {\n  current: PerformanceMetrics | null\n  recent: PerformanceMetrics | null\n  overall: PerformanceMetrics | null\n  recommendations: string[]\n}\n\n// 性能优化工具函数\nexport class PerformanceOptimizer {\n  // 防抖函数\n  public static debounce<T extends (...args: any[]) => any>(\n    func: T,\n    wait: number\n  ): (...args: Parameters<T>) => void {\n    let timeout: NodeJS.Timeout\n    return (...args: Parameters<T>) => {\n      clearTimeout(timeout)\n      timeout = setTimeout(() => func.apply(this, args), wait)\n    }\n  }\n\n  // 节流函数\n  public static throttle<T extends (...args: any[]) => any>(\n    func: T,\n    limit: number\n  ): (...args: Parameters<T>) => void {\n    let inThrottle: boolean\n    return (...args: Parameters<T>) => {\n      if (!inThrottle) {\n        func.apply(this, args)\n        inThrottle = true\n        setTimeout(() => inThrottle = false, limit)\n      }\n    }\n  }\n\n  // 对象池\n  public static createObjectPool<T>(\n    createFn: () => T,\n    resetFn: (obj: T) => void,\n    initialSize: number = 10\n  ) {\n    const pool: T[] = []\n    \n    // 初始化对象池\n    for (let i = 0; i < initialSize; i++) {\n      pool.push(createFn())\n    }\n\n    return {\n      get(): T {\n        return pool.pop() || createFn()\n      },\n      \n      release(obj: T): void {\n        resetFn(obj)\n        pool.push(obj)\n      },\n      \n      size(): number {\n        return pool.length\n      }\n    }\n  }\n\n  // 批处理执行\n  public static createBatchProcessor<T>(\n    processFn: (items: T[]) => void,\n    batchSize: number = 10,\n    delay: number = 16\n  ) {\n    let batch: T[] = []\n    let timeoutId: NodeJS.Timeout | null = null\n\n    const processBatch = () => {\n      if (batch.length > 0) {\n        processFn([...batch])\n        batch = []\n      }\n      timeoutId = null\n    }\n\n    return {\n      add(item: T): void {\n        batch.push(item)\n        \n        if (batch.length >= batchSize) {\n          if (timeoutId) {\n            clearTimeout(timeoutId)\n            timeoutId = null\n          }\n          processBatch()\n        } else if (!timeoutId) {\n          timeoutId = setTimeout(processBatch, delay)\n        }\n      },\n      \n      flush(): void {\n        if (timeoutId) {\n          clearTimeout(timeoutId)\n          timeoutId = null\n        }\n        processBatch()\n      }\n    }\n  }\n}\n\n// 全局性能监控实例\nexport const globalPerformanceMonitor = new PerformanceMonitor((issue) => {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(`性能问题: ${issue.message}`, issue)\n  }\n})\n", "import React, { useEffect, useState } from 'react'\nimport styled, { keyframes, css } from 'styled-components'\nimport { ComboEffect, COMBO_VISUALS } from '../../types/specialGems'\nimport { theme, media } from '../../styles/theme'\n\ninterface ComboDisplayProps {\n  combo: ComboEffect | null\n  comboCount: number\n  onComboEnd?: () => void\n}\n\nconst bounceIn = keyframes`\n  0% {\n    opacity: 0;\n    transform: scale(0.3) translateY(50px);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.2) translateY(-10px);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n`\n\nconst fadeOut = keyframes`\n  0% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  100% {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n`\n\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n`\n\nconst sparkle = keyframes`\n  0%, 100% {\n    opacity: 0;\n    transform: scale(0) rotate(0deg);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1) rotate(180deg);\n  }\n`\n\nconst Container = styled.div<{ $visible: boolean }>`\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: ${theme.zIndex.modal};\n  pointer-events: none;\n  \n  ${props => props.$visible ? css`\n    animation: ${bounceIn} 0.6s ease-out;\n  ` : css`\n    animation: ${fadeOut} 0.3s ease-in;\n    opacity: 0;\n  `}\n`\n\nconst ComboText = styled.div<{ \n  $comboCount: number\n  $color: string\n  $size: number\n}>`\n  font-size: ${props => props.$size}px;\n  font-weight: ${theme.fontWeights.bold};\n  color: ${props => props.$color};\n  text-align: center;\n  text-shadow: \n    0 0 10px ${props => props.$color}80,\n    0 0 20px ${props => props.$color}60,\n    0 0 30px ${props => props.$color}40;\n  font-family: ${theme.fonts.primary};\n  letter-spacing: 2px;\n  \n  ${props => props.$comboCount >= 5 && css`\n    animation: ${pulse} 0.5s ease-in-out infinite;\n  `}\n  \n  ${media.maxMd} {\n    font-size: ${props => Math.max(20, props.$size * 0.7)}px;\n  }\n  \n  ${media.maxSm} {\n    font-size: ${props => Math.max(16, props.$size * 0.5)}px;\n  }\n`\n\nconst MultiplierText = styled.div`\n  font-size: ${theme.fontSizes.lg};\n  color: ${theme.colors.secondary[400]};\n  text-align: center;\n  margin-top: ${theme.spacing[2]};\n  font-weight: ${theme.fontWeights.semibold};\n  text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.base};\n  }\n  \n  ${media.maxSm} {\n    font-size: ${theme.fontSizes.sm};\n  }\n`\n\nconst ScoreText = styled.div`\n  font-size: ${theme.fontSizes.xl};\n  color: ${theme.colors.success};\n  text-align: center;\n  margin-top: ${theme.spacing[1]};\n  font-weight: ${theme.fontWeights.bold};\n  text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.lg};\n  }\n  \n  ${media.maxSm} {\n    font-size: ${theme.fontSizes.base};\n  }\n`\n\nconst ParticleContainer = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 200px;\n  height: 200px;\n  pointer-events: none;\n`\n\nconst Particle = styled.div<{ \n  $delay: number\n  $x: number\n  $y: number\n  $color: string\n}>`\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background: ${props => props.$color};\n  border-radius: 50%;\n  left: ${props => props.$x}%;\n  top: ${props => props.$y}%;\n  animation: ${sparkle} 1s ease-in-out ${props => props.$delay}s infinite;\n  box-shadow: 0 0 10px ${props => props.$color};\n`\n\nconst ComboDisplay: React.FC<ComboDisplayProps> = ({\n  combo,\n  comboCount,\n  onComboEnd\n}) => {\n  const [visible, setVisible] = useState(false)\n  const [particles, setParticles] = useState<Array<{\n    id: number\n    x: number\n    y: number\n    delay: number\n    color: string\n  }>>([])\n\n  useEffect(() => {\n    if (combo && comboCount >= 2) {\n      setVisible(true)\n      \n      // 生成粒子效果\n      const newParticles = Array.from({ length: Math.min(comboCount * 2, 20) }, (_, i) => ({\n        id: i,\n        x: Math.random() * 100,\n        y: Math.random() * 100,\n        delay: Math.random() * 0.5,\n        color: getParticleColor(comboCount)\n      }))\n      setParticles(newParticles)\n      \n      // 自动隐藏\n      const timer = setTimeout(() => {\n        setVisible(false)\n        setTimeout(() => {\n          onComboEnd?.()\n        }, 300)\n      }, 2000)\n      \n      return () => clearTimeout(timer)\n    } else {\n      setVisible(false)\n    }\n  }, [combo, comboCount, onComboEnd])\n\n  const getComboVisual = (count: number) => {\n    const visuals = COMBO_VISUALS.text\n    const maxCombo = Math.max(...Object.keys(visuals).map(Number))\n    \n    if (count in visuals) {\n      return visuals[count as keyof typeof visuals]\n    }\n    \n    // 超过最大连击数时使用最大的样式\n    return visuals[maxCombo as keyof typeof visuals]\n  }\n\n  const getParticleColor = (count: number): string => {\n    if (count >= 10) return '#ffd700' // 金色\n    if (count >= 8) return '#ff6b6b'  // 红色\n    if (count >= 6) return '#9c27b0'  // 紫色\n    if (count >= 4) return '#ff9800'  // 橙色\n    if (count >= 3) return '#2196f3'  // 蓝色\n    return '#4caf50' // 绿色\n  }\n\n  if (!combo || comboCount < 2) {\n    return null\n  }\n\n  const visual = getComboVisual(comboCount)\n\n  return (\n    <Container $visible={visible}>\n      <ParticleContainer>\n        {particles.map(particle => (\n          <Particle\n            key={particle.id}\n            $delay={particle.delay}\n            $x={particle.x}\n            $y={particle.y}\n            $color={particle.color}\n          />\n        ))}\n      </ParticleContainer>\n      \n      <ComboText\n        $comboCount={comboCount}\n        $color={visual.color}\n        $size={visual.size}\n      >\n        {visual.text}\n      </ComboText>\n      \n      <MultiplierText>\n        {combo.multiplier.toFixed(1)}x 倍数\n      </MultiplierText>\n      \n      <ScoreText>\n        +{Math.round(combo.scoreBonus)}\n      </ScoreText>\n    </Container>\n  )\n}\n\nexport default ComboDisplay\n", "import React, { useRef, useEffect, useState, useCallback } from 'react'\nimport styled from 'styled-components'\nimport { useGame } from '../../contexts/GameContext'\nimport { useGameAudio } from '../../contexts/AudioContext'\nimport { AnimationManager } from '../../utils/animationManager'\n// import { SpecialGemManager } from '../../utils/specialGemManager'\nimport { ComboEffect } from '../../types/specialGems'\nimport { globalPerformanceMonitor } from '../../utils/performanceMonitor'\nimport ComboDisplay from './ComboDisplay'\n\nconst BoardContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  padding: 20px;\n  width: 100%;\n  max-width: 600px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: 10px;\n    gap: 15px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 5px;\n    gap: 10px;\n  }\n`\n\nconst CanvasContainer = styled.div`\n  position: relative;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\n  padding: 10px;\n  width: 100%;\n  max-width: 480px;\n  aspect-ratio: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  @media (max-width: 768px) {\n    padding: 8px;\n    border-radius: 8px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 5px;\n    border-radius: 6px;\n    max-width: 90vw;\n  }\n`\n\nconst GameCanvas = styled.canvas`\n  border-radius: 8px;\n  cursor: pointer;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);\n  width: 100% !important;\n  height: 100% !important;\n  max-width: 100%;\n  max-height: 100%;\n  touch-action: none; /* 防止触摸时的默认行为 */\n  user-select: none; /* 防止选择 */\n\n  @media (max-width: 768px) {\n    border-radius: 6px;\n  }\n\n  @media (max-width: 480px) {\n    border-radius: 4px;\n  }\n`\n\nconst GameInfo = styled.div`\n  display: flex;\n  gap: 30px;\n  align-items: center;\n  color: white;\n  font-size: 18px;\n  font-weight: 600;\n  width: 100%;\n  justify-content: center;\n  flex-wrap: wrap;\n\n  @media (max-width: 768px) {\n    gap: 20px;\n    font-size: 16px;\n  }\n\n  @media (max-width: 480px) {\n    gap: 15px;\n    font-size: 14px;\n    justify-content: space-around;\n  }\n`\n\nconst InfoItem = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 5px;\n  min-width: 80px;\n\n  .label {\n    font-size: 14px;\n    opacity: 0.8;\n  }\n\n  .value {\n    font-size: 24px;\n    color: #fbbf24;\n  }\n\n  @media (max-width: 768px) {\n    min-width: 70px;\n    gap: 3px;\n\n    .label {\n      font-size: 12px;\n    }\n\n    .value {\n      font-size: 20px;\n    }\n  }\n\n  @media (max-width: 480px) {\n    min-width: 60px;\n    gap: 2px;\n\n    .label {\n      font-size: 11px;\n    }\n\n    .value {\n      font-size: 18px;\n    }\n  }\n`\n\n// 宝石颜色配置\nconst GEM_COLORS = {\n  1: '#ef4444', // 红色\n  2: '#3b82f6', // 蓝色\n  3: '#10b981', // 绿色\n  4: '#fbbf24', // 黄色\n  5: '#8b5cf6', // 紫色\n  6: '#f97316', // 橙色\n}\n\ninterface Position {\n  row: number\n  col: number\n}\n\ninterface GameBoardProps {\n  size?: number\n}\n\nconst GameBoard: React.FC<GameBoardProps> = ({ size = 6 }) => {\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n  const { gameState, makeMove } = useGame()\n  const gameAudio = useGameAudio()\n  const [selectedGem, setSelectedGem] = useState<Position | null>(null)\n  const [isAnimating, setIsAnimating] = useState(false)\n\n  // 特殊宝石系统\n  // const [specialGemManager] = useState(() => new SpecialGemManager())\n  // const [specialGems, setSpecialGems] = useState<SpecialGem[]>([])\n  const [currentCombo, setCurrentCombo] = useState<ComboEffect | null>(null)\n  const [comboCount, setComboCount] = useState(0)\n\n  // 性能优化相关\n  const [isDirty, setIsDirty] = useState(true)\n  const offscreenCanvasRef = useRef<HTMLCanvasElement | null>(null)\n  const [devicePixelRatio] = useState(() => window.devicePixelRatio || 1)\n  const animationManagerRef = useRef<AnimationManager | null>(null)\n  \n  // 响应式计算单元格大小\n  const [cellSize, setCellSize] = useState(60)\n  const CANVAS_SIZE = size * cellSize\n\n  // 响应式调整单元格大小\n  useEffect(() => {\n    const updateCellSize = () => {\n      const screenWidth = window.innerWidth\n      let newCellSize = 60\n\n      if (screenWidth <= 480) {\n        newCellSize = Math.min(50, (screenWidth * 0.9) / size)\n      } else if (screenWidth <= 768) {\n        newCellSize = Math.min(55, (screenWidth * 0.8) / size)\n      }\n\n      setCellSize(Math.max(30, newCellSize)) // 最小30px\n    }\n\n    updateCellSize()\n    window.addEventListener('resize', updateCellSize)\n\n    return () => window.removeEventListener('resize', updateCellSize)\n  }, [size])\n\n  // 初始化动画管理器\n  useEffect(() => {\n    if (!animationManagerRef.current) {\n      animationManagerRef.current = new AnimationManager(() => {\n        // 不需要在这里重新绘制，游戏循环会处理\n      })\n    }\n\n    return () => {\n      animationManagerRef.current?.clear()\n    }\n  }, [])\n  \n  // 绘制宝石\n  const drawGem = useCallback((ctx: CanvasRenderingContext2D, type: number, x: number, y: number, isSelected: boolean = false) => {\n    const centerX = x + cellSize / 2\n    const centerY = y + cellSize / 2\n    const radius = cellSize * 0.35\n    \n    // 绘制选中状态的光晕\n    if (isSelected) {\n      ctx.save()\n      ctx.shadowColor = '#fbbf24'\n      ctx.shadowBlur = 20\n      ctx.beginPath()\n      ctx.arc(centerX, centerY, radius + 5, 0, Math.PI * 2)\n      ctx.fillStyle = 'rgba(251, 191, 36, 0.3)'\n      ctx.fill()\n      ctx.restore()\n    }\n    \n    // 绘制宝石主体\n    ctx.save()\n    ctx.beginPath()\n    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)\n    \n    // 创建渐变效果\n    const gradient = ctx.createRadialGradient(\n      centerX - radius * 0.3, centerY - radius * 0.3, 0,\n      centerX, centerY, radius\n    )\n    \n    const color = GEM_COLORS[type as keyof typeof GEM_COLORS] || '#666666'\n    gradient.addColorStop(0, color)\n    gradient.addColorStop(0.7, color)\n    gradient.addColorStop(1, '#000000')\n    \n    ctx.fillStyle = gradient\n    ctx.fill()\n    \n    // 添加高光效果\n    ctx.beginPath()\n    ctx.arc(centerX - radius * 0.3, centerY - radius * 0.3, radius * 0.3, 0, Math.PI * 2)\n    ctx.fillStyle = 'rgba(255, 255, 255, 0.4)'\n    ctx.fill()\n    \n    ctx.restore()\n  }, [cellSize])\n\n  // 获取离屏Canvas\n  const getOffscreenCanvas = useCallback(() => {\n    if (!offscreenCanvasRef.current) {\n      const offscreenCanvas = document.createElement('canvas')\n      offscreenCanvas.width = CANVAS_SIZE * devicePixelRatio\n      offscreenCanvas.height = CANVAS_SIZE * devicePixelRatio\n      const ctx = offscreenCanvas.getContext('2d')\n      if (ctx) {\n        ctx.scale(devicePixelRatio, devicePixelRatio)\n      }\n      offscreenCanvasRef.current = offscreenCanvas\n    }\n    return offscreenCanvasRef.current\n  }, [CANVAS_SIZE, devicePixelRatio])\n\n  // 渲染静态内容（背景和静态宝石）\n  const renderStaticContent = useCallback((ctx: CanvasRenderingContext2D) => {\n    // 绘制网格背景\n    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'\n    ctx.lineWidth = 1\n\n    // 批量绘制网格线\n    ctx.beginPath()\n    for (let i = 0; i <= size; i++) {\n      // 垂直线\n      ctx.moveTo(i * cellSize, 0)\n      ctx.lineTo(i * cellSize, CANVAS_SIZE)\n      // 水平线\n      ctx.moveTo(0, i * cellSize)\n      ctx.lineTo(CANVAS_SIZE, i * cellSize)\n    }\n    ctx.stroke()\n\n    // 绘制静态宝石\n    if (gameState.board) {\n      for (let row = 0; row < size; row++) {\n        for (let col = 0; col < size; col++) {\n          const gemType = gameState.board[row][col]\n          if (gemType > 0) {\n            const isSelected = selectedGem?.row === row && selectedGem?.col === col\n            drawGem(ctx, gemType, col * cellSize, row * cellSize, isSelected)\n          }\n        }\n      }\n    }\n  }, [size, cellSize, CANVAS_SIZE, gameState.board, selectedGem, drawGem])\n\n  // 优化的渲染函数\n  const drawBoard = useCallback(() => {\n    const renderStart = performance.now()\n\n    const canvas = canvasRef.current\n    if (!canvas) return\n\n    const ctx = canvas.getContext('2d')\n    if (!ctx) return\n\n    // 如果没有变化且不是动画帧，跳过渲染\n    if (!isDirty && !animationManagerRef.current?.hasActiveAnimations()) {\n      return\n    }\n    \n    // 清空画布\n    ctx.clearRect(0, 0, CANVAS_SIZE, CANVAS_SIZE)\n\n    // 使用离屏Canvas进行预渲染（仅在静态内容变化时）\n    const hasActiveAnimations = animationManagerRef.current?.hasActiveAnimations()\n\n    if (!hasActiveAnimations && isDirty) {\n      const offscreenCanvas = getOffscreenCanvas()\n      const offscreenCtx = offscreenCanvas.getContext('2d')\n      if (offscreenCtx) {\n        offscreenCtx.clearRect(0, 0, CANVAS_SIZE, CANVAS_SIZE)\n        renderStaticContent(offscreenCtx)\n      }\n      setIsDirty(false)\n    }\n\n    // 如果有离屏Canvas且没有动画，直接绘制\n    if (offscreenCanvasRef.current && !hasActiveAnimations) {\n      ctx.drawImage(offscreenCanvasRef.current, 0, 0)\n    } else {\n      // 直接渲染（用于动画帧）\n      renderStaticContent(ctx)\n\n      // 绘制动画中的宝石（跳过静态位置的宝石）\n      if (hasActiveAnimations && gameState.board) {\n        for (let row = 0; row < size; row++) {\n          for (let col = 0; col < size; col++) {\n            const gemType = gameState.board[row][col]\n            if (gemType > 0) {\n              // 检查是否有动画中的宝石在这个位置\n              const hasAnimation = animationManagerRef.current?.getActiveAnimations()\n                .some(anim => anim.from.row === row && anim.from.col === col)\n\n              if (!hasAnimation) {\n                const isSelected = selectedGem?.row === row && selectedGem?.col === col\n                drawGem(ctx, gemType, col * cellSize, row * cellSize, isSelected)\n              }\n            }\n          }\n        }\n      }\n    }\n\n    // 绘制动画中的宝石\n    if (animationManagerRef.current) {\n      const animations = animationManagerRef.current.getActiveAnimations()\n      animations.forEach(animation => {\n        const pos = animationManagerRef.current!.getInterpolatedPosition(animation)\n        const x = pos.col * cellSize\n        const y = pos.row * cellSize\n\n        // 根据动画类型应用不同效果\n        if (animation.type === 'eliminate') {\n          const progress = animationManagerRef.current!.getAnimationProgress(animation.id)\n          const scale = 1 - progress\n          const alpha = 1 - progress\n\n          ctx.save()\n          ctx.globalAlpha = alpha\n          ctx.translate(x + cellSize / 2, y + cellSize / 2)\n          ctx.scale(scale, scale)\n          ctx.translate(-cellSize / 2, -cellSize / 2)\n          drawGem(ctx, animation.gemType, 0, 0)\n          ctx.restore()\n        } else {\n          drawGem(ctx, animation.gemType, x, y)\n        }\n      })\n    }\n\n    // 绘制粒子效果\n    if (animationManagerRef.current) {\n      const particles = animationManagerRef.current.getParticles()\n      particles.forEach(particle => {\n        const alpha = particle.life / particle.maxLife\n        ctx.save()\n        ctx.globalAlpha = alpha\n        ctx.fillStyle = particle.color\n        ctx.beginPath()\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)\n        ctx.fill()\n        ctx.restore()\n      })\n    }\n\n    // 性能监控\n    const renderEnd = performance.now()\n    const renderTime = renderEnd - renderStart\n    globalPerformanceMonitor.recordFrame(renderTime, 0)\n  }, [gameState.board, selectedGem, size, cellSize, CANVAS_SIZE, drawGem, isDirty, getOffscreenCanvas, renderStaticContent])\n  \n  // 获取触摸/点击位置\n  const getPositionFromEvent = useCallback((clientX: number, clientY: number) => {\n    const canvas = canvasRef.current\n    if (!canvas) return null\n\n    const rect = canvas.getBoundingClientRect()\n    const x = clientX - rect.left\n    const y = clientY - rect.top\n\n    const col = Math.floor(x / cellSize)\n    const row = Math.floor(y / cellSize)\n\n    if (row < 0 || row >= size || col < 0 || col >= size) return null\n\n    return { row, col }\n  }, [cellSize, size])\n\n  // 处理宝石选择逻辑\n  const handleGemSelection = useCallback((position: Position) => {\n    if (isAnimating || gameState.status !== 'playing') return\n    \n    if (!selectedGem) {\n      // 选择第一个宝石\n      if (gameState.board && gameState.board[position.row][position.col] > 0) {\n        setSelectedGem(position)\n        gameAudio.onGemSelect()\n      }\n    } else {\n      // 处理第二次点击\n      if (selectedGem.row === position.row && selectedGem.col === position.col) {\n        // 取消选择\n        setSelectedGem(null)\n      } else {\n        // 检查是否是相邻位置\n        const isAdjacent =\n          (Math.abs(selectedGem.row - position.row) === 1 && selectedGem.col === position.col) ||\n          (Math.abs(selectedGem.col - position.col) === 1 && selectedGem.row === position.row)\n\n        if (isAdjacent) {\n          // 执行移动动画\n          setIsAnimating(true)\n\n          if (animationManagerRef.current && gameState.board) {\n            const gem1 = {\n              pos: selectedGem,\n              type: gameState.board[selectedGem.row][selectedGem.col]\n            }\n            const gem2 = {\n              pos: position,\n              type: gameState.board[position.row][position.col]\n            }\n\n            // 播放交换音效\n            gameAudio.onGemSwap()\n\n            // 添加交换动画\n            animationManagerRef.current.addSwapAnimation(\n              gem1,\n              gem2,\n              300,\n              () => {\n                // 动画完成后执行实际移动\n                makeMove(selectedGem, position, (matches) => {\n                  // 如果有匹配，添加消除动画和音效\n                  if (matches && matches.length > 0 && animationManagerRef.current) {\n                    const positions = matches.flatMap(match => match.positions)\n                    animationManagerRef.current.addEliminateAnimation(positions, 500)\n                    gameAudio.onGemMatch()\n\n                    // 检查是否有连击\n                    if (matches.some(match => match.cascade)) {\n                      gameAudio.onGemCascade()\n                    }\n                  }\n                })\n                  .finally(() => {\n                    setIsAnimating(false)\n                    setSelectedGem(null)\n                  })\n              }\n            )\n          } else {\n            // 降级处理：直接执行移动\n            makeMove(selectedGem, position)\n              .finally(() => {\n                setIsAnimating(false)\n                setSelectedGem(null)\n              })\n          }\n        } else {\n          // 选择新的宝石\n          if (gameState.board && gameState.board[position.row][position.col] > 0) {\n            setSelectedGem(position)\n            gameAudio.onGemSelect()\n          } else {\n            setSelectedGem(null)\n          }\n        }\n      }\n    }\n  }, [selectedGem, isAnimating, gameState.status, gameState.board, size, cellSize, makeMove])\n\n  // 处理鼠标点击事件\n  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {\n    const position = getPositionFromEvent(event.clientX, event.clientY)\n    if (position) {\n      handleGemSelection(position)\n    }\n  }, [getPositionFromEvent, handleGemSelection])\n\n  // 处理触摸事件\n  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {\n    event.preventDefault() // 防止滚动\n    const touch = event.touches[0]\n    if (touch) {\n      const position = getPositionFromEvent(touch.clientX, touch.clientY)\n      if (position) {\n        handleGemSelection(position)\n      }\n    }\n  }, [getPositionFromEvent, handleGemSelection])\n\n  // 防止触摸时的默认行为\n  const handleTouchMove = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {\n    event.preventDefault()\n  }, [])\n\n  const handleTouchEnd = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {\n    event.preventDefault()\n  }, [])\n  \n  // 监听状态变化，标记需要重新渲染\n  useEffect(() => {\n    setIsDirty(true)\n  }, [gameState.board, selectedGem, size])\n\n  // 游戏循环和性能监控\n  useEffect(() => {\n    globalPerformanceMonitor.startMonitoring()\n    let animationId: number\n\n    const gameLoop = () => {\n      const frameStart = performance.now()\n\n      if (animationManagerRef.current) {\n        const updateStart = performance.now()\n        animationManagerRef.current.update()\n        const updateEnd = performance.now()\n\n        drawBoard()\n        const renderEnd = performance.now()\n\n        // 记录性能数据：渲染时间和更新时间\n        globalPerformanceMonitor.recordFrame(renderEnd - updateEnd, updateEnd - updateStart)\n      } else {\n        drawBoard()\n        const renderEnd = performance.now()\n\n        // 只记录渲染时间\n        globalPerformanceMonitor.recordFrame(renderEnd - frameStart, 0)\n      }\n\n      animationId = requestAnimationFrame(gameLoop)\n    }\n\n    animationId = requestAnimationFrame(gameLoop)\n\n    return () => {\n      globalPerformanceMonitor.stopMonitoring()\n      if (animationId) {\n        cancelAnimationFrame(animationId)\n      }\n    }\n  }, [drawBoard])\n\n  // 设置canvas尺寸和高DPI支持\n  useEffect(() => {\n    const canvas = canvasRef.current\n    if (canvas) {\n      // 设置实际尺寸\n      canvas.width = CANVAS_SIZE * devicePixelRatio\n      canvas.height = CANVAS_SIZE * devicePixelRatio\n\n      // 设置CSS尺寸\n      canvas.style.width = `${CANVAS_SIZE}px`\n      canvas.style.height = `${CANVAS_SIZE}px`\n\n      // 缩放上下文以支持高DPI\n      const ctx = canvas.getContext('2d')\n      if (ctx) {\n        ctx.scale(devicePixelRatio, devicePixelRatio)\n      }\n\n      setIsDirty(true)\n    }\n  }, [CANVAS_SIZE, devicePixelRatio])\n  \n  return (\n    <BoardContainer>\n      <GameInfo>\n        <InfoItem>\n          <div className=\"label\">分数</div>\n          <div className=\"value\">{gameState.score.toLocaleString()}</div>\n        </InfoItem>\n        <InfoItem>\n          <div className=\"label\">剩余步数</div>\n          <div className=\"value\">{gameState.movesLeft}</div>\n        </InfoItem>\n        <InfoItem>\n          <div className=\"label\">目标</div>\n          <div className=\"value\">{gameState.targetScore?.toLocaleString() || 0}</div>\n        </InfoItem>\n      </GameInfo>\n      \n      <CanvasContainer>\n        <GameCanvas\n          ref={canvasRef}\n          onClick={handleCanvasClick}\n          onTouchStart={handleTouchStart}\n          onTouchMove={handleTouchMove}\n          onTouchEnd={handleTouchEnd}\n          width={CANVAS_SIZE}\n          height={CANVAS_SIZE}\n        />\n      </CanvasContainer>\n\n      <ComboDisplay\n        combo={currentCombo}\n        comboCount={comboCount}\n        onComboEnd={() => {\n          setCurrentCombo(null)\n          setComboCount(0)\n        }}\n      />\n    </BoardContainer>\n  )\n}\n\nexport default GameBoard\n"], "names": ["PerformanceMonitor", "onPerformanceIssue", "__publicField", "renderTime", "updateTime", "now", "frameTime", "memoryUsage", "metrics", "issues", "issue", "timeWindow", "recentMetrics", "entry", "sum", "acc", "count", "recent", "overall", "recommendations", "globalPerformanceMonitor", "bounceIn", "keyframes", "fadeOut", "pulse", "sparkle", "Container", "styled", "theme", "props", "css", "ComboText", "media", "MultiplierText", "ScoreText", "ParticleContainer", "Particle", "ComboDisplay", "combo", "comboCount", "onComboEnd", "visible", "setVisible", "useState", "particles", "setParticles", "useEffect", "newParticles", "_", "i", "getParticleColor", "timer", "getComboVisual", "visuals", "COMBO_VISUALS", "maxCombo", "visual", "jsxs", "jsx", "particle", "BoardContainer", "CanvasContainer", "GameCanvas", "GameInfo", "InfoItem", "GEM_COLORS", "GameBoard", "size", "canvasRef", "useRef", "gameState", "makeMove", "useGame", "gameAudio", "useGameAudio", "selected<PERSON>em", "setSelectedGem", "isAnimating", "setIsAnimating", "currentCombo", "setCurrentCombo", "setComboCount", "isDirty", "setIsDirty", "offscreenCanvasRef", "devicePixelRatio", "animationManagerRef", "cellSize", "setCellSize", "CANVAS_SIZE", "updateCellSize", "screenWidth", "newCellSize", "AnimationManager", "_a", "drawGem", "useCallback", "ctx", "type", "x", "y", "isSelected", "centerX", "centerY", "radius", "gradient", "color", "getOffscreenCanvas", "offscreenCanvas", "renderStaticContent", "row", "col", "gemType", "drawBoard", "renderStart", "canvas", "hasActiveAnimations", "_b", "offscreenCtx", "_c", "anim", "animation", "pos", "progress", "scale", "alpha", "getPositionFromEvent", "clientX", "clientY", "rect", "handleGemSelection", "position", "gem1", "gem2", "matches", "positions", "match", "handleCanvasClick", "event", "handleTouchStart", "touch", "handleTouchMove", "handleTouchEnd", "animationId", "gameLoop", "frameStart", "updateStart", "updateEnd", "renderEnd"], "mappings": "ydAcO,MAAMA,EAAmB,CAmB9B,YAAYC,EAAwD,CAlB5DC,EAAA,eAA8B,CAAA,GAC9BA,EAAA,qBAAgB,GAChBA,EAAA,kBAAa,GACbA,EAAA,yBAAoB,KACpBA,EAAA,qBAAgB,GAChBA,EAAA,kBAAa,GACbA,EAAA,oBAAe,IAGNA,EAAA,kBAAa,CAC5B,OAAQ,GACR,aAAc,MACd,eAAgB,IAAM,KAAO,KAC7B,cAAe,KAAA,GAGTA,EAAA,2BAGN,KAAK,mBAAqBD,CAC5B,CAEO,iBAAwB,CAC7B,KAAK,aAAe,GACpB,KAAK,cAAgB,YAAY,IAAA,EACjC,KAAK,cAAgB,YAAY,IAAA,EACjC,KAAK,WAAa,CACpB,CAEO,gBAAuB,CAC5B,KAAK,aAAe,EACtB,CAEO,YAAYE,EAAqB,EAAGC,EAAqB,EAAS,CACvE,GAAI,CAAC,KAAK,aAAc,OAExB,MAAMC,EAAM,YAAY,IAAA,EAClBC,EAAYD,EAAM,KAAK,cAC7B,KAAK,cAAgBA,EACrB,KAAK,aAGDA,EAAM,KAAK,eAAiB,KAAK,oBACnC,KAAK,WAAc,KAAK,WAAa,KAASA,EAAM,KAAK,eACzD,KAAK,WAAa,EAClB,KAAK,cAAgBA,GAIvB,MAAME,EAAc,KAAK,eAAA,EAEnBC,EAA8B,CAClC,IAAK,KAAK,WACV,UAAAF,EACA,YAAAC,EACA,WAAAJ,EACA,WAAAC,EACA,UAAWD,EAAaC,CAAA,EAI1B,KAAK,QAAQ,KAAK,CAChB,UAAWC,EACX,QAAAG,CAAA,CACD,EAGG,KAAK,QAAQ,OAAS,KACxB,KAAK,QAAQ,MAAA,EAIf,KAAK,uBAAuBA,CAAO,CACrC,CAEQ,gBAAyB,CAC/B,MAAI,WAAY,aACE,YAAoB,OACtB,gBAAkB,CAGpC,CAEQ,uBAAuBA,EAAmC,CAChE,MAAMC,EAA6B,CAAA,EAE/BD,EAAQ,IAAM,KAAK,WAAW,QAChCC,EAAO,KAAK,CACV,KAAM,UACN,SAAU,UACV,QAAS,UAAUD,EAAQ,IAAI,QAAQ,CAAC,CAAC,GACzC,MAAOA,EAAQ,IACf,UAAW,KAAK,WAAW,MAAA,CAC5B,EAGCA,EAAQ,UAAY,KAAK,WAAW,cACtCC,EAAO,KAAK,CACV,KAAM,kBACN,SAAU,UACV,QAAS,UAAUD,EAAQ,UAAU,QAAQ,CAAC,CAAC,KAC/C,MAAOA,EAAQ,UACf,UAAW,KAAK,WAAW,YAAA,CAC5B,EAGCA,EAAQ,YAAc,KAAK,WAAW,gBACxCC,EAAO,KAAK,CACV,KAAM,oBACN,SAAU,QACV,QAAS,YAAYD,EAAQ,YAAc,KAAO,MAAM,QAAQ,CAAC,CAAC,KAClE,MAAOA,EAAQ,YACf,UAAW,KAAK,WAAW,cAAA,CAC5B,EAGCA,EAAQ,WAAa,KAAK,WAAW,eACvCC,EAAO,KAAK,CACV,KAAM,mBACN,SAAU,UACV,QAAS,WAAWD,EAAQ,WAAW,QAAQ,CAAC,CAAC,KACjD,MAAOA,EAAQ,WACf,UAAW,KAAK,WAAW,aAAA,CAC5B,EAIHC,EAAO,QAAQC,GAAS,CAClB,KAAK,oBACP,KAAK,mBAAmBA,CAAK,CAEjC,CAAC,CACH,CAEO,kBAAkBC,EAAqB,IAAiC,CAC7E,MAAMN,EAAM,YAAY,IAAA,EAClBO,EAAgB,KAAK,QAAQ,OACjCC,GAASR,EAAMQ,EAAM,WAAaF,CAAA,EAGpC,GAAIC,EAAc,SAAW,EAAG,OAAO,KAEvC,MAAME,EAAMF,EAAc,OACxB,CAACG,EAAKF,KAAW,CACf,IAAKE,EAAI,IAAMF,EAAM,QAAQ,IAC7B,UAAWE,EAAI,UAAYF,EAAM,QAAQ,UACzC,YAAaE,EAAI,YAAcF,EAAM,QAAQ,YAC7C,WAAYE,EAAI,WAAaF,EAAM,QAAQ,WAC3C,WAAYE,EAAI,WAAaF,EAAM,QAAQ,WAC3C,UAAWE,EAAI,UAAYF,EAAM,QAAQ,SAAA,GAE3C,CAAE,IAAK,EAAG,UAAW,EAAG,YAAa,EAAG,WAAY,EAAG,WAAY,EAAG,UAAW,CAAA,CAAE,EAG/EG,EAAQJ,EAAc,OAC5B,MAAO,CACL,IAAKE,EAAI,IAAME,EACf,UAAWF,EAAI,UAAYE,EAC3B,YAAaF,EAAI,YAAcE,EAC/B,WAAYF,EAAI,WAAaE,EAC7B,WAAYF,EAAI,WAAaE,EAC7B,UAAWF,EAAI,UAAYE,CAAA,CAE/B,CAEO,mBAA+C,CACpD,OAAI,KAAK,QAAQ,SAAW,EAAU,KAC/B,KAAK,QAAQ,KAAK,QAAQ,OAAS,CAAC,EAAE,OAC/C,CAEO,sBAA0C,CAC/C,MAAMC,EAAS,KAAK,kBAAkB,GAAI,EACpCC,EAAU,KAAK,kBAAkB,GAAK,EAE5C,MAAO,CACL,QAAS,KAAK,kBAAA,EACd,OAAAD,EACA,QAAAC,EACA,gBAAiB,KAAK,wBAAwBD,CAAM,CAAA,CAExD,CAEQ,wBAAwBT,EAA8C,CAC5E,GAAI,CAACA,EAAS,MAAO,CAAA,EAErB,MAAMW,EAA4B,CAAA,EAElC,OAAIX,EAAQ,IAAM,IAChBW,EAAgB,KAAK,mBAAmB,EAGtCX,EAAQ,WAAa,IACvBW,EAAgB,KAAK,gCAAgC,EAGnDX,EAAQ,YAAc,GAAK,KAAO,MACpCW,EAAgB,KAAK,mBAAmB,EAGtCX,EAAQ,UAAY,IACtBW,EAAgB,KAAK,oBAAoB,EAGpCA,CACT,CAEO,cAAqB,CAC1B,KAAK,QAAU,CAAA,CACjB,CACF,CAuHO,MAAMC,EAA2B,IAAIpB,GAAoBU,GAAU,CAI1E,CAAC,EChVKW,GAAWC;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAeXC,GAAUD;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAWVE,GAAQF;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EASRG,GAAUH;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,EAWVI,GAAYC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,aAKZC,EAAM,OAAO,KAAK;AAAA;AAAA;AAAA,IAG3BC,GAASA,EAAM,SAAWC;AAAAA,iBACbT,EAAQ;AAAA,IACnBS;AAAAA,iBACWP,EAAO;AAAA;AAAA,GAErB;AAAA,EAGGQ,GAAYJ,EAAO;AAAA,eAKVE,GAASA,EAAM,KAAK;AAAA,iBAClBD,EAAM,YAAY,IAAI;AAAA,WAC5BC,GAASA,EAAM,MAAM;AAAA;AAAA;AAAA,eAGjBA,GAASA,EAAM,MAAM;AAAA,eACrBA,GAASA,EAAM,MAAM;AAAA,eACrBA,GAASA,EAAM,MAAM;AAAA,iBACnBD,EAAM,MAAM,OAAO;AAAA;AAAA;AAAA,IAGhCC,GAASA,EAAM,aAAe,GAAKC;AAAAA,iBACtBN,EAAK;AAAA,GACnB;AAAA;AAAA,IAECQ,EAAM,KAAK;AAAA,oBACW,KAAK,IAAI,GAAIH,EAAM,MAAQ,EAAG,CAAC;AAAA;AAAA;AAAA,IAGrDG,EAAM,KAAK;AAAA,oBACW,KAAK,IAAI,GAAIH,EAAM,MAAQ,EAAG,CAAC;AAAA;AAAA,EAInDI,GAAiBN,EAAO;AAAA,eACfC,EAAM,UAAU,EAAE;AAAA,WACtBA,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA;AAAA,gBAEtBA,EAAM,QAAQ,CAAC,CAAC;AAAA,iBACfA,EAAM,YAAY,QAAQ;AAAA;AAAA;AAAA,IAGvCI,EAAM,KAAK;AAAA,iBACEJ,EAAM,UAAU,IAAI;AAAA;AAAA;AAAA,IAGjCI,EAAM,KAAK;AAAA,iBACEJ,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BM,GAAYP,EAAO;AAAA,eACVC,EAAM,UAAU,EAAE;AAAA,WACtBA,EAAM,OAAO,OAAO;AAAA;AAAA,gBAEfA,EAAM,QAAQ,CAAC,CAAC;AAAA,iBACfA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA,IAGnCI,EAAM,KAAK;AAAA,iBACEJ,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA,IAG/BI,EAAM,KAAK;AAAA,iBACEJ,EAAM,UAAU,IAAI;AAAA;AAAA,EAI/BO,GAAoBR,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU3BS,GAAWT,EAAO;AAAA;AAAA;AAAA;AAAA,gBASRE,GAASA,EAAM,MAAM;AAAA;AAAA,UAE3BA,GAASA,EAAM,EAAE;AAAA,SAClBA,GAASA,EAAM,EAAE;AAAA,eACXJ,EAAO,mBAAmBI,GAASA,EAAM,MAAM;AAAA,yBACrCA,GAASA,EAAM,MAAM;AAAA,EAGxCQ,GAA4C,CAAC,CACjD,MAAAC,EACA,WAAAC,EACA,WAAAC,CACF,IAAM,CACJ,KAAM,CAACC,EAASC,CAAU,EAAIC,EAAAA,SAAS,EAAK,EACtC,CAACC,EAAWC,CAAY,EAAIF,EAAAA,SAM9B,CAAA,CAAE,EAENG,EAAAA,UAAU,IAAM,CACd,GAAIR,GAASC,GAAc,EAAG,CAC5BG,EAAW,EAAI,EAGf,MAAMK,EAAe,MAAM,KAAK,CAAE,OAAQ,KAAK,IAAIR,EAAa,EAAG,EAAE,CAAA,EAAK,CAACS,EAAGC,KAAO,CACnF,GAAIA,EACJ,EAAG,KAAK,OAAA,EAAW,IACnB,EAAG,KAAK,OAAA,EAAW,IACnB,MAAO,KAAK,OAAA,EAAW,GACvB,MAAOC,EAAiBX,CAAU,CAAA,EAClC,EACFM,EAAaE,CAAY,EAGzB,MAAMI,EAAQ,WAAW,IAAM,CAC7BT,EAAW,EAAK,EAChB,WAAW,IAAM,CACfF,GAAA,MAAAA,GACF,EAAG,GAAG,CACR,EAAG,GAAI,EAEP,MAAO,IAAM,aAAaW,CAAK,CACjC,MACET,EAAW,EAAK,CAEpB,EAAG,CAACJ,EAAOC,EAAYC,CAAU,CAAC,EAElC,MAAMY,EAAkBpC,GAAkB,CACxC,MAAMqC,EAAUC,GAAc,KACxBC,EAAW,KAAK,IAAI,GAAG,OAAO,KAAKF,CAAO,EAAE,IAAI,MAAM,CAAC,EAE7D,OAAIrC,KAASqC,EACJA,EAAQrC,CAA6B,EAIvCqC,EAAQE,CAAgC,CACjD,EAEML,EAAoBlC,GACpBA,GAAS,GAAW,UACpBA,GAAS,EAAU,UACnBA,GAAS,EAAU,UACnBA,GAAS,EAAU,UACnBA,GAAS,EAAU,UAChB,UAGT,GAAI,CAACsB,GAASC,EAAa,EACzB,OAAO,KAGT,MAAMiB,EAASJ,EAAeb,CAAU,EAExC,OACEkB,EAAAA,KAAC/B,GAAA,CAAU,SAAUe,EACnB,SAAA,CAAAiB,EAAAA,IAACvB,GAAA,CACE,SAAAS,EAAU,IAAIe,GACbD,EAAAA,IAACtB,GAAA,CAEC,OAAQuB,EAAS,MACjB,GAAIA,EAAS,EACb,GAAIA,EAAS,EACb,OAAQA,EAAS,KAAA,EAJZA,EAAS,EAAA,CAMjB,EACH,EAEAD,EAAAA,IAAC3B,GAAA,CACC,YAAaQ,EACb,OAAQiB,EAAO,MACf,MAAOA,EAAO,KAEb,SAAAA,EAAO,IAAA,CAAA,SAGTvB,GAAA,CACE,SAAA,CAAAK,EAAM,WAAW,QAAQ,CAAC,EAAE,MAAA,EAC/B,SAECJ,GAAA,CAAU,SAAA,CAAA,IACP,KAAK,MAAMI,EAAM,UAAU,CAAA,CAAA,CAC/B,CAAA,EACF,CAEJ,EC7PMsB,GAAiBjC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBxBkC,GAAkBlC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBzBmC,GAAanC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBpBoC,GAAWpC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBlBqC,EAAWrC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6ClBsC,GAAa,CACjB,EAAG,UACH,EAAG,UACH,EAAG,UACH,EAAG,UACH,EAAG,UACH,EAAG,SACL,EAWMC,GAAsC,CAAC,CAAE,KAAAC,EAAO,KAAQ,OAC5D,MAAMC,EAAYC,EAAAA,OAA0B,IAAI,EAC1C,CAAE,UAAAC,EAAW,SAAAC,CAAA,EAAaC,GAAA,EAC1BC,EAAYC,GAAA,EACZ,CAACC,EAAaC,CAAc,EAAIjC,EAAAA,SAA0B,IAAI,EAC9D,CAACkC,EAAaC,CAAc,EAAInC,EAAAA,SAAS,EAAK,EAK9C,CAACoC,EAAcC,CAAe,EAAIrC,EAAAA,SAA6B,IAAI,EACnE,CAACJ,EAAY0C,CAAa,EAAItC,EAAAA,SAAS,CAAC,EAGxC,CAACuC,EAASC,CAAU,EAAIxC,EAAAA,SAAS,EAAI,EACrCyC,EAAqBf,EAAAA,OAAiC,IAAI,EAC1D,CAACgB,CAAgB,EAAI1C,EAAAA,SAAS,IAAM,OAAO,kBAAoB,CAAC,EAChE2C,EAAsBjB,EAAAA,OAAgC,IAAI,EAG1D,CAACkB,EAAUC,CAAW,EAAI7C,EAAAA,SAAS,EAAE,EACrC8C,EAActB,EAAOoB,EAG3BzC,EAAAA,UAAU,IAAM,CACd,MAAM4C,EAAiB,IAAM,CAC3B,MAAMC,EAAc,OAAO,WAC3B,IAAIC,EAAc,GAEdD,GAAe,IACjBC,EAAc,KAAK,IAAI,GAAKD,EAAc,GAAOxB,CAAI,EAC5CwB,GAAe,MACxBC,EAAc,KAAK,IAAI,GAAKD,EAAc,GAAOxB,CAAI,GAGvDqB,EAAY,KAAK,IAAI,GAAII,CAAW,CAAC,CACvC,EAEA,OAAAF,EAAA,EACA,OAAO,iBAAiB,SAAUA,CAAc,EAEzC,IAAM,OAAO,oBAAoB,SAAUA,CAAc,CAClE,EAAG,CAACvB,CAAI,CAAC,EAGTrB,EAAAA,UAAU,KACHwC,EAAoB,UACvBA,EAAoB,QAAU,IAAIO,GAAiB,IAAM,CAEzD,CAAC,GAGI,IAAM,QACXC,EAAAR,EAAoB,UAApB,MAAAQ,EAA6B,OAC/B,GACC,CAAA,CAAE,EAGL,MAAMC,EAAUC,EAAAA,YAAY,CAACC,EAA+BC,EAAcC,EAAWC,EAAWC,EAAsB,KAAU,CAC9H,MAAMC,EAAUH,EAAIZ,EAAW,EACzBgB,EAAUH,EAAIb,EAAW,EACzBiB,EAASjB,EAAW,IAGtBc,IACFJ,EAAI,KAAA,EACJA,EAAI,YAAc,UAClBA,EAAI,WAAa,GACjBA,EAAI,UAAA,EACJA,EAAI,IAAIK,EAASC,EAASC,EAAS,EAAG,EAAG,KAAK,GAAK,CAAC,EACpDP,EAAI,UAAY,0BAChBA,EAAI,KAAA,EACJA,EAAI,QAAA,GAINA,EAAI,KAAA,EACJA,EAAI,UAAA,EACJA,EAAI,IAAIK,EAASC,EAASC,EAAQ,EAAG,KAAK,GAAK,CAAC,EAGhD,MAAMC,EAAWR,EAAI,qBACnBK,EAAUE,EAAS,GAAKD,EAAUC,EAAS,GAAK,EAChDF,EAASC,EAASC,CAAA,EAGdE,EAAQzC,GAAWiC,CAA+B,GAAK,UAC7DO,EAAS,aAAa,EAAGC,CAAK,EAC9BD,EAAS,aAAa,GAAKC,CAAK,EAChCD,EAAS,aAAa,EAAG,SAAS,EAElCR,EAAI,UAAYQ,EAChBR,EAAI,KAAA,EAGJA,EAAI,UAAA,EACJA,EAAI,IAAIK,EAAUE,EAAS,GAAKD,EAAUC,EAAS,GAAKA,EAAS,GAAK,EAAG,KAAK,GAAK,CAAC,EACpFP,EAAI,UAAY,2BAChBA,EAAI,KAAA,EAEJA,EAAI,QAAA,CACN,EAAG,CAACV,CAAQ,CAAC,EAGPoB,EAAqBX,EAAAA,YAAY,IAAM,CAC3C,GAAI,CAACZ,EAAmB,QAAS,CAC/B,MAAMwB,EAAkB,SAAS,cAAc,QAAQ,EACvDA,EAAgB,MAAQnB,EAAcJ,EACtCuB,EAAgB,OAASnB,EAAcJ,EACvC,MAAMY,EAAMW,EAAgB,WAAW,IAAI,EACvCX,GACFA,EAAI,MAAMZ,EAAkBA,CAAgB,EAE9CD,EAAmB,QAAUwB,CAC/B,CACA,OAAOxB,EAAmB,OAC5B,EAAG,CAACK,EAAaJ,CAAgB,CAAC,EAG5BwB,EAAsBb,cAAaC,GAAkC,CAEzEA,EAAI,YAAc,2BAClBA,EAAI,UAAY,EAGhBA,EAAI,UAAA,EACJ,QAAShD,EAAI,EAAGA,GAAKkB,EAAMlB,IAEzBgD,EAAI,OAAOhD,EAAIsC,EAAU,CAAC,EAC1BU,EAAI,OAAOhD,EAAIsC,EAAUE,CAAW,EAEpCQ,EAAI,OAAO,EAAGhD,EAAIsC,CAAQ,EAC1BU,EAAI,OAAOR,EAAaxC,EAAIsC,CAAQ,EAKtC,GAHAU,EAAI,OAAA,EAGA3B,EAAU,MACZ,QAASwC,EAAM,EAAGA,EAAM3C,EAAM2C,IAC5B,QAASC,EAAM,EAAGA,EAAM5C,EAAM4C,IAAO,CACnC,MAAMC,EAAU1C,EAAU,MAAMwC,CAAG,EAAEC,CAAG,EACxC,GAAIC,EAAU,EAAG,CACf,MAAMX,GAAa1B,GAAA,YAAAA,EAAa,OAAQmC,IAAOnC,GAAA,YAAAA,EAAa,OAAQoC,EACpEhB,EAAQE,EAAKe,EAASD,EAAMxB,EAAUuB,EAAMvB,EAAUc,CAAU,CAClE,CACF,CAGN,EAAG,CAAClC,EAAMoB,EAAUE,EAAanB,EAAU,MAAOK,EAAaoB,CAAO,CAAC,EAGjEkB,EAAYjB,EAAAA,YAAY,IAAM,WAClC,MAAMkB,EAAc,YAAY,IAAA,EAE1BC,EAAS/C,EAAU,QACzB,GAAI,CAAC+C,EAAQ,OAEb,MAAMlB,EAAMkB,EAAO,WAAW,IAAI,EAIlC,GAHI,CAAClB,GAGD,CAACf,GAAW,GAACY,EAAAR,EAAoB,UAApB,MAAAQ,EAA6B,uBAC5C,OAIFG,EAAI,UAAU,EAAG,EAAGR,EAAaA,CAAW,EAG5C,MAAM2B,GAAsBC,EAAA/B,EAAoB,UAApB,YAAA+B,EAA6B,sBAEzD,GAAI,CAACD,GAAuBlC,EAAS,CAEnC,MAAMoC,EADkBX,EAAA,EACa,WAAW,IAAI,EAChDW,IACFA,EAAa,UAAU,EAAG,EAAG7B,EAAaA,CAAW,EACrDoB,EAAoBS,CAAY,GAElCnC,EAAW,EAAK,CAClB,CAGA,GAAIC,EAAmB,SAAW,CAACgC,EACjCnB,EAAI,UAAUb,EAAmB,QAAS,EAAG,CAAC,UAG9CyB,EAAoBZ,CAAG,EAGnBmB,GAAuB9C,EAAU,MACnC,QAASwC,EAAM,EAAGA,EAAM3C,EAAM2C,IAC5B,QAASC,EAAM,EAAGA,EAAM5C,EAAM4C,IAAO,CACnC,MAAMC,EAAU1C,EAAU,MAAMwC,CAAG,EAAEC,CAAG,EACxC,GAAIC,EAAU,GAKR,GAHiBO,EAAAjC,EAAoB,UAApB,YAAAiC,EAA6B,sBAC/C,KAAKC,GAAQA,EAAK,KAAK,MAAQV,GAAOU,EAAK,KAAK,MAAQT,IAExC,CACjB,MAAMV,GAAa1B,GAAA,YAAAA,EAAa,OAAQmC,IAAOnC,GAAA,YAAAA,EAAa,OAAQoC,EACpEhB,EAAQE,EAAKe,EAASD,EAAMxB,EAAUuB,EAAMvB,EAAUc,CAAU,CAClE,CAEJ,CAMFf,EAAoB,SACHA,EAAoB,QAAQ,oBAAA,EACpC,QAAQmC,GAAa,CAC9B,MAAMC,EAAMpC,EAAoB,QAAS,wBAAwBmC,CAAS,EACpEtB,EAAIuB,EAAI,IAAMnC,EACda,EAAIsB,EAAI,IAAMnC,EAGpB,GAAIkC,EAAU,OAAS,YAAa,CAClC,MAAME,EAAWrC,EAAoB,QAAS,qBAAqBmC,EAAU,EAAE,EACzEG,EAAQ,EAAID,EACZE,GAAQ,EAAIF,EAElB1B,EAAI,KAAA,EACJA,EAAI,YAAc4B,GAClB5B,EAAI,UAAUE,EAAIZ,EAAW,EAAGa,EAAIb,EAAW,CAAC,EAChDU,EAAI,MAAM2B,EAAOA,CAAK,EACtB3B,EAAI,UAAU,CAACV,EAAW,EAAG,CAACA,EAAW,CAAC,EAC1CQ,EAAQE,EAAKwB,EAAU,QAAS,EAAG,CAAC,EACpCxB,EAAI,QAAA,CACN,MACEF,EAAQE,EAAKwB,EAAU,QAAStB,EAAGC,CAAC,CAExC,CAAC,EAICd,EAAoB,SACJA,EAAoB,QAAQ,aAAA,EACpC,QAAQ3B,GAAY,CAC5B,MAAMkE,EAAQlE,EAAS,KAAOA,EAAS,QACvCsC,EAAI,KAAA,EACJA,EAAI,YAAc4B,EAClB5B,EAAI,UAAYtC,EAAS,MACzBsC,EAAI,UAAA,EACJA,EAAI,IAAItC,EAAS,EAAGA,EAAS,EAAGA,EAAS,KAAM,EAAG,KAAK,GAAK,CAAC,EAC7DsC,EAAI,KAAA,EACJA,EAAI,QAAA,CACN,CAAC,EAKH,MAAM9F,EADY,YAAY,IAAA,EACC+G,EAC/B9F,EAAyB,YAAYjB,EAAY,CAAC,CACpD,EAAG,CAACmE,EAAU,MAAOK,EAAaR,EAAMoB,EAAUE,EAAaM,EAASb,EAASyB,EAAoBE,CAAmB,CAAC,EAGnHiB,EAAuB9B,EAAAA,YAAY,CAAC+B,EAAiBC,IAAoB,CAC7E,MAAMb,EAAS/C,EAAU,QACzB,GAAI,CAAC+C,EAAQ,OAAO,KAEpB,MAAMc,EAAOd,EAAO,sBAAA,EACdhB,EAAI4B,EAAUE,EAAK,KACnB7B,EAAI4B,EAAUC,EAAK,IAEnBlB,EAAM,KAAK,MAAMZ,EAAIZ,CAAQ,EAC7BuB,EAAM,KAAK,MAAMV,EAAIb,CAAQ,EAEnC,OAAIuB,EAAM,GAAKA,GAAO3C,GAAQ4C,EAAM,GAAKA,GAAO5C,EAAa,KAEtD,CAAE,IAAA2C,EAAK,IAAAC,CAAA,CAChB,EAAG,CAACxB,EAAUpB,CAAI,CAAC,EAGb+D,EAAqBlC,cAAamC,GAAuB,CAC7D,GAAI,EAAAtD,GAAeP,EAAU,SAAW,WAExC,GAAI,CAACK,EAECL,EAAU,OAASA,EAAU,MAAM6D,EAAS,GAAG,EAAEA,EAAS,GAAG,EAAI,IACnEvD,EAAeuD,CAAQ,EACvB1D,EAAU,YAAA,WAIRE,EAAY,MAAQwD,EAAS,KAAOxD,EAAY,MAAQwD,EAAS,IAEnEvD,EAAe,IAAI,UAIhB,KAAK,IAAID,EAAY,IAAMwD,EAAS,GAAG,IAAM,GAAKxD,EAAY,MAAQwD,EAAS,KAC/E,KAAK,IAAIxD,EAAY,IAAMwD,EAAS,GAAG,IAAM,GAAKxD,EAAY,MAAQwD,EAAS,IAMhF,GAFArD,EAAe,EAAI,EAEfQ,EAAoB,SAAWhB,EAAU,MAAO,CAClD,MAAM8D,EAAO,CACX,IAAKzD,EACL,KAAML,EAAU,MAAMK,EAAY,GAAG,EAAEA,EAAY,GAAG,CAAA,EAElD0D,EAAO,CACX,IAAKF,EACL,KAAM7D,EAAU,MAAM6D,EAAS,GAAG,EAAEA,EAAS,GAAG,CAAA,EAIlD1D,EAAU,UAAA,EAGVa,EAAoB,QAAQ,iBAC1B8C,EACAC,EACA,IACA,IAAM,CAEJ9D,EAASI,EAAawD,EAAWG,GAAY,CAE3C,GAAIA,GAAWA,EAAQ,OAAS,GAAKhD,EAAoB,QAAS,CAChE,MAAMiD,EAAYD,EAAQ,QAAQE,GAASA,EAAM,SAAS,EAC1DlD,EAAoB,QAAQ,sBAAsBiD,EAAW,GAAG,EAChE9D,EAAU,WAAA,EAGN6D,EAAQ,KAAKE,GAASA,EAAM,OAAO,GACrC/D,EAAU,aAAA,CAEd,CACF,CAAC,EACE,QAAQ,IAAM,CACbK,EAAe,EAAK,EACpBF,EAAe,IAAI,CACrB,CAAC,CACL,CAAA,CAEJ,MAEEL,EAASI,EAAawD,CAAQ,EAC3B,QAAQ,IAAM,CACbrD,EAAe,EAAK,EACpBF,EAAe,IAAI,CACrB,CAAC,OAIDN,EAAU,OAASA,EAAU,MAAM6D,EAAS,GAAG,EAAEA,EAAS,GAAG,EAAI,GACnEvD,EAAeuD,CAAQ,EACvB1D,EAAU,YAAA,GAEVG,EAAe,IAAI,CAK7B,EAAG,CAACD,EAAaE,EAAaP,EAAU,OAAQA,EAAU,MAAOH,EAAMoB,EAAUhB,CAAQ,CAAC,EAGpFkE,GAAoBzC,cAAa0C,GAA+C,CACpF,MAAMP,EAAWL,EAAqBY,EAAM,QAASA,EAAM,OAAO,EAC9DP,GACFD,EAAmBC,CAAQ,CAE/B,EAAG,CAACL,EAAsBI,CAAkB,CAAC,EAGvCS,GAAmB3C,cAAa0C,GAA+C,CACnFA,EAAM,eAAA,EACN,MAAME,EAAQF,EAAM,QAAQ,CAAC,EAC7B,GAAIE,EAAO,CACT,MAAMT,EAAWL,EAAqBc,EAAM,QAASA,EAAM,OAAO,EAC9DT,GACFD,EAAmBC,CAAQ,CAE/B,CACF,EAAG,CAACL,EAAsBI,CAAkB,CAAC,EAGvCW,GAAkB7C,cAAa0C,GAA+C,CAClFA,EAAM,eAAA,CACR,EAAG,CAAA,CAAE,EAECI,GAAiB9C,cAAa0C,GAA+C,CACjFA,EAAM,eAAA,CACR,EAAG,CAAA,CAAE,EAGL5F,OAAAA,EAAAA,UAAU,IAAM,CACdqC,EAAW,EAAI,CACjB,EAAG,CAACb,EAAU,MAAOK,EAAaR,CAAI,CAAC,EAGvCrB,EAAAA,UAAU,IAAM,CACd1B,EAAyB,gBAAA,EACzB,IAAI2H,EAEJ,MAAMC,EAAW,IAAM,CACrB,MAAMC,EAAa,YAAY,IAAA,EAE/B,GAAI3D,EAAoB,QAAS,CAC/B,MAAM4D,EAAc,YAAY,IAAA,EAChC5D,EAAoB,QAAQ,OAAA,EAC5B,MAAM6D,EAAY,YAAY,IAAA,EAE9BlC,EAAA,EACA,MAAMmC,EAAY,YAAY,IAAA,EAG9BhI,EAAyB,YAAYgI,EAAYD,EAAWA,EAAYD,CAAW,CACrF,KAAO,CACLjC,EAAA,EACA,MAAMmC,EAAY,YAAY,IAAA,EAG9BhI,EAAyB,YAAYgI,EAAYH,EAAY,CAAC,CAChE,CAEAF,EAAc,sBAAsBC,CAAQ,CAC9C,EAEA,OAAAD,EAAc,sBAAsBC,CAAQ,EAErC,IAAM,CACX5H,EAAyB,eAAA,EACrB2H,GACF,qBAAqBA,CAAW,CAEpC,CACF,EAAG,CAAC9B,CAAS,CAAC,EAGdnE,EAAAA,UAAU,IAAM,CACd,MAAMqE,EAAS/C,EAAU,QACzB,GAAI+C,EAAQ,CAEVA,EAAO,MAAQ1B,EAAcJ,EAC7B8B,EAAO,OAAS1B,EAAcJ,EAG9B8B,EAAO,MAAM,MAAQ,GAAG1B,CAAW,KACnC0B,EAAO,MAAM,OAAS,GAAG1B,CAAW,KAGpC,MAAMQ,EAAMkB,EAAO,WAAW,IAAI,EAC9BlB,GACFA,EAAI,MAAMZ,EAAkBA,CAAgB,EAG9CF,EAAW,EAAI,CACjB,CACF,EAAG,CAACM,EAAaJ,CAAgB,CAAC,SAG/BzB,GAAA,CACC,SAAA,CAAAH,OAACM,GAAA,CACC,SAAA,CAAAN,OAACO,EAAA,CACC,SAAA,CAAAN,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAQ,SAAA,KAAE,QACxB,MAAA,CAAI,UAAU,QAAS,SAAAY,EAAU,MAAM,gBAAe,CAAE,CAAA,EAC3D,SACCN,EAAA,CACC,SAAA,CAAAN,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAQ,SAAA,OAAI,EAC3BA,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAS,WAAU,SAAA,CAAU,CAAA,EAC9C,SACCM,EAAA,CACC,SAAA,CAAAN,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAQ,SAAA,KAAE,EACzBA,MAAC,OAAI,UAAU,QAAS,eAAU,4BAAa,mBAAoB,CAAA,CAAE,CAAA,CAAA,CACvE,CAAA,EACF,QAECG,GAAA,CACC,SAAAH,EAAAA,IAACI,GAAA,CACC,IAAKM,EACL,QAASqE,GACT,aAAcE,GACd,YAAaE,GACb,WAAYC,GACZ,MAAOrD,EACP,OAAQA,CAAA,CAAA,EAEZ,EAEA/B,EAAAA,IAACrB,GAAA,CACC,MAAO0C,EACP,WAAAxC,EACA,WAAY,IAAM,CAChByC,EAAgB,IAAI,EACpBC,EAAc,CAAC,CACjB,CAAA,CAAA,CACF,EACF,CAEJ"}