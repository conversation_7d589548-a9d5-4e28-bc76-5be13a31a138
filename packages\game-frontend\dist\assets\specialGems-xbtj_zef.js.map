{"version": 3, "file": "specialGems-xbtj_zef.js", "sources": ["../../src/types/specialGems.ts"], "sourcesContent": ["export type SpecialGemType = \n  | 'line_horizontal'    // 横向消除宝石\n  | 'line_vertical'      // 纵向消除宝石\n  | 'bomb'               // 炸弹宝石 (3x3区域)\n  | 'color_bomb'         // 彩色炸弹 (消除所有同色)\n  | 'lightning'          // 闪电宝石 (十字消除)\n  | 'rainbow'            // 彩虹宝石 (万能宝石)\n\nexport interface SpecialGem {\n  type: SpecialGemType\n  color: number\n  row: number\n  col: number\n  id: string\n}\n\nexport interface ComboEffect {\n  type: 'special_gem_combo' | 'cascade_combo' | 'color_combo'\n  multiplier: number\n  description: string\n  scoreBonus: number\n}\n\nexport interface ComboChain {\n  id: string\n  startTime: number\n  combos: ComboEffect[]\n  totalMultiplier: number\n  totalScore: number\n  isActive: boolean\n}\n\n// 特殊宝石生成条件\nexport const SPECIAL_GEM_CONDITIONS = {\n  line_horizontal: {\n    minMatches: 4,\n    pattern: 'horizontal_line',\n    description: '横向消除一整行'\n  },\n  line_vertical: {\n    minMatches: 4,\n    pattern: 'vertical_line',\n    description: '纵向消除一整列'\n  },\n  bomb: {\n    minMatches: 5,\n    pattern: 'L_or_T_shape',\n    description: '消除周围3x3区域'\n  },\n  color_bomb: {\n    minMatches: 5,\n    pattern: 'square_2x2',\n    description: '消除所有同色宝石'\n  },\n  lightning: {\n    minMatches: 5,\n    pattern: 'cross_shape',\n    description: '十字形消除'\n  },\n  rainbow: {\n    minMatches: 6,\n    pattern: 'any_6_match',\n    description: '可与任意宝石交换'\n  }\n}\n\n// 连击倍数配置\nexport const COMBO_MULTIPLIERS = {\n  2: 1.2,   // 2连击: 1.2倍\n  3: 1.5,   // 3连击: 1.5倍\n  4: 2.0,   // 4连击: 2.0倍\n  5: 2.5,   // 5连击: 2.5倍\n  6: 3.0,   // 6连击: 3.0倍\n  7: 4.0,   // 7连击: 4.0倍\n  8: 5.0,   // 8连击: 5.0倍\n  9: 6.0,   // 9连击: 6.0倍\n  10: 8.0,  // 10连击: 8.0倍\n  max: 10.0 // 最大倍数\n}\n\n// 特殊宝石组合效果\nexport const SPECIAL_GEM_COMBOS = {\n  // 两个炸弹宝石\n  'bomb+bomb': {\n    effect: 'mega_explosion',\n    description: '超级爆炸 - 消除5x5区域',\n    scoreMultiplier: 3.0\n  },\n  \n  // 炸弹 + 横向消除\n  'bomb+line_horizontal': {\n    effect: 'horizontal_explosion',\n    description: '横向爆炸 - 消除3行',\n    scoreMultiplier: 2.5\n  },\n  \n  // 炸弹 + 纵向消除\n  'bomb+line_vertical': {\n    effect: 'vertical_explosion',\n    description: '纵向爆炸 - 消除3列',\n    scoreMultiplier: 2.5\n  },\n  \n  // 彩色炸弹 + 任意宝石\n  'color_bomb+any': {\n    effect: 'color_explosion',\n    description: '颜色爆炸 - 消除所有同色宝石',\n    scoreMultiplier: 4.0\n  },\n  \n  // 彩虹宝石 + 特殊宝石\n  'rainbow+special': {\n    effect: 'rainbow_special',\n    description: '彩虹特效 - 将所有同色宝石变为特殊宝石',\n    scoreMultiplier: 5.0\n  },\n  \n  // 闪电 + 闪电\n  'lightning+lightning': {\n    effect: 'double_lightning',\n    description: '双重闪电 - 十字形大范围消除',\n    scoreMultiplier: 3.5\n  }\n}\n\n// 连击奖励配置\nexport const COMBO_REWARDS = {\n  score: {\n    base: 100,\n    perCombo: 50\n  },\n  \n  // 连击达到特定数量时的特殊奖励\n  milestones: {\n    5: {\n      reward: 'time_bonus',\n      value: 5, // 5秒\n      message: '5连击！时间奖励 +5秒'\n    },\n    10: {\n      reward: 'score_multiplier',\n      value: 2.0,\n      duration: 10000, // 10秒\n      message: '10连击！分数翻倍10秒'\n    },\n    15: {\n      reward: 'special_gem',\n      value: 'rainbow',\n      message: '15连击！获得彩虹宝石'\n    },\n    20: {\n      reward: 'mega_bonus',\n      value: 5000,\n      message: '20连击！超级奖励 +5000分'\n    }\n  }\n}\n\n// 特殊宝石视觉效果配置\nexport const SPECIAL_GEM_VISUALS = {\n  line_horizontal: {\n    color: '#ff6b6b',\n    glow: '#ff9999',\n    particle: 'horizontal_lines',\n    animation: 'pulse_horizontal'\n  },\n  line_vertical: {\n    color: '#4ecdc4',\n    glow: '#7fdddd',\n    particle: 'vertical_lines',\n    animation: 'pulse_vertical'\n  },\n  bomb: {\n    color: '#ffa726',\n    glow: '#ffcc80',\n    particle: 'explosion_sparks',\n    animation: 'pulse_bomb'\n  },\n  color_bomb: {\n    color: '#ab47bc',\n    glow: '#ce93d8',\n    particle: 'color_burst',\n    animation: 'rainbow_cycle'\n  },\n  lightning: {\n    color: '#42a5f5',\n    glow: '#90caf9',\n    particle: 'lightning_bolts',\n    animation: 'electric_pulse'\n  },\n  rainbow: {\n    color: 'rainbow',\n    glow: '#ffffff',\n    particle: 'rainbow_sparkles',\n    animation: 'rainbow_shimmer'\n  }\n}\n\n// 连击视觉效果\nexport const COMBO_VISUALS = {\n  text: {\n    2: { text: 'GOOD!', color: '#4caf50', size: 24 },\n    3: { text: 'GREAT!', color: '#2196f3', size: 28 },\n    4: { text: 'EXCELLENT!', color: '#ff9800', size: 32 },\n    5: { text: 'AMAZING!', color: '#e91e63', size: 36 },\n    6: { text: 'INCREDIBLE!', color: '#9c27b0', size: 40 },\n    7: { text: 'FANTASTIC!', color: '#673ab7', size: 44 },\n    8: { text: 'LEGENDARY!', color: '#3f51b5', size: 48 },\n    9: { text: 'GODLIKE!', color: '#f44336', size: 52 },\n    10: { text: 'IMPOSSIBLE!', color: '#ffd700', size: 56 }\n  },\n  \n  effects: {\n    screen_shake: { intensity: 0.1, duration: 200 },\n    particle_burst: { count: 20, spread: 360 },\n    color_flash: { duration: 100 },\n    sound_pitch: { base: 1.0, increment: 0.1 }\n  }\n}\n"], "names": ["COMBO_MULTIPLIERS", "COMBO_REWARDS", "COMBO_VISUALS"], "mappings": "AAmEO,MAAMA,EAAoB,CAC/B,EAAG,IACH,EAAG,IACH,EAAG,EACH,EAAG,IACH,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAG,EACH,GAAI,EACJ,IAAK,EACP,EAgDaC,EAAgB,CAO3B,WAAY,CACV,EAAG,CACD,OAAQ,aACR,MAAO,EACP,QAAS,cAAA,EAEX,GAAI,CACF,OAAQ,mBACR,MAAO,EACP,SAAU,IACV,QAAS,cAAA,EAEX,GAAI,CACF,OAAQ,cACR,MAAO,UACP,QAAS,aAAA,EAEX,GAAI,CACF,OAAQ,aACR,MAAO,IACP,QAAS,kBAAA,CACX,CAEJ,EA2CaC,EAAgB,CAC3B,KAAM,CACJ,EAAG,CAAE,KAAM,QAAS,MAAO,UAAW,KAAM,EAAA,EAC5C,EAAG,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,EAAA,EAC7C,EAAG,CAAE,KAAM,aAAc,MAAO,UAAW,KAAM,EAAA,EACjD,EAAG,CAAE,KAAM,WAAY,MAAO,UAAW,KAAM,EAAA,EAC/C,EAAG,CAAE,KAAM,cAAe,MAAO,UAAW,KAAM,EAAA,EAClD,EAAG,CAAE,KAAM,aAAc,MAAO,UAAW,KAAM,EAAA,EACjD,EAAG,CAAE,KAAM,aAAc,MAAO,UAAW,KAAM,EAAA,EACjD,EAAG,CAAE,KAAM,WAAY,MAAO,UAAW,KAAM,EAAA,EAC/C,GAAI,CAAE,KAAM,cAAe,MAAO,UAAW,KAAM,EAAA,CAAG,CAS1D"}