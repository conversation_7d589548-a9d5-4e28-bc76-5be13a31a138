{"version": 3, "file": "GameOverModal-4jLpsjyZ.js", "sources": ["../../src/components/Game/GameOverModal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react'\nimport styled from 'styled-components'\nimport { useNavigate } from 'react-router-dom'\nimport { useGame } from '../../contexts/GameContext'\nimport { useGameAudio } from '../../contexts/AudioContext'\n\nconst ModalOverlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n`\n\nconst ModalContent = styled.div`\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\n  border-radius: 20px;\n  padding: 2rem;\n  text-align: center;\n  max-width: 400px;\n  width: 90%;\n  border: 2px solid #fbbf24;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\n`\n\nconst Title = styled.h2<{ isWin: boolean }>`\n  color: ${props => props.isWin ? '#10b981' : '#ef4444'};\n  font-size: 2rem;\n  margin-bottom: 1rem;\n  text-shadow: 0 0 20px ${props => props.isWin ? 'rgba(16, 185, 129, 0.5)' : 'rgba(239, 68, 68, 0.5)'};\n`\n\nconst ScoreSection = styled.div`\n  margin: 1.5rem 0;\n  padding: 1rem;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`\n\nconst ScoreItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0.5rem 0;\n  color: white;\n  \n  .label {\n    font-size: 1rem;\n    opacity: 0.8;\n  }\n  \n  .value {\n    font-size: 1.2rem;\n    font-weight: bold;\n    color: #fbbf24;\n  }\n`\n\nconst StarsContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 0.5rem;\n  margin: 1rem 0;\n`\n\nconst Star = styled.div<{ filled: boolean }>`\n  font-size: 2rem;\n  color: ${props => props.filled ? '#fbbf24' : 'rgba(255, 255, 255, 0.3)'};\n  filter: ${props => props.filled ? 'drop-shadow(0 0 10px #fbbf24)' : 'none'};\n  transition: all 0.3s ease;\n`\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-top: 1.5rem;\n  flex-wrap: wrap;\n  justify-content: center;\n`\n\nconst Button = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n    color: white;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\n    }\n  ` : `\n    background: rgba(255, 255, 255, 0.1);\n    color: white;\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    \n    &:hover {\n      background: rgba(255, 255, 255, 0.2);\n      transform: translateY(-2px);\n    }\n  `}\n`\n\ninterface GameOverModalProps {\n  isVisible: boolean\n}\n\nconst GameOverModal: React.FC<GameOverModalProps> = ({ isVisible }) => {\n  const { gameState, resetGame } = useGame()\n  const gameAudio = useGameAudio()\n  const navigate = useNavigate()\n\n  // 播放游戏结束音效\n  useEffect(() => {\n    if (isVisible && (gameState.status === 'won' || gameState.status === 'lost')) {\n      if (gameState.status === 'won') {\n        gameAudio.onLevelComplete()\n      } else {\n        gameAudio.onLevelFailed()\n      }\n    }\n  }, [isVisible, gameState.status, gameAudio])\n\n  if (!isVisible || (gameState.status !== 'won' && gameState.status !== 'lost')) {\n    return null\n  }\n\n  const isWin = gameState.status === 'won'\n  const targetScore = gameState.targetScore || 0\n  const scorePercentage = Math.min((gameState.score / targetScore) * 100, 100)\n  \n  // Calculate stars based on score percentage\n  const getStars = () => {\n    if (scorePercentage >= 100) return 3\n    if (scorePercentage >= 80) return 2\n    if (scorePercentage >= 60) return 1\n    return 0\n  }\n\n  const stars = isWin ? getStars() : 0\n\n  const handleRestart = () => {\n    resetGame()\n  }\n\n  const handleNextLevel = () => {\n    const currentLevel = gameState.currentLevel || 1\n    navigate(`/game?level=${currentLevel + 1}`)\n  }\n\n  const handleBackToLevels = () => {\n    navigate('/levels')\n  }\n\n  const handleBackToHome = () => {\n    navigate('/')\n  }\n\n  return (\n    <ModalOverlay>\n      <ModalContent>\n        <Title isWin={isWin}>\n          {isWin ? '🎉 恭喜过关！' : '😔 游戏结束'}\n        </Title>\n\n        {isWin && (\n          <StarsContainer>\n            {[1, 2, 3].map(star => (\n              <Star key={star} filled={star <= stars}>\n                ⭐\n              </Star>\n            ))}\n          </StarsContainer>\n        )}\n\n        <ScoreSection>\n          <ScoreItem>\n            <span className=\"label\">最终分数</span>\n            <span className=\"value\">{gameState.score.toLocaleString()}</span>\n          </ScoreItem>\n          <ScoreItem>\n            <span className=\"label\">目标分数</span>\n            <span className=\"value\">{targetScore.toLocaleString()}</span>\n          </ScoreItem>\n          <ScoreItem>\n            <span className=\"label\">完成度</span>\n            <span className=\"value\">{scorePercentage.toFixed(1)}%</span>\n          </ScoreItem>\n          {gameState.movesLeft !== undefined && (\n            <ScoreItem>\n              <span className=\"label\">剩余步数</span>\n              <span className=\"value\">{gameState.movesLeft}</span>\n            </ScoreItem>\n          )}\n        </ScoreSection>\n\n        <ButtonGroup>\n          <Button variant=\"primary\" onClick={handleRestart}>\n            重新开始\n          </Button>\n          \n          {isWin && (\n            <Button variant=\"primary\" onClick={handleNextLevel}>\n              下一关\n            </Button>\n          )}\n          \n          <Button onClick={handleBackToLevels}>\n            选择关卡\n          </Button>\n          \n          <Button onClick={handleBackToHome}>\n            返回主页\n          </Button>\n        </ButtonGroup>\n      </ModalContent>\n    </ModalOverlay>\n  )\n}\n\nexport default GameOverModal\n"], "names": ["ModalOverlay", "styled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title", "props", "ScoreSection", "ScoreItem", "StarsContainer", "Star", "ButtonGroup", "<PERSON><PERSON>", "GameOverModal", "isVisible", "gameState", "resetGame", "useGame", "gameAudio", "useGameAudio", "navigate", "useNavigate", "useEffect", "isWin", "targetScore", "scorePercentage", "stars", "handleRestart", "handleNextLevel", "currentLevel", "handleBackToLevels", "handleBackToHome", "jsx", "jsxs", "star"], "mappings": "6KAMA,MAAMA,EAAeC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatBC,EAAeD,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtBE,EAAQF,EAAO;AAAA,WACVG,GAASA,EAAM,MAAQ,UAAY,SAAS;AAAA;AAAA;AAAA,0BAG7BA,GAASA,EAAM,MAAQ,0BAA4B,wBAAwB;AAAA,EAG/FC,EAAeJ,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQtBK,EAAYL,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBnBM,EAAiBN,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxBO,EAAOP,EAAO;AAAA;AAAA,WAETG,GAASA,EAAM,OAAS,UAAY,0BAA0B;AAAA,YAC7DA,GAASA,EAAM,OAAS,gCAAkC,MAAM;AAAA;AAAA,EAItEK,EAAcR,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrBS,EAAST,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASlBG,GAASA,EAAM,UAAY,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASH;AAAA,EAOGO,EAA8C,CAAC,CAAE,UAAAC,KAAgB,CACrE,KAAM,CAAE,UAAAC,EAAW,UAAAC,CAAA,EAAcC,EAAA,EAC3BC,EAAYC,EAAA,EACZC,EAAWC,EAAA,EAajB,GAVAC,EAAAA,UAAU,IAAM,CACVR,IAAcC,EAAU,SAAW,OAASA,EAAU,SAAW,UAC/DA,EAAU,SAAW,MACvBG,EAAU,gBAAA,EAEVA,EAAU,cAAA,EAGhB,EAAG,CAACJ,EAAWC,EAAU,OAAQG,CAAS,CAAC,EAEvC,CAACJ,GAAcC,EAAU,SAAW,OAASA,EAAU,SAAW,OACpE,OAAO,KAGT,MAAMQ,EAAQR,EAAU,SAAW,MAC7BS,EAAcT,EAAU,aAAe,EACvCU,EAAkB,KAAK,IAAKV,EAAU,MAAQS,EAAe,IAAK,GAAG,EAUrEE,EAAQH,EANRE,GAAmB,IAAY,EAC/BA,GAAmB,GAAW,EAC9BA,GAAmB,GAAW,EAC3B,EAG0B,EAE7BE,EAAgB,IAAM,CAC1BX,EAAA,CACF,EAEMY,EAAkB,IAAM,CAC5B,MAAMC,EAAed,EAAU,cAAgB,EAC/CK,EAAS,eAAeS,EAAe,CAAC,EAAE,CAC5C,EAEMC,EAAqB,IAAM,CAC/BV,EAAS,SAAS,CACpB,EAEMW,EAAmB,IAAM,CAC7BX,EAAS,GAAG,CACd,EAEA,OACEY,EAAAA,IAAC9B,EAAA,CACC,SAAA+B,EAAAA,KAAC7B,EAAA,CACC,SAAA,CAAA4B,EAAAA,IAAC3B,EAAA,CAAM,MAAAkB,EACJ,SAAAA,EAAQ,WAAa,UACxB,EAECA,GACCS,EAAAA,IAACvB,EAAA,CACE,UAAC,EAAG,EAAG,CAAC,EAAE,IAAIyB,GACbF,EAAAA,IAACtB,GAAgB,OAAQwB,GAAQR,EAAO,SAAA,GAAA,EAA7BQ,CAEX,CACD,EACH,SAGD3B,EAAA,CACC,SAAA,CAAA0B,OAACzB,EAAA,CACC,SAAA,CAAAwB,EAAAA,IAAC,OAAA,CAAK,UAAU,QAAQ,SAAA,OAAI,QAC3B,OAAA,CAAK,UAAU,QAAS,SAAAjB,EAAU,MAAM,gBAAe,CAAE,CAAA,EAC5D,SACCP,EAAA,CACC,SAAA,CAAAwB,EAAAA,IAAC,OAAA,CAAK,UAAU,QAAQ,SAAA,OAAI,QAC3B,OAAA,CAAK,UAAU,QAAS,SAAAR,EAAY,gBAAe,CAAE,CAAA,EACxD,SACChB,EAAA,CACC,SAAA,CAAAwB,EAAAA,IAAC,OAAA,CAAK,UAAU,QAAQ,SAAA,MAAG,EAC3BC,EAAAA,KAAC,OAAA,CAAK,UAAU,QAAS,SAAA,CAAAR,EAAgB,QAAQ,CAAC,EAAE,GAAA,CAAA,CAAC,CAAA,EACvD,EACCV,EAAU,YAAc,QACvBkB,EAAAA,KAACzB,EAAA,CACC,SAAA,CAAAwB,EAAAA,IAAC,OAAA,CAAK,UAAU,QAAQ,SAAA,OAAI,EAC5BA,EAAAA,IAAC,OAAA,CAAK,UAAU,QAAS,WAAU,SAAA,CAAU,CAAA,CAAA,CAC/C,CAAA,EAEJ,SAECrB,EAAA,CACC,SAAA,CAAAqB,MAACpB,EAAA,CAAO,QAAQ,UAAU,QAASe,EAAe,SAAA,OAElD,EAECJ,GACCS,EAAAA,IAACpB,EAAA,CAAO,QAAQ,UAAU,QAASgB,EAAiB,SAAA,MAEpD,EAGFI,EAAAA,IAACpB,EAAA,CAAO,QAASkB,EAAoB,SAAA,OAErC,EAEAE,EAAAA,IAACpB,EAAA,CAAO,QAASmB,EAAkB,SAAA,MAAA,CAEnC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAEJ"}