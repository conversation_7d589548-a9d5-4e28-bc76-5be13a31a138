import { Router, Response } from 'express';
import { Database } from '../database/database';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取任务列表
router.get('/', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  // 这里简化实现，实际应该有完整的任务系统
  const dailyQuests = [
    {
      questCode: 'daily_complete_3',
      name: '完成3个关卡',
      description: '今日完成任意3个关卡',
      type: 'daily',
      targetType: 'complete_levels',
      targetValue: 3,
      currentProgress: 0,
      rewards: {
        coins: 100,
        gems: 0,
        items: []
      },
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    },
    {
      questCode: 'daily_score_10000',
      name: '获得10000分',
      description: '单局游戏获得10000分以上',
      type: 'daily',
      targetType: 'high_score',
      targetValue: 10000,
      currentProgress: 0,
      rewards: {
        coins: 200,
        gems: 1,
        items: []
      },
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    }
  ];

  const achievements = [
    {
      questCode: 'achievement_first_win',
      name: '初次胜利',
      description: '完成第一个关卡',
      type: 'achievement',
      targetType: 'complete_level',
      targetValue: 1,
      currentProgress: 0,
      rewards: {
        coins: 50,
        gems: 1,
        items: ['hammer']
      }
    }
  ];

  res.json({
    success: true,
    data: {
      daily: dailyQuests,
      achievements: achievements
    }
  });
}));

// 领取任务奖励
router.post('/:questCode/claim', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;
  const { questCode } = req.params;

  // 简化实现，实际应该检查任务完成状态
  res.json({
    success: true,
    data: {
      questCode,
      rewards: {
        coins: 100,
        gems: 0,
        items: []
      }
    },
    message: '奖励领取成功'
  });
}));

export { router as questRoutes };
