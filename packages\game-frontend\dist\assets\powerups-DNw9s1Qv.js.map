{"version": 3, "file": "powerups-DNw9s1Qv.js", "sources": ["../../src/types/powerups.ts"], "sourcesContent": ["export type PowerUpType = \n  | 'bomb'           // 炸弹 - 消除周围3x3区域\n  | 'rainbow'        // 彩虹宝石 - 消除所有同色宝石\n  | 'hammer'         // 锤子 - 消除单个宝石\n  | 'shuffle'        // 洗牌 - 重新排列棋盘\n  | 'time_extend'    // 时间延长 - 增加游戏时间\n  | 'score_boost'    // 分数加成 - 双倍分数\n  | 'hint'           // 提示 - 显示可能的移动\n  | 'freeze'         // 冰冻 - 暂停时间流逝\n\nexport interface PowerUp {\n  id: string\n  type: PowerUpType\n  name: string\n  description: string\n  icon: string\n  cost: number          // 购买成本（金币）\n  cooldown?: number     // 冷却时间（秒）\n  duration?: number     // 持续时间（秒）\n  uses?: number         // 使用次数限制\n  rarity: 'common' | 'rare' | 'epic' | 'legendary'\n}\n\nexport interface PowerUpInstance {\n  powerUpId: string\n  type: PowerUpType\n  quantity: number\n  expiresAt?: Date\n}\n\nexport interface PowerUpEffect {\n  type: PowerUpType\n  startTime: number\n  duration?: number\n  multiplier?: number\n  active: boolean\n}\n\n// 道具配置\nexport const POWER_UPS: Record<PowerUpType, PowerUp> = {\n  bomb: {\n    id: 'bomb',\n    type: 'bomb',\n    name: '💣 炸弹',\n    description: '消除选中位置周围3x3区域的所有宝石',\n    icon: '💣',\n    cost: 100,\n    uses: 1,\n    rarity: 'common'\n  },\n  \n  rainbow: {\n    id: 'rainbow',\n    type: 'rainbow',\n    name: '🌈 彩虹宝石',\n    description: '消除棋盘上所有与选中宝石相同颜色的宝石',\n    icon: '🌈',\n    cost: 200,\n    uses: 1,\n    rarity: 'rare'\n  },\n  \n  hammer: {\n    id: 'hammer',\n    type: 'hammer',\n    name: '🔨 锤子',\n    description: '直接消除选中的单个宝石',\n    icon: '🔨',\n    cost: 50,\n    uses: 1,\n    rarity: 'common'\n  },\n  \n  shuffle: {\n    id: 'shuffle',\n    type: 'shuffle',\n    name: '🔄 洗牌',\n    description: '重新随机排列棋盘上的所有宝石',\n    icon: '🔄',\n    cost: 150,\n    uses: 1,\n    rarity: 'rare'\n  },\n  \n  time_extend: {\n    id: 'time_extend',\n    type: 'time_extend',\n    name: '⏰ 时间延长',\n    description: '为当前关卡增加30秒游戏时间',\n    icon: '⏰',\n    cost: 80,\n    uses: 1,\n    rarity: 'common'\n  },\n  \n  score_boost: {\n    id: 'score_boost',\n    type: 'score_boost',\n    name: '⭐ 分数加成',\n    description: '接下来60秒内获得的分数翻倍',\n    icon: '⭐',\n    cost: 120,\n    duration: 60,\n    rarity: 'rare'\n  },\n  \n  hint: {\n    id: 'hint',\n    type: 'hint',\n    name: '💡 提示',\n    description: '高亮显示一个可能的移动组合',\n    icon: '💡',\n    cost: 30,\n    uses: 1,\n    rarity: 'common'\n  },\n  \n  freeze: {\n    id: 'freeze',\n    type: 'freeze',\n    name: '❄️ 时间冰冻',\n    description: '暂停时间流逝15秒',\n    icon: '❄️',\n    cost: 180,\n    duration: 15,\n    rarity: 'epic'\n  }\n}\n\n// 道具稀有度配置\nexport const RARITY_CONFIG = {\n  common: {\n    color: '#9ca3af',\n    bgColor: 'rgba(156, 163, 175, 0.1)',\n    borderColor: 'rgba(156, 163, 175, 0.3)',\n    name: '普通'\n  },\n  rare: {\n    color: '#3b82f6',\n    bgColor: 'rgba(59, 130, 246, 0.1)',\n    borderColor: 'rgba(59, 130, 246, 0.3)',\n    name: '稀有'\n  },\n  epic: {\n    color: '#8b5cf6',\n    bgColor: 'rgba(139, 92, 246, 0.1)',\n    borderColor: 'rgba(139, 92, 246, 0.3)',\n    name: '史诗'\n  },\n  legendary: {\n    color: '#f59e0b',\n    bgColor: 'rgba(245, 158, 11, 0.1)',\n    borderColor: 'rgba(245, 158, 11, 0.3)',\n    name: '传说'\n  }\n}\n\n// 道具使用结果\nexport interface PowerUpResult {\n  success: boolean\n  message: string\n  effects?: {\n    gemsRemoved?: Array<{ row: number; col: number }>\n    scoreGained?: number\n    timeAdded?: number\n    boardShuffled?: boolean\n    effectApplied?: PowerUpEffect\n  }\n}\n\n// 道具使用参数\nexport interface PowerUpUsageParams {\n  type: PowerUpType\n  position?: { row: number; col: number }\n  board?: number[][]\n  gameState?: any\n}\n"], "names": ["POWER_UPS", "RARITY_CONFIG"], "mappings": "AAuCO,MAAMA,EAA0C,CACrD,KAAM,CACJ,GAAI,OACJ,KAAM,OACN,KAAM,QACN,YAAa,qBACb,KAAM,KACN,KAAM,IACN,KAAM,EACN,OAAQ,QAAA,EAGV,QAAS,CACP,GAAI,UACJ,KAAM,UACN,KAAM,UACN,YAAa,sBACb,KAAM,KACN,KAAM,IACN,KAAM,EACN,OAAQ,MAAA,EAGV,OAAQ,CACN,GAAI,SACJ,KAAM,SACN,KAAM,QACN,YAAa,cACb,KAAM,KACN,KAAM,GACN,KAAM,EACN,OAAQ,QAAA,EAGV,QAAS,CACP,GAAI,UACJ,KAAM,UACN,KAAM,QACN,YAAa,iBACb,KAAM,KACN,KAAM,IACN,KAAM,EACN,OAAQ,MAAA,EAGV,YAAa,CACX,GAAI,cACJ,KAAM,cACN,KAAM,SACN,YAAa,iBACb,KAAM,IACN,KAAM,GACN,KAAM,EACN,OAAQ,QAAA,EAGV,YAAa,CACX,GAAI,cACJ,KAAM,cACN,KAAM,SACN,YAAa,iBACb,KAAM,IACN,KAAM,IACN,SAAU,GACV,OAAQ,MAAA,EAGV,KAAM,CACJ,GAAI,OACJ,KAAM,OACN,KAAM,QACN,YAAa,gBACb,KAAM,KACN,KAAM,GACN,KAAM,EACN,OAAQ,QAAA,EAGV,OAAQ,CACN,GAAI,SACJ,KAAM,SACN,KAAM,UACN,YAAa,YACb,KAAM,KACN,KAAM,IACN,SAAU,GACV,OAAQ,MAAA,CAEZ,EAGaC,EAAgB,CAC3B,OAAQ,CACN,MAAO,UACP,QAAS,2BACT,YAAa,2BACb,KAAM,IAAA,EAER,KAAM,CACJ,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,IAAA,EAER,KAAM,CACJ,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,IAAA,EAER,UAAW,CACT,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,IAAA,CAEV"}