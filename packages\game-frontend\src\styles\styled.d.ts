import 'styled-components'

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: {
      primary: {
        50: string
        100: string
        200: string
        300: string
        400: string
        500: string
        600: string
        700: string
        800: string
        900: string
      }
      secondary: {
        50: string
        100: string
        200: string
        300: string
        400: string
        500: string
        600: string
        700: string
        800: string
        900: string
      }
      gray: {
        50: string
        100: string
        200: string
        300: string
        400: string
        500: string
        600: string
        700: string
        800: string
        900: string
      }
      success: string
      warning: string
      error: string
      info: string
      background: {
        primary: string
        secondary: string
        card: string
        overlay: string
      }
      text: {
        primary: string
        secondary: string
        muted: string
        inverse: string
      }
    }
    fonts: {
      primary: string
      mono: string
    }
    fontSizes: {
      xs: string
      sm: string
      base: string
      lg: string
      xl: string
      '2xl': string
      '3xl': string
      '4xl': string
      '5xl': string
      '6xl': string
    }
    fontWeights: {
      light: number
      normal: number
      medium: number
      semibold: number
      bold: number
    }
    spacing: {
      0: string
      1: string
      2: string
      3: string
      4: string
      5: string
      6: string
      8: string
      10: string
      12: string
      16: string
      20: string
      24: string
      32: string
      40: string
      48: string
      56: string
      64: string
    }
    borderRadius: {
      none: string
      sm: string
      base: string
      md: string
      lg: string
      xl: string
      '2xl': string
      '3xl': string
      full: string
    }
    shadows: {
      sm: string
      base: string
      md: string
      lg: string
      xl: string
      '2xl': string
      glow: string
      glowGold: string
      glowPurple: string
      gemGlow: string
      inner: string
      text: string
    }
    breakpoints: {
      sm: string
      md: string
      lg: string
      xl: string
      '2xl': string
    }
    transitions: {
      fast: string
      base: string
      slow: string
    }
    zIndex: {
      dropdown: number
      sticky: number
      fixed: number
      modal: number
      popover: number
      tooltip: number
    }
  }
}
