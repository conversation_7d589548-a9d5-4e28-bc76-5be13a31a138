export interface Position {
  row: number
  col: number
}

export interface AnimationFrame {
  timestamp: number
  progress: number // 0 to 1
}

export interface GemAnimation {
  id: string
  type: 'swap' | 'fall' | 'eliminate' | 'spawn'
  startTime: number
  duration: number
  from: Position
  to: Position
  gemType: number
  isActive: boolean
  onComplete?: () => void
}

export interface ParticleEffect {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  color: string
  size: number
  type: 'star' | 'sparkle' | 'explosion'
}

export class AnimationManager {
  private animations: Map<string, GemAnimation> = new Map()
  private particles: ParticleEffect[] = []
  private animationId: number | null = null
  private lastFrameTime: number = 0

  constructor(private onAnimationUpdate: () => void) {}

  // 开始动画循环
  public start(): void {
    if (this.animationId) return
    
    const animate = (timestamp: number) => {
      this.update(timestamp)
      this.onAnimationUpdate()
      
      if (this.hasActiveAnimations() || this.particles.length > 0) {
        this.animationId = requestAnimationFrame(animate)
      } else {
        this.animationId = null
      }
    }
    
    this.animationId = requestAnimationFrame(animate)
  }

  // 停止动画循环
  public stop(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  // 更新动画状态
  public update(timestamp: number = performance.now()): void {
    this.lastFrameTime = timestamp
    
    // 更新宝石动画
    for (const [id, animation] of this.animations) {
      const elapsed = timestamp - animation.startTime
      const progress = Math.min(elapsed / animation.duration, 1)
      
      if (progress >= 1) {
        animation.isActive = false
        if (animation.onComplete) {
          animation.onComplete()
        }
        this.animations.delete(id)
      }
    }
    
    // 更新粒子效果
    this.particles = this.particles.filter(particle => {
      particle.life -= 16 // 假设60fps，每帧约16ms
      particle.x += particle.vx
      particle.y += particle.vy
      particle.vy += 0.2 // 重力
      particle.vx *= 0.99 // 阻力
      
      return particle.life > 0
    })
  }

  // 添加宝石交换动画
  public addSwapAnimation(
    gem1: { pos: Position; type: number },
    gem2: { pos: Position; type: number },
    duration: number = 300,
    onComplete?: () => void
  ): void {
    const id1 = `swap_${gem1.pos.row}_${gem1.pos.col}_${Date.now()}`
    const id2 = `swap_${gem2.pos.row}_${gem2.pos.col}_${Date.now()}_2`
    
    this.animations.set(id1, {
      id: id1,
      type: 'swap',
      startTime: this.lastFrameTime,
      duration,
      from: gem1.pos,
      to: gem2.pos,
      gemType: gem1.type,
      isActive: true,
      onComplete: onComplete
    })
    
    this.animations.set(id2, {
      id: id2,
      type: 'swap',
      startTime: this.lastFrameTime,
      duration,
      from: gem2.pos,
      to: gem1.pos,
      gemType: gem2.type,
      isActive: true
    })
    
    // 不自动启动动画循环，由GameBoard的游戏循环控制
  }

  // 添加宝石下落动画
  public addFallAnimation(
    gemType: number,
    from: Position,
    to: Position,
    duration: number = 400,
    onComplete?: () => void
  ): void {
    const id = `fall_${to.row}_${to.col}_${Date.now()}`
    
    this.animations.set(id, {
      id,
      type: 'fall',
      startTime: this.lastFrameTime,
      duration,
      from,
      to,
      gemType,
      isActive: true,
      onComplete
    })
    
    // 不自动启动动画循环，由GameBoard的游戏循环控制
  }

  // 添加宝石消除动画
  public addEliminateAnimation(
    positions: Position[],
    duration: number = 500,
    onComplete?: () => void
  ): void {
    positions.forEach((pos, index) => {
      const id = `eliminate_${pos.row}_${pos.col}_${Date.now()}`
      
      this.animations.set(id, {
        id,
        type: 'eliminate',
        startTime: this.lastFrameTime + (index * 50), // 错开时间
        duration,
        from: pos,
        to: pos,
        gemType: 0,
        isActive: true,
        onComplete: index === positions.length - 1 ? onComplete : undefined
      })
    })
    
    // 添加粒子效果
    positions.forEach(pos => {
      this.addParticleEffect(pos, 'explosion')
    })
    
    // 不自动启动动画循环，由GameBoard的游戏循环控制
  }

  // 添加粒子效果
  public addParticleEffect(position: Position, type: ParticleEffect['type']): void {
    const cellSize = 60 // 假设单元格大小
    const centerX = position.col * cellSize + cellSize / 2
    const centerY = position.row * cellSize + cellSize / 2
    
    const particleCount = type === 'explosion' ? 8 : 4
    
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.PI * 2 * i) / particleCount
      const speed = 2 + Math.random() * 3
      
      this.particles.push({
        id: `particle_${Date.now()}_${i}`,
        x: centerX,
        y: centerY,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        life: 1000 + Math.random() * 500,
        maxLife: 1500,
        color: this.getParticleColor(type),
        size: 3 + Math.random() * 2,
        type
      })
    }
  }

  // 获取粒子颜色
  private getParticleColor(type: ParticleEffect['type']): string {
    switch (type) {
      case 'star':
        return '#fbbf24'
      case 'sparkle':
        return '#60a5fa'
      case 'explosion':
        return '#f87171'
      default:
        return '#ffffff'
    }
  }

  // 获取动画进度
  public getAnimationProgress(id: string): number {
    const animation = this.animations.get(id)
    if (!animation) return 1
    
    const elapsed = this.lastFrameTime - animation.startTime
    return Math.min(elapsed / animation.duration, 1)
  }

  // 获取当前位置（用于插值）
  public getInterpolatedPosition(animation: GemAnimation): Position {
    const elapsed = this.lastFrameTime - animation.startTime
    const progress = Math.min(elapsed / animation.duration, 1)
    
    // 使用缓动函数
    const easedProgress = this.easeInOutCubic(progress)
    
    return {
      row: animation.from.row + (animation.to.row - animation.from.row) * easedProgress,
      col: animation.from.col + (animation.to.col - animation.from.col) * easedProgress
    }
  }

  // 缓动函数
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  // 检查是否有活跃动画
  public hasActiveAnimations(): boolean {
    return this.animations.size > 0
  }

  // 获取所有活跃动画
  public getActiveAnimations(): GemAnimation[] {
    return Array.from(this.animations.values())
  }

  // 获取所有粒子
  public getParticles(): ParticleEffect[] {
    return this.particles
  }

  // 清除所有动画
  public clear(): void {
    this.animations.clear()
    this.particles.length = 0
    this.stop()
  }
}
