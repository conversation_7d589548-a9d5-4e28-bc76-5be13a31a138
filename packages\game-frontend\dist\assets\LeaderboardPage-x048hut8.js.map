{"version": 3, "file": "LeaderboardPage-x048hut8.js", "sources": ["../../src/pages/LeaderboardPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react'\nimport styled from 'styled-components'\nimport {\n  LeaderboardType,\n  LeaderboardData,\n  Friend,\n  LEADERBOARD_CONFIG,\n  generateMockLeaderboardData,\n  generateMockFriends\n} from '../types/leaderboard'\nimport { theme, media } from '../styles/theme'\nimport { Card } from '../components/UI/Card'\nimport { Button } from '../components/UI/Button'\n\nconst Container = styled.div`\n  min-height: 100vh;\n  background: ${theme.colors.background.primary};\n  padding: ${theme.spacing[6]} ${theme.spacing[4]};\n\n  ${media.maxMd} {\n    padding: ${theme.spacing[4]} ${theme.spacing[3]};\n  }\n\n  ${media.maxSm} {\n    padding: ${theme.spacing[3]} ${theme.spacing[2]};\n  }\n`\n\nconst Header = styled.div`\n  text-align: center;\n  margin-bottom: ${theme.spacing[8]};\n\n  ${media.maxMd} {\n    margin-bottom: ${theme.spacing[6]};\n  }\n`\n\nconst Title = styled.h1`\n  color: ${theme.colors.text.primary};\n  font-size: ${theme.fontSizes['4xl']};\n  font-weight: ${theme.fontWeights.bold};\n  margin-bottom: ${theme.spacing[4]};\n  text-shadow: ${theme.shadows.text};\n\n  ${media.maxMd} {\n    font-size: ${theme.fontSizes['3xl']};\n  }\n\n  ${media.maxSm} {\n    font-size: ${theme.fontSizes['2xl']};\n  }\n`\n\nconst TabContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: ${theme.spacing[2]};\n  margin-bottom: ${theme.spacing[6]};\n  flex-wrap: wrap;\n\n  ${media.maxMd} {\n    margin-bottom: ${theme.spacing[4]};\n  }\n`\n\nconst TabButton = styled(Button)<{ $active: boolean }>`\n  ${props => props.$active && `\n    background: ${theme.colors.secondary[500]};\n    color: white;\n\n    &:hover {\n      background: ${theme.colors.secondary[600]};\n    }\n  `}\n\n  ${media.maxSm} {\n    font-size: ${theme.fontSizes.xs};\n    padding: ${theme.spacing[1]} ${theme.spacing[2]};\n  }\n`\n\nconst ContentContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 300px;\n  gap: ${theme.spacing[6]};\n  max-width: 1200px;\n  margin: 0 auto;\n\n  ${media.maxLg} {\n    grid-template-columns: 1fr;\n    gap: ${theme.spacing[4]};\n  }\n`\n\nconst LeaderboardContainer = styled(Card)`\n  padding: ${theme.spacing[6]};\n\n  ${media.maxMd} {\n    padding: ${theme.spacing[4]};\n  }\n\n  ${media.maxSm} {\n    padding: ${theme.spacing[3]};\n  }\n`\n\nconst LeaderboardHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n\n  h2 {\n    color: ${theme.colors.text.primary};\n    font-size: ${theme.fontSizes.xl};\n    margin: 0;\n    display: flex;\n    align-items: center;\n    gap: ${theme.spacing[2]};\n  }\n\n  .last-updated {\n    color: ${theme.colors.text.secondary};\n    font-size: ${theme.fontSizes.sm};\n  }\n`\n\nconst LeaderboardList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${theme.spacing[2]};\n`\n\nconst LeaderboardEntry = styled.div<{ $isCurrentUser?: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: ${theme.spacing[3]};\n  padding: ${theme.spacing[3]};\n  background: ${props => props.$isCurrentUser\n    ? 'rgba(251, 191, 36, 0.1)'\n    : 'rgba(255, 255, 255, 0.05)'\n  };\n  border: 1px solid ${props => props.$isCurrentUser\n    ? 'rgba(251, 191, 36, 0.3)'\n    : 'rgba(255, 255, 255, 0.1)'\n  };\n  border-radius: ${theme.borderRadius.lg};\n  transition: all ${theme.transitions.base} ease-in-out;\n\n  &:hover {\n    background: ${props => props.$isCurrentUser\n      ? 'rgba(251, 191, 36, 0.15)'\n      : 'rgba(255, 255, 255, 0.08)'\n    };\n    transform: translateY(-1px);\n  }\n\n  ${media.maxSm} {\n    padding: ${theme.spacing[2]};\n    gap: ${theme.spacing[2]};\n  }\n`\n\nconst Rank = styled.div<{ $rank: number }>`\n  font-size: ${theme.fontSizes.lg};\n  font-weight: ${theme.fontWeights.bold};\n  color: ${props => {\n    if (props.$rank === 1) return '#ffd700'\n    if (props.$rank === 2) return '#c0c0c0'\n    if (props.$rank === 3) return '#cd7f32'\n    return theme.colors.text.secondary\n  }};\n  min-width: 40px;\n  text-align: center;\n\n  ${media.maxSm} {\n    font-size: ${theme.fontSizes.base};\n    min-width: 30px;\n  }\n`\n\nconst LeaderboardPage: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<LeaderboardType>('global_score')\n  const [leaderboardData, setLeaderboardData] = useState<LeaderboardData | null>(null)\n  const [friends, setFriends] = useState<Friend[]>([])\n  const [loading, setLoading] = useState(false)\n\n  // 加载排行榜数据\n  useEffect(() => {\n    setLoading(true)\n    // 模拟API调用\n    setTimeout(() => {\n      setLeaderboardData(generateMockLeaderboardData(activeTab))\n      setLoading(false)\n    }, 500)\n  }, [activeTab])\n\n  // 加载好友数据\n  useEffect(() => {\n    setFriends(generateMockFriends())\n  }, [])\n\n  const tabs = Object.entries(LEADERBOARD_CONFIG).map(([key, config]) => ({\n    key: key as LeaderboardType,\n    ...config\n  }))\n\n  return (\n    <Container>\n      <Header>\n        <Title>🏆 排行榜</Title>\n\n        <TabContainer>\n          {tabs.map(tab => (\n            <TabButton\n              key={tab.key}\n              variant=\"ghost\"\n              size=\"sm\"\n              $active={activeTab === tab.key}\n              onClick={() => setActiveTab(tab.key)}\n            >\n              {tab.icon} {tab.name}\n            </TabButton>\n          ))}\n        </TabContainer>\n      </Header>\n\n      <ContentContainer>\n        <LeaderboardContainer>\n          {leaderboardData && (\n            <>\n              <LeaderboardHeader>\n                <h2>\n                  {LEADERBOARD_CONFIG[activeTab].icon} {LEADERBOARD_CONFIG[activeTab].name}\n                </h2>\n                <div className=\"last-updated\">\n                  最后更新: {leaderboardData.lastUpdated.toLocaleTimeString()}\n                </div>\n              </LeaderboardHeader>\n\n              {loading ? (\n                <div style={{ textAlign: 'center', padding: '2rem', color: theme.colors.text.secondary }}>\n                  加载中...\n                </div>\n              ) : (\n                <LeaderboardList>\n                  {leaderboardData.entries.map(entry => (\n                    <LeaderboardEntry\n                      key={entry.id}\n                      $isCurrentUser={entry.isCurrentUser}\n                    >\n                      <Rank $rank={entry.rank}>\n                        {entry.rank <= 3 ? (\n                          entry.rank === 1 ? '🥇' : entry.rank === 2 ? '🥈' : '🥉'\n                        ) : (\n                          entry.rank\n                        )}\n                      </Rank>\n\n                      <img\n                        src={entry.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${entry.userId}`}\n                        alt={entry.username}\n                        style={{\n                          width: '48px',\n                          height: '48px',\n                          borderRadius: '50%',\n                          border: '2px solid rgba(255, 255, 255, 0.2)'\n                        }}\n                      />\n\n                      <div style={{ flex: 1, minWidth: 0 }}>\n                        <div style={{\n                          color: theme.colors.text.primary,\n                          fontWeight: theme.fontWeights.semibold,\n                          fontSize: theme.fontSizes.base,\n                          marginBottom: '2px'\n                        }}>\n                          {entry.username}\n                          {entry.isCurrentUser && ' (你)'}\n                        </div>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: theme.spacing[1],\n                          fontSize: theme.fontSizes.xs,\n                          color: theme.colors.text.secondary\n                        }}>\n                          <div style={{\n                            width: '8px',\n                            height: '8px',\n                            borderRadius: '50%',\n                            background: entry.isOnline ? '#10b981' : '#6b7280'\n                          }} />\n                          {entry.isOnline ? '在线' : '离线'}\n                          {entry.isFriend && ' • 好友'}\n                        </div>\n                      </div>\n\n                      <div style={{ textAlign: 'right' }}>\n                        <div style={{\n                          color: theme.colors.secondary[400],\n                          fontWeight: theme.fontWeights.bold,\n                          fontSize: theme.fontSizes.lg\n                        }}>\n                          {formatValue(entry.value, activeTab)}\n                        </div>\n                        <div style={{\n                          color: theme.colors.text.secondary,\n                          fontSize: theme.fontSizes.xs\n                        }}>\n                          {LEADERBOARD_CONFIG[activeTab].valueLabel}\n                        </div>\n                      </div>\n                    </LeaderboardEntry>\n                  ))}\n                </LeaderboardList>\n              )}\n            </>\n          )}\n        </LeaderboardContainer>\n\n        {/* 好友面板 */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing[4] }}>\n          <Card style={{ padding: theme.spacing[4] }}>\n            <h3 style={{\n              color: theme.colors.text.primary,\n              margin: '0 0 1rem 0',\n              display: 'flex',\n              alignItems: 'center',\n              gap: theme.spacing[2]\n            }}>\n              👥 好友动态\n            </h3>\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: theme.spacing[2]\n            }}>\n              {friends.slice(0, 5).map(friend => (\n                <div key={friend.id} style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: theme.spacing[2],\n                  padding: theme.spacing[2],\n                  background: 'rgba(255, 255, 255, 0.05)',\n                  borderRadius: theme.borderRadius.base\n                }}>\n                  <img\n                    src={friend.avatar}\n                    alt={friend.username}\n                    style={{\n                      width: '32px',\n                      height: '32px',\n                      borderRadius: '50%'\n                    }}\n                  />\n                  <div style={{ flex: 1, minWidth: 0 }}>\n                    <div style={{\n                      color: theme.colors.text.primary,\n                      fontSize: theme.fontSizes.sm,\n                      fontWeight: theme.fontWeights.medium\n                    }}>\n                      {friend.username}\n                    </div>\n                    <div style={{\n                      color: theme.colors.text.secondary,\n                      fontSize: theme.fontSizes.xs\n                    }}>\n                      等级 {friend.level} • {friend.totalScore.toLocaleString()} 分\n                    </div>\n                  </div>\n                  <div style={{\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    background: friend.isOnline ? '#10b981' : '#6b7280'\n                  }} />\n                </div>\n              ))}\n            </div>\n          </Card>\n\n          <Card style={{ padding: theme.spacing[4] }}>\n            <h3 style={{\n              color: theme.colors.text.primary,\n              margin: '0 0 1rem 0',\n              display: 'flex',\n              alignItems: 'center',\n              gap: theme.spacing[2]\n            }}>\n              📊 我的排名\n            </h3>\n            {leaderboardData?.currentUserEntry && (\n              <div style={{\n                background: 'rgba(251, 191, 36, 0.1)',\n                border: '1px solid rgba(251, 191, 36, 0.3)',\n                borderRadius: theme.borderRadius.lg,\n                padding: theme.spacing[3]\n              }}>\n                <div style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  marginBottom: theme.spacing[2]\n                }}>\n                  <span style={{ color: theme.colors.text.primary }}>当前排名</span>\n                  <span style={{\n                    color: theme.colors.secondary[400],\n                    fontWeight: theme.fontWeights.bold,\n                    fontSize: theme.fontSizes.lg\n                  }}>\n                    #{leaderboardData.currentUserRank}\n                  </span>\n                </div>\n                <div style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                }}>\n                  <span style={{ color: theme.colors.text.secondary, fontSize: theme.fontSizes.sm }}>\n                    {LEADERBOARD_CONFIG[activeTab].valueLabel}\n                  </span>\n                  <span style={{\n                    color: theme.colors.secondary[400],\n                    fontWeight: theme.fontWeights.semibold\n                  }}>\n                    {formatValue(leaderboardData.currentUserEntry.value, activeTab)}\n                  </span>\n                </div>\n              </div>\n            )}\n          </Card>\n        </div>\n      </ContentContainer>\n    </Container>\n  )\n}\n\n// 格式化数值显示\nfunction formatValue(value: number, type: LeaderboardType): string {\n  switch (type) {\n    case 'speed_run':\n      return `${value}秒`\n    case 'combo_record':\n      return `${value}连击`\n    case 'level_completion':\n      return `${value}关`\n    case 'achievement_count':\n      return `${value}个`\n    default:\n      return value.toLocaleString()\n  }\n}\n\nexport default LeaderboardPage\n"], "names": ["Container", "styled", "theme", "media", "Header", "Title", "TabContainer", "TabButton", "<PERSON><PERSON>", "props", "ContentContainer", "LeaderboardContainer", "Card", "LeaderboardHeader", "LeaderboardList", "LeaderboardEntry", "Rank", "LeaderboardPage", "activeTab", "setActiveTab", "useState", "leaderboardData", "setLeaderboardData", "friends", "setFriends", "loading", "setLoading", "useEffect", "generateMockLeaderboardData", "generateMockFriends", "tabs", "LEADERBOARD_CONFIG", "key", "config", "jsxs", "jsx", "tab", "Fragment", "entry", "formatValue", "friend", "value", "type"], "mappings": "6SAcA,MAAMA,EAAYC,EAAO;AAAA;AAAA,gBAETC,EAAM,OAAO,WAAW,OAAO;AAAA,aAClCA,EAAM,QAAQ,CAAC,CAAC,IAAIA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE7CC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC,IAAIA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG/CC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC,IAAIA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAI7CE,EAASH,EAAO;AAAA;AAAA,mBAEHC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE/BC,EAAM,KAAK;AAAA,qBACMD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAI/BG,EAAQJ,EAAO;AAAA,WACVC,EAAM,OAAO,KAAK,OAAO;AAAA,eACrBA,EAAM,UAAU,KAAK,CAAC;AAAA,iBACpBA,EAAM,YAAY,IAAI;AAAA,mBACpBA,EAAM,QAAQ,CAAC,CAAC;AAAA,iBAClBA,EAAM,QAAQ,IAAI;AAAA;AAAA,IAE/BC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,KAAK,CAAC;AAAA;AAAA;AAAA,IAGnCC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,KAAK,CAAC;AAAA;AAAA,EAIjCI,EAAeL,EAAO;AAAA;AAAA;AAAA,SAGnBC,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACNA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG/BC,EAAM,KAAK;AAAA,qBACMD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAI/BK,EAAYN,EAAOO,CAAM;AAAA,IAC3BC,GAASA,EAAM,SAAW;AAAA,kBACZP,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,oBAIzBA,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA;AAAA,GAE5C;AAAA;AAAA,IAECC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,EAAE;AAAA,eACpBA,EAAM,QAAQ,CAAC,CAAC,IAAIA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAI7CQ,EAAmBT,EAAO;AAAA;AAAA;AAAA,SAGvBC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,IAIrBC,EAAM,KAAK;AAAA;AAAA,WAEJD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBS,EAAuBV,EAAOW,CAAI;AAAA,aAC3BV,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAEzBC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG3BC,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIzBW,EAAoBZ,EAAO;AAAA;AAAA;AAAA;AAAA,mBAIdC,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,aAGtBA,EAAM,OAAO,KAAK,OAAO;AAAA,iBACrBA,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA,WAIxBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIdA,EAAM,OAAO,KAAK,SAAS;AAAA,iBACvBA,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BY,EAAkBb,EAAO;AAAA;AAAA;AAAA,SAGtBC,EAAM,QAAQ,CAAC,CAAC;AAAA,EAGnBa,EAAmBd,EAAO;AAAA;AAAA;AAAA,SAGvBC,EAAM,QAAQ,CAAC,CAAC;AAAA,aACZA,EAAM,QAAQ,CAAC,CAAC;AAAA,gBACbO,GAASA,EAAM,eACzB,0BACA,2BACJ;AAAA,sBACoBA,GAASA,EAAM,eAC/B,0BACA,0BACJ;AAAA,mBACiBP,EAAM,aAAa,EAAE;AAAA,oBACpBA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA,kBAGxBO,GAASA,EAAM,eACzB,2BACA,2BACJ;AAAA;AAAA;AAAA;AAAA,IAIAN,EAAM,KAAK;AAAA,eACAD,EAAM,QAAQ,CAAC,CAAC;AAAA,WACpBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAIrBc,EAAOf,EAAO;AAAA,eACLC,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,IAAI;AAAA,WAC5BO,GACHA,EAAM,QAAU,EAAU,UAC1BA,EAAM,QAAU,EAAU,UAC1BA,EAAM,QAAU,EAAU,UACvBP,EAAM,OAAO,KAAK,SAC1B;AAAA;AAAA;AAAA;AAAA,IAICC,EAAM,KAAK;AAAA,iBACED,EAAM,UAAU,IAAI;AAAA;AAAA;AAAA,EAK/Be,EAA4B,IAAM,CACtC,KAAM,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAA0B,cAAc,EACpE,CAACC,EAAiBC,CAAkB,EAAIF,EAAAA,SAAiC,IAAI,EAC7E,CAACG,EAASC,CAAU,EAAIJ,EAAAA,SAAmB,CAAA,CAAE,EAC7C,CAACK,EAASC,CAAU,EAAIN,EAAAA,SAAS,EAAK,EAG5CO,EAAAA,UAAU,IAAM,CACdD,EAAW,EAAI,EAEf,WAAW,IAAM,CACfJ,EAAmBM,EAA4BV,CAAS,CAAC,EACzDQ,EAAW,EAAK,CAClB,EAAG,GAAG,CACR,EAAG,CAACR,CAAS,CAAC,EAGdS,EAAAA,UAAU,IAAM,CACdH,EAAWK,GAAqB,CAClC,EAAG,CAAA,CAAE,EAEL,MAAMC,EAAO,OAAO,QAAQC,CAAkB,EAAE,IAAI,CAAC,CAACC,EAAKC,CAAM,KAAO,CACtE,IAAAD,EACA,GAAGC,CAAA,EACH,EAEF,cACGjC,EAAA,CACC,SAAA,CAAAkC,OAAC9B,EAAA,CACC,SAAA,CAAA+B,EAAAA,IAAC9B,GAAM,SAAA,QAAA,CAAM,EAEb8B,EAAAA,IAAC7B,EAAA,CACE,SAAAwB,EAAK,IAAIM,GACRF,EAAAA,KAAC3B,EAAA,CAEC,QAAQ,QACR,KAAK,KACL,QAASW,IAAckB,EAAI,IAC3B,QAAS,IAAMjB,EAAaiB,EAAI,GAAG,EAElC,SAAA,CAAAA,EAAI,KAAK,IAAEA,EAAI,IAAA,CAAA,EANXA,EAAI,GAAA,CAQZ,CAAA,CACH,CAAA,EACF,SAEC1B,EAAA,CACC,SAAA,CAAAyB,EAAAA,IAACxB,EAAA,CACE,YACCuB,OAAAG,EAAAA,SAAA,CACE,SAAA,CAAAH,OAACrB,EAAA,CACC,SAAA,CAAAqB,OAAC,KAAA,CACE,SAAA,CAAAH,EAAmBb,CAAS,EAAE,KAAK,IAAEa,EAAmBb,CAAS,EAAE,IAAA,EACtE,EACAgB,EAAAA,KAAC,MAAA,CAAI,UAAU,eAAe,SAAA,CAAA,SACrBb,EAAgB,YAAY,mBAAA,CAAmB,CAAA,CACxD,CAAA,EACF,EAECI,QACE,MAAA,CAAI,MAAO,CAAE,UAAW,SAAU,QAAS,OAAQ,MAAOvB,EAAM,OAAO,KAAK,SAAA,EAAa,SAAA,SAE1F,QAECY,EAAA,CACE,SAAAO,EAAgB,QAAQ,IAAIiB,GAC3BJ,EAAAA,KAACnB,EAAA,CAEC,eAAgBuB,EAAM,cAEtB,SAAA,CAAAH,MAACnB,GAAK,MAAOsB,EAAM,KAChB,SAAAA,EAAM,MAAQ,EACbA,EAAM,OAAS,EAAI,KAAOA,EAAM,OAAS,EAAI,KAAO,KAEpDA,EAAM,KAEV,EAEAH,EAAAA,IAAC,MAAA,CACC,IAAKG,EAAM,QAAU,mDAAmDA,EAAM,MAAM,GACpF,IAAKA,EAAM,SACX,MAAO,CACL,MAAO,OACP,OAAQ,OACR,aAAc,MACd,OAAQ,oCAAA,CACV,CAAA,EAGFJ,OAAC,OAAI,MAAO,CAAE,KAAM,EAAG,SAAU,GAC/B,SAAA,CAAAA,OAAC,OAAI,MAAO,CACV,MAAOhC,EAAM,OAAO,KAAK,QACzB,WAAYA,EAAM,YAAY,SAC9B,SAAUA,EAAM,UAAU,KAC1B,aAAc,KAAA,EAEb,SAAA,CAAAoC,EAAM,SACNA,EAAM,eAAiB,MAAA,EAC1B,EACAJ,OAAC,OAAI,MAAO,CACV,QAAS,OACT,WAAY,SACZ,IAAKhC,EAAM,QAAQ,CAAC,EACpB,SAAUA,EAAM,UAAU,GAC1B,MAAOA,EAAM,OAAO,KAAK,SAAA,EAEzB,SAAA,CAAAiC,MAAC,OAAI,MAAO,CACV,MAAO,MACP,OAAQ,MACR,aAAc,MACd,WAAYG,EAAM,SAAW,UAAY,SAAA,EACxC,EACFA,EAAM,SAAW,KAAO,KACxBA,EAAM,UAAY,OAAA,CAAA,CACrB,CAAA,EACF,SAEC,MAAA,CAAI,MAAO,CAAE,UAAW,SACvB,SAAA,CAAAH,MAAC,OAAI,MAAO,CACV,MAAOjC,EAAM,OAAO,UAAU,GAAG,EACjC,WAAYA,EAAM,YAAY,KAC9B,SAAUA,EAAM,UAAU,EAAA,EAEzB,SAAAqC,EAAYD,EAAM,MAAOpB,CAAS,CAAA,CACrC,EACAiB,MAAC,OAAI,MAAO,CACV,MAAOjC,EAAM,OAAO,KAAK,UACzB,SAAUA,EAAM,UAAU,EAAA,EAEzB,SAAA6B,EAAmBb,CAAS,EAAE,UAAA,CACjC,CAAA,CAAA,CACF,CAAA,CAAA,EAhEKoB,EAAM,EAAA,CAkEd,CAAA,CACH,CAAA,CAAA,CAEJ,CAAA,CAEJ,EAGAJ,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,cAAe,SAAU,IAAKhC,EAAM,QAAQ,CAAC,GAC1E,SAAA,CAAAgC,OAACtB,EAAA,CAAK,MAAO,CAAE,QAASV,EAAM,QAAQ,CAAC,GACrC,SAAA,CAAAiC,MAAC,MAAG,MAAO,CACT,MAAOjC,EAAM,OAAO,KAAK,QACzB,OAAQ,aACR,QAAS,OACT,WAAY,SACZ,IAAKA,EAAM,QAAQ,CAAC,CAAA,EACnB,SAAA,UAEH,EACAiC,MAAC,OAAI,MAAO,CACV,QAAS,OACT,cAAe,SACf,IAAKjC,EAAM,QAAQ,CAAC,CAAA,EAEnB,SAAAqB,EAAQ,MAAM,EAAG,CAAC,EAAE,IAAIiB,GACvBN,EAAAA,KAAC,MAAA,CAAoB,MAAO,CAC1B,QAAS,OACT,WAAY,SACZ,IAAKhC,EAAM,QAAQ,CAAC,EACpB,QAASA,EAAM,QAAQ,CAAC,EACxB,WAAY,4BACZ,aAAcA,EAAM,aAAa,IAAA,EAEjC,SAAA,CAAAiC,EAAAA,IAAC,MAAA,CACC,IAAKK,EAAO,OACZ,IAAKA,EAAO,SACZ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,aAAc,KAAA,CAChB,CAAA,EAEFN,OAAC,OAAI,MAAO,CAAE,KAAM,EAAG,SAAU,GAC/B,SAAA,CAAAC,MAAC,OAAI,MAAO,CACV,MAAOjC,EAAM,OAAO,KAAK,QACzB,SAAUA,EAAM,UAAU,GAC1B,WAAYA,EAAM,YAAY,MAAA,EAE7B,WAAO,SACV,EACAgC,OAAC,OAAI,MAAO,CACV,MAAOhC,EAAM,OAAO,KAAK,UACzB,SAAUA,EAAM,UAAU,EAAA,EACzB,SAAA,CAAA,MACGsC,EAAO,MAAM,MAAIA,EAAO,WAAW,eAAA,EAAiB,IAAA,CAAA,CAC1D,CAAA,EACF,EACAL,MAAC,OAAI,MAAO,CACV,MAAO,MACP,OAAQ,MACR,aAAc,MACd,WAAYK,EAAO,SAAW,UAAY,SAAA,CAC5C,CAAG,CAAA,GArCKA,EAAO,EAsCjB,CACD,CAAA,CACH,CAAA,EACF,EAEAN,OAACtB,GAAK,MAAO,CAAE,QAASV,EAAM,QAAQ,CAAC,CAAA,EACrC,SAAA,CAAAiC,MAAC,MAAG,MAAO,CACT,MAAOjC,EAAM,OAAO,KAAK,QACzB,OAAQ,aACR,QAAS,OACT,WAAY,SACZ,IAAKA,EAAM,QAAQ,CAAC,CAAA,EACnB,SAAA,UAEH,GACCmB,GAAA,YAAAA,EAAiB,mBAChBa,EAAAA,KAAC,MAAA,CAAI,MAAO,CACV,WAAY,0BACZ,OAAQ,oCACR,aAAchC,EAAM,aAAa,GACjC,QAASA,EAAM,QAAQ,CAAC,CAAA,EAExB,SAAA,CAAAgC,OAAC,OAAI,MAAO,CACV,QAAS,OACT,eAAgB,gBAChB,WAAY,SACZ,aAAchC,EAAM,QAAQ,CAAC,CAAA,EAE7B,SAAA,CAAAiC,EAAAA,IAAC,OAAA,CAAK,MAAO,CAAE,MAAOjC,EAAM,OAAO,KAAK,OAAA,EAAW,SAAA,MAAA,CAAI,EACvDgC,OAAC,QAAK,MAAO,CACX,MAAOhC,EAAM,OAAO,UAAU,GAAG,EACjC,WAAYA,EAAM,YAAY,KAC9B,SAAUA,EAAM,UAAU,EAAA,EACzB,SAAA,CAAA,IACCmB,EAAgB,eAAA,CAAA,CACpB,CAAA,EACF,EACAa,OAAC,OAAI,MAAO,CACV,QAAS,OACT,eAAgB,gBAChB,WAAY,QAAA,EAEZ,SAAA,CAAAC,MAAC,QAAK,MAAO,CAAE,MAAOjC,EAAM,OAAO,KAAK,UAAW,SAAUA,EAAM,UAAU,IAC1E,SAAA6B,EAAmBb,CAAS,EAAE,WACjC,EACAiB,MAAC,QAAK,MAAO,CACX,MAAOjC,EAAM,OAAO,UAAU,GAAG,EACjC,WAAYA,EAAM,YAAY,QAAA,EAE7B,SAAAqC,EAAYlB,EAAgB,iBAAiB,MAAOH,CAAS,CAAA,CAChE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EACF,CAEJ,EAGA,SAASqB,EAAYE,EAAeC,EAA+B,CACjE,OAAQA,EAAA,CACN,IAAK,YACH,MAAO,GAAGD,CAAK,IACjB,IAAK,eACH,MAAO,GAAGA,CAAK,KACjB,IAAK,mBACH,MAAO,GAAGA,CAAK,IACjB,IAAK,oBACH,MAAO,GAAGA,CAAK,IACjB,QACE,OAAOA,EAAM,eAAA,CAAe,CAElC"}