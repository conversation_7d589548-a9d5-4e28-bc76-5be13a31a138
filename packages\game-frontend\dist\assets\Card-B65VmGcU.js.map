{"version": 3, "file": "Card-B65VmGcU.js", "sources": ["../../src/components/UI/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport styled, { css } from 'styled-components'\nimport { theme, mixins } from '../../styles/theme'\n\nexport type CardVariant = 'default' | 'elevated' | 'outlined' | 'glass'\nexport type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: CardVariant\n  padding?: CardPadding\n  hoverable?: boolean\n  children: React.ReactNode\n}\n\nconst getVariantStyles = (variant: CardVariant) => {\n  switch (variant) {\n    case 'default':\n      return css`\n        ${mixins.glassmorphism}\n        box-shadow: ${theme.shadows.md};\n      `\n    \n    case 'elevated':\n      return css`\n        background: ${theme.colors.background.card};\n        backdrop-filter: blur(15px);\n        border: 1px solid rgba(255, 255, 255, 0.3);\n        box-shadow: ${theme.shadows.xl};\n      `\n    \n    case 'outlined':\n      return css`\n        background: transparent;\n        border: 2px solid rgba(255, 255, 255, 0.2);\n        box-shadow: none;\n      `\n    \n    case 'glass':\n      return css`\n        background: rgba(255, 255, 255, 0.05);\n        backdrop-filter: blur(20px);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        box-shadow: ${theme.shadows.inner}, ${theme.shadows.lg};\n      `\n    \n    default:\n      return css``\n  }\n}\n\nconst getPaddingStyles = (padding: CardPadding) => {\n  switch (padding) {\n    case 'none':\n      return css`padding: 0;`\n    \n    case 'sm':\n      return css`padding: ${theme.spacing[3]};`\n    \n    case 'md':\n      return css`padding: ${theme.spacing[4]};`\n    \n    case 'lg':\n      return css`padding: ${theme.spacing[6]};`\n    \n    case 'xl':\n      return css`padding: ${theme.spacing[8]};`\n    \n    default:\n      return css`padding: ${theme.spacing[4]};`\n  }\n}\n\nconst StyledCard = styled.div<{\n  $variant: CardVariant\n  $padding: CardPadding\n  $hoverable: boolean\n}>`\n  ${props => getVariantStyles(props.$variant)}\n  ${props => getPaddingStyles(props.$padding)}\n  \n  border-radius: ${theme.borderRadius.xl};\n  transition: all ${theme.transitions.base} ease-in-out;\n  position: relative;\n  overflow: hidden;\n  \n  ${props => props.$hoverable && css`\n    cursor: pointer;\n    \n    &:hover {\n      transform: translateY(-4px);\n      box-shadow: ${theme.shadows['2xl']};\n      \n      ${props.$variant === 'default' && css`\n        background: ${theme.colors.background.cardHover};\n      `}\n      \n      ${props.$variant === 'elevated' && css`\n        box-shadow: ${theme.shadows['2xl']}, ${theme.shadows.glowGold};\n      `}\n      \n      ${props.$variant === 'outlined' && css`\n        border-color: rgba(255, 255, 255, 0.4);\n        background: rgba(255, 255, 255, 0.05);\n      `}\n      \n      ${props.$variant === 'glass' && css`\n        background: rgba(255, 255, 255, 0.1);\n        border-color: rgba(255, 255, 255, 0.2);\n      `}\n    }\n    \n    &:active {\n      transform: translateY(-2px);\n    }\n  `}\n  \n  /* 添加微妙的光效 */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n    opacity: 0.5;\n  }\n  \n  /* 添加边缘高光 */\n  &::after {\n    content: '';\n    position: absolute;\n    top: 1px;\n    left: 1px;\n    right: 1px;\n    bottom: 1px;\n    border-radius: ${theme.borderRadius.lg};\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n    pointer-events: none;\n    opacity: 0.6;\n  }\n`\n\nconst Card: React.FC<CardProps> = ({\n  variant = 'default',\n  padding = 'md',\n  hoverable = false,\n  children,\n  ...props\n}) => {\n  return (\n    <StyledCard\n      $variant={variant}\n      $padding={padding}\n      $hoverable={hoverable}\n      {...props}\n    >\n      {children}\n    </StyledCard>\n  )\n}\n\nexport default Card\nexport { Card }\n\n// 预定义的卡片组件\nexport const GameCard = styled(Card)`\n  background: linear-gradient(135deg, \n    rgba(255, 255, 255, 0.1) 0%, \n    rgba(255, 255, 255, 0.05) 100%\n  );\n  border: 1px solid rgba(251, 191, 36, 0.2);\n  box-shadow: ${theme.shadows.lg}, 0 0 30px rgba(251, 191, 36, 0.1);\n  \n  &:hover {\n    border-color: rgba(251, 191, 36, 0.4);\n    box-shadow: ${theme.shadows.xl}, 0 0 40px rgba(251, 191, 36, 0.2);\n  }\n`\n\nexport const LevelCard = styled(Card)`\n  background: linear-gradient(135deg, \n    rgba(99, 102, 241, 0.1) 0%, \n    rgba(139, 92, 246, 0.1) 100%\n  );\n  border: 1px solid rgba(99, 102, 241, 0.2);\n  \n  &:hover {\n    border-color: rgba(99, 102, 241, 0.4);\n    box-shadow: ${theme.shadows.xl}, ${theme.shadows.glowPurple};\n  }\n`\n\nexport const StatsCard = styled(Card)`\n  background: linear-gradient(135deg, \n    rgba(16, 185, 129, 0.1) 0%, \n    rgba(5, 150, 105, 0.1) 100%\n  );\n  border: 1px solid rgba(16, 185, 129, 0.2);\n  \n  &:hover {\n    border-color: rgba(16, 185, 129, 0.4);\n    box-shadow: ${theme.shadows.xl}, 0 0 30px rgba(16, 185, 129, 0.2);\n  }\n`\n"], "names": ["getVariantStyles", "variant", "css", "mixins", "theme", "getPaddingStyles", "padding", "StyledCard", "styled", "props", "Card", "hoverable", "children", "jsx"], "mappings": "kGAcA,MAAMA,EAAoBC,GAAyB,CACjD,OAAQA,EAAA,CACN,IAAK,UACH,OAAOC;AAAAA,UACHC,EAAO,aAAa;AAAA,sBACRC,EAAM,QAAQ,EAAE;AAAA,QAGlC,IAAK,WACH,OAAOF;AAAAA,sBACSE,EAAM,OAAO,WAAW,IAAI;AAAA;AAAA;AAAA,sBAG5BA,EAAM,QAAQ,EAAE;AAAA,QAGlC,IAAK,WACH,OAAOF;AAAAA;AAAAA;AAAAA;AAAAA,QAMT,IAAK,QACH,OAAOA;AAAAA;AAAAA;AAAAA;AAAAA,sBAISE,EAAM,QAAQ,KAAK,KAAKA,EAAM,QAAQ,EAAE;AAAA,QAG1D,QACE,OAAOF,GAAA,CAEb,EAEMG,EAAoBC,GAAyB,CACjD,OAAQA,EAAA,CACN,IAAK,OACH,OAAOJ,eAET,IAAK,KACH,OAAOA,aAAeE,EAAM,QAAQ,CAAC,CAAC,IAExC,IAAK,KACH,OAAOF,aAAeE,EAAM,QAAQ,CAAC,CAAC,IAExC,IAAK,KACH,OAAOF,aAAeE,EAAM,QAAQ,CAAC,CAAC,IAExC,IAAK,KACH,OAAOF,aAAeE,EAAM,QAAQ,CAAC,CAAC,IAExC,QACE,OAAOF,aAAeE,EAAM,QAAQ,CAAC,CAAC,GAAA,CAE5C,EAEMG,EAAaC,EAAO;AAAA,IAKtBC,GAAST,EAAiBS,EAAM,QAAQ,CAAC;AAAA,IACzCA,GAASJ,EAAiBI,EAAM,QAAQ,CAAC;AAAA;AAAA,mBAE1BL,EAAM,aAAa,EAAE;AAAA,oBACpBA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA;AAAA,IAItCK,GAASA,EAAM,YAAcP;AAAAA;AAAAA;AAAAA;AAAAA;AAAAA,oBAKbE,EAAM,QAAQ,KAAK,CAAC;AAAA;AAAA,QAEhCK,EAAM,WAAa,WAAaP;AAAAA,sBAClBE,EAAM,OAAO,WAAW,SAAS;AAAA,OAChD;AAAA;AAAA,QAECK,EAAM,WAAa,YAAcP;AAAAA,sBACnBE,EAAM,QAAQ,KAAK,CAAC,KAAKA,EAAM,QAAQ,QAAQ;AAAA,OAC9D;AAAA;AAAA,QAECK,EAAM,WAAa,YAAcP;AAAAA;AAAAA;AAAAA,OAGlC;AAAA;AAAA,QAECO,EAAM,WAAa,SAAWP;AAAAA;AAAAA;AAAAA,OAG/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAsBkBE,EAAM,aAAa,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpCM,EAA4B,CAAC,CACjC,QAAAT,EAAU,UACV,QAAAK,EAAU,KACV,UAAAK,EAAY,GACZ,SAAAC,EACA,GAAGH,CACL,IAEII,EAAAA,IAACN,EAAA,CACC,SAAUN,EACV,SAAUK,EACV,WAAYK,EACX,GAAGF,EAEH,SAAAG,CAAA,CAAA,EASiBJ,EAAOE,CAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMnBN,EAAM,QAAQ,EAAE;AAAA;AAAA;AAAA;AAAA,kBAIdA,EAAM,QAAQ,EAAE;AAAA;AAAA,EAITI,EAAOE,CAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASlBN,EAAM,QAAQ,EAAE,KAAKA,EAAM,QAAQ,UAAU;AAAA;AAAA,EAItCI,EAAOE,CAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASlBN,EAAM,QAAQ,EAAE;AAAA;"}