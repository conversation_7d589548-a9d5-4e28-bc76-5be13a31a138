import{j as t,t as i,m as n}from"./index-CNCEp3EQ.js";import{d as s}from"./ui-ldAE8JkK.js";import{P as $,R as m}from"./powerups-DNw9s1Qv.js";import{B as f}from"./Button-BlkTGlvm.js";import{C as z}from"./Card-B65VmGcU.js";import"./vendor-Dneogk0_.js";import"./router-DMCr7QLp.js";const v=s.div`
  display: flex;
  flex-direction: column;
  gap: ${i.spacing[3]};
  padding: ${i.spacing[4]};
  background: ${i.colors.background.card};
  border-radius: ${i.borderRadius.xl};
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  ${n.maxMd} {
    padding: ${i.spacing[3]};
    gap: ${i.spacing[2]};
  }
`,C=s.h3`
  color: ${i.colors.text.primary};
  font-size: ${i.fontSizes.lg};
  font-weight: ${i.fontWeights.semibold};
  margin: 0;
  text-align: center;
  
  ${n.maxMd} {
    font-size: ${i.fontSizes.base};
  }
`,k=s.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: ${i.spacing[2]};
  
  ${n.maxMd} {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: ${i.spacing[1]};
  }
`,w=s(z)`
  padding: ${i.spacing[3]};
  cursor: ${o=>o.$disabled?"not-allowed":"pointer"};
  text-align: center;
  transition: all ${i.transitions.base} ease-in-out;
  position: relative;
  min-height: 80px;
  
  ${o=>{const e=m[o.$rarity];return`
      border: 2px solid ${o.$selected?e.color:e.borderColor};
      background: ${o.$selected?e.bgColor:"rgba(255, 255, 255, 0.05)"};
    `}}
  
  ${o=>o.$disabled&&`
    opacity: 0.5;
    filter: grayscale(50%);
  `}
  
  ${o=>!o.$disabled&&`
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${i.shadows.lg};
      border-color: ${m[o.$rarity].color};
    }
  `}
  
  ${n.maxMd} {
    padding: ${i.spacing[2]};
    min-height: 70px;
  }
`,M=s.div`
  font-size: 24px;
  margin-bottom: ${i.spacing[1]};
  
  ${n.maxMd} {
    font-size: 20px;
  }
`,R=s.div`
  font-size: ${i.fontSizes.xs};
  color: ${i.colors.text.secondary};
  font-weight: ${i.fontWeights.medium};
  line-height: 1.2;
  
  ${n.maxMd} {
    font-size: 10px;
  }
`,P=s.div`
  position: absolute;
  top: -8px;
  right: -8px;
  background: ${i.colors.secondary[500]};
  color: ${i.colors.gray[900]};
  border-radius: ${i.borderRadius.full};
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${i.fontSizes.xs};
  font-weight: ${i.fontWeights.bold};
  box-shadow: ${i.shadows.md};
  
  ${n.maxMd} {
    width: 18px;
    height: 18px;
    font-size: 10px;
  }
`,W=s.div`
  display: flex;
  gap: ${i.spacing[2]};
  margin-top: ${i.spacing[2]};
  
  ${n.maxMd} {
    flex-direction: column;
    gap: ${i.spacing[1]};
  }
`,h=s.div`
  background: rgba(0, 0, 0, 0.8);
  color: ${i.colors.text.primary};
  padding: ${i.spacing[3]};
  border-radius: ${i.borderRadius.lg};
  font-size: ${i.fontSizes.sm};
  line-height: 1.4;
  margin-top: ${i.spacing[2]};
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  ${n.maxMd} {
    padding: ${i.spacing[2]};
    font-size: ${i.fontSizes.xs};
  }
`,G=({powerUps:o,onUsePowerUp:e,selectedPowerUp:a,onSelectPowerUp:d,disabled:l=!1})=>{const u=r=>{l||d(a===r?null:r)},b=()=>{a&&!l&&(e(a),d(null))},y=()=>{d(null)},p=()=>o.filter(r=>r.quantity>0),c=a?$[a]:null;return t.jsxs(v,{children:[t.jsx(C,{children:"道具"}),t.jsx(k,{children:p().map(r=>{const g=$[r.type],j=a===r.type,x=l||r.quantity===0;return t.jsxs(w,{$selected:j,$disabled:x,$rarity:g.rarity,onClick:()=>u(r.type),hoverable:!x,children:[t.jsx(M,{children:g.icon}),t.jsx(R,{children:g.name.split(" ")[1]}),t.jsx(P,{children:r.quantity})]},r.type)})}),c&&t.jsxs(t.Fragment,{children:[t.jsxs(h,{children:[t.jsx("strong",{children:c.name}),t.jsx("br",{}),c.description]}),t.jsxs(W,{children:[t.jsx(f,{variant:"primary",size:"sm",onClick:b,disabled:l,fullWidth:!0,children:"使用道具"}),t.jsx(f,{variant:"ghost",size:"sm",onClick:y,fullWidth:!0,children:"取消"})]})]}),p().length===0&&t.jsx(h,{children:"暂无可用道具"})]})};export{G as default};
//# sourceMappingURL=PowerUpPanel-Cy3BrM2g.js.map
