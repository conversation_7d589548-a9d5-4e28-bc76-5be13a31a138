# 星光对对碰项目编程规范

## 项目架构原则

### 1. 分层架构
- **前端层**: React + TypeScript + Canvas游戏引擎
- **API网关层**: Express.js路由和中间件
- **服务层**: 业务逻辑服务
- **数据层**: SQLite/PostgreSQL + Redis缓存

### 2. 目录结构规范
```
packages/
├── shared/           # 共享类型和工具
├── game-frontend/    # 游戏前端
├── admin-panel/      # 管理后台
└── backend-api/      # 后端API
```

## TypeScript编程规范

### 1. 类型定义
```typescript
// ✅ 推荐：明确的接口定义
interface User {
  readonly id: string;
  username: string;
  email: string;
  createdAt: Date;
}

// ✅ 推荐：泛型API响应
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// ❌ 避免：使用any类型
// const user: any = {};
```

### 2. 函数规范
```typescript
// ✅ 推荐：明确的返回类型
async function getUserById(id: string): Promise<User | null> {
  try {
    return await userService.findById(id);
  } catch (error) {
    logger.error('Failed to get user', { id, error });
    throw new AppError('USER_NOT_FOUND', 404);
  }
}
```

## React组件规范

### 1. 组件结构
```typescript
interface ComponentProps {
  required: string;
  optional?: boolean;
  onAction: (data: ActionData) => void;
}

export const Component: React.FC<ComponentProps> = ({
  required,
  optional = false,
  onAction
}) => {
  // 1. Hooks在顶部
  const [state, setState] = useState<StateType>(initialState);
  
  // 2. 事件处理函数
  const handleClick = useCallback(() => {
    // 处理逻辑
  }, []);
  
  // 3. 副作用
  useEffect(() => {
    // 初始化逻辑
  }, []);
  
  // 4. 渲染
  return <div>{/* JSX */}</div>;
};
```

### 2. 自定义Hook规范
```typescript
interface UseFeatureReturn {
  data: DataType;
  actions: {
    create: (data: CreateData) => void;
    update: (id: string, data: UpdateData) => void;
    delete: (id: string) => void;
  };
  loading: boolean;
  error: string | null;
}

export function useFeature(): UseFeatureReturn {
  // Hook实现
}
```

## 后端API规范

### 1. 控制器规范
```typescript
export class UserController {
  constructor(
    private userService: UserService,
    private logger: Logger
  ) {}
  
  async getUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const user = await this.userService.findById(id);
      
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      this.logger.error('Failed to get user', { error });
      throw error;
    }
  }
}
```

### 2. 服务层规范
```typescript
export class UserService {
  constructor(
    private userRepository: UserRepository,
    private cacheService: CacheService
  ) {}
  
  async findById(id: string): Promise<User | null> {
    // 1. 检查缓存
    const cached = await this.cacheService.get(`user:${id}`);
    if (cached) return JSON.parse(cached);
    
    // 2. 查询数据库
    const user = await this.userRepository.findById(id);
    
    // 3. 更新缓存
    if (user) {
      await this.cacheService.set(`user:${id}`, JSON.stringify(user), 3600);
    }
    
    return user;
  }
}
```

## 错误处理规范

### 1. 自定义错误类
```typescript
export class AppError extends Error {
  constructor(
    public code: string,
    public statusCode: number,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super('VALIDATION_ERROR', 400, message, details);
  }
}
```

### 2. 错误处理中间件
```typescript
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (error instanceof AppError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message
      }
    });
  }
  
  res.status(500).json({
    success: false,
    error: { code: 'INTERNAL_ERROR', message: 'Internal server error' }
  });
};
```

## 数据库规范

### 1. 模型定义
```typescript
export interface User {
  id: string;
  username: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  username: string;
  email: string;
  password: string;
}
```

### 2. Repository模式
```typescript
export class UserRepository {
  async findById(id: string): Promise<User | null> {
    const query = `
      SELECT id, username, email, created_at, updated_at
      FROM users 
      WHERE id = ? AND is_active = 1
    `;
    
    const row = await this.db.get(query, [id]);
    return row ? this.mapRowToUser(row) : null;
  }
  
  private mapRowToUser(row: any): User {
    return {
      id: row.id,
      username: row.username,
      email: row.email,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
```

## 测试规范

### 1. 单元测试
```typescript
describe('UserService', () => {
  let userService: UserService;
  let mockRepository: jest.Mocked<UserRepository>;
  
  beforeEach(() => {
    mockRepository = createMockRepository();
    userService = new UserService(mockRepository);
  });
  
  it('should return user when found', async () => {
    // Arrange
    const userId = 'user-123';
    const expectedUser = { id: userId, username: 'test' };
    mockRepository.findById.mockResolvedValue(expectedUser);
    
    // Act
    const result = await userService.findById(userId);
    
    // Assert
    expect(result).toEqual(expectedUser);
    expect(mockRepository.findById).toHaveBeenCalledWith(userId);
  });
});
```

## 命名规范

### 1. 文件命名
- 组件文件: `PascalCase.tsx` (如: `GameBoard.tsx`)
- 工具文件: `camelCase.ts` (如: `gameUtils.ts`)
- 常量文件: `UPPER_SNAKE_CASE.ts` (如: `API_ENDPOINTS.ts`)

### 2. 变量命名
- 变量/函数: `camelCase` (如: `userName`, `getUserData`)
- 常量: `UPPER_SNAKE_CASE` (如: `MAX_RETRY_COUNT`)
- 类/接口: `PascalCase` (如: `UserService`, `ApiResponse`)

### 3. Git提交规范
```
feat: 添加用户认证功能
fix: 修复登录验证错误
docs: 更新API文档
style: 代码格式化
refactor: 重构用户服务
test: 添加单元测试
chore: 更新依赖
```

## 代码质量要求

### 1. ESLint规则
- 禁止使用 `any` 类型
- 要求明确的函数返回类型
- 禁止未使用的变量
- 要求使用 `const` 而非 `let`

### 2. 代码审查检查点
- [ ] 类型安全性
- [ ] 错误处理完整性
- [ ] 性能考虑
- [ ] 测试覆盖率
- [ ] 文档完整性

## 性能优化规范

### 1. 前端优化
- 使用 `React.memo` 避免不必要的重渲染
- 使用 `useCallback` 和 `useMemo` 优化计算
- 实现虚拟滚动处理大列表
- 使用代码分割和懒加载

### 2. 后端优化
- 实现数据库查询缓存
- 使用连接池管理数据库连接
- 实现API响应压缩
- 添加请求限流和防抖

## 安全规范

### 1. 认证授权
- 使用JWT token进行身份验证
- 实现角色基础的访问控制
- 敏感操作需要二次验证

### 2. 数据验证
- 所有输入数据必须验证
- 使用参数化查询防止SQL注入
- 实现XSS防护
- 添加CSRF保护

## 游戏开发特定规范

### 1. Canvas渲染优化
```typescript
class GameRenderer {
  private offscreenCanvas: OffscreenCanvas;
  private dirtyRegions: Set<Region> = new Set();

  render(): void {
    // 只重绘脏区域
    this.dirtyRegions.forEach(region => {
      this.renderRegion(region);
    });
    this.dirtyRegions.clear();
  }

  markDirty(region: Region): void {
    this.dirtyRegions.add(region);
  }
}
```

### 2. 游戏状态管理
```typescript
interface GameState {
  board: Cell[][];
  score: number;
  moves: number;
  status: 'playing' | 'paused' | 'completed' | 'failed';
  selectedCells: Position[];
}

class GameStateManager {
  private state: GameState;
  private history: GameState[] = [];

  makeMove(move: Move): void {
    this.saveState();
    this.applyMove(move);
    this.notifyObservers();
  }

  undo(): void {
    if (this.history.length > 0) {
      this.state = this.history.pop()!;
      this.notifyObservers();
    }
  }
}
```

### 3. 音效和动画管理
```typescript
class AudioManager {
  private sounds: Map<string, HTMLAudioElement> = new Map();
  private volume: number = 1.0;

  preloadSounds(soundPaths: Record<string, string>): void {
    Object.entries(soundPaths).forEach(([key, path]) => {
      const audio = new Audio(path);
      audio.preload = 'auto';
      this.sounds.set(key, audio);
    });
  }

  playSound(soundKey: string): void {
    const sound = this.sounds.get(soundKey);
    if (sound) {
      sound.currentTime = 0;
      sound.volume = this.volume;
      sound.play().catch(console.error);
    }
  }
}
```

## API设计规范

### 1. RESTful API设计
```
GET    /api/users           # 获取用户列表
GET    /api/users/:id       # 获取单个用户
POST   /api/users           # 创建用户
PUT    /api/users/:id       # 更新用户
DELETE /api/users/:id       # 删除用户

GET    /api/levels          # 获取关卡列表
GET    /api/levels/:id      # 获取关卡详情
POST   /api/game/start      # 开始游戏
POST   /api/game/move       # 提交移动
POST   /api/game/complete   # 完成游戏
```

### 2. 响应格式标准
```typescript
// 成功响应
{
  "success": true,
  "data": { /* 实际数据 */ },
  "message": "操作成功"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据无效",
    "details": { /* 详细错误信息 */ }
  }
}

// 分页响应
{
  "success": true,
  "data": {
    "items": [ /* 数据列表 */ ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```
