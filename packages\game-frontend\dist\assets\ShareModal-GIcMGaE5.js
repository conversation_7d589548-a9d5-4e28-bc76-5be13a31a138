import{j as o,t as e,m as a}from"./index-CNCEp3EQ.js";import{r as h}from"./vendor-Dneogk0_.js";import{d as r}from"./ui-ldAE8JkK.js";import{S as v}from"./leaderboard-Or32oZ16.js";import{B as g}from"./Button-BlkTGlvm.js";import"./router-DMCr7QLp.js";const w=r.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: ${e.zIndex.modal};
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${s=>s.$visible?1:0};
  visibility: ${s=>s.$visible?"visible":"hidden"};
  transition: all ${e.transitions.base} ease-in-out;
`,y=r.div`
  background: ${e.colors.background.card};
  border-radius: ${e.borderRadius.xl};
  padding: ${e.spacing[6]};
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: ${e.shadows.xl};
  transform: ${s=>s.$visible?"scale(1)":"scale(0.9)"};
  transition: all ${e.transitions.base} ease-in-out;
  
  ${a.maxMd} {
    padding: ${e.spacing[4]};
  }
  
  ${a.maxSm} {
    padding: ${e.spacing[3]};
    width: 95vw;
  }
`,S=r.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${e.spacing[4]};
`,z=r.h2`
  color: ${e.colors.text.primary};
  font-size: ${e.fontSizes.xl};
  font-weight: ${e.fontWeights.bold};
  margin: 0;
  
  ${a.maxMd} {
    font-size: ${e.fontSizes.lg};
  }
`,j=r.button`
  background: none;
  border: none;
  color: ${e.colors.text.secondary};
  font-size: ${e.fontSizes.xl};
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${e.borderRadius.full};
  transition: all ${e.transitions.fast} ease-in-out;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: ${e.colors.text.primary};
  }
`,k=r.div`
  text-align: center;
  margin-bottom: ${e.spacing[6]};
`,R=r.h3`
  color: ${e.colors.text.primary};
  font-size: ${e.fontSizes.lg};
  font-weight: ${e.fontWeights.semibold};
  margin: 0 0 ${e.spacing[2]} 0;
  
  ${a.maxMd} {
    font-size: ${e.fontSizes.base};
  }
`,C=r.p`
  color: ${e.colors.text.secondary};
  font-size: ${e.fontSizes.base};
  margin: 0 0 ${e.spacing[4]} 0;
  line-height: 1.5;
  
  ${a.maxMd} {
    font-size: ${e.fontSizes.sm};
  }
`,M=r.div`
  width: 200px;
  height: 200px;
  margin: 0 auto ${e.spacing[4]} auto;
  background: linear-gradient(135deg, ${e.colors.secondary[500]} 0%, ${e.colors.secondary[600]} 100%);
  border-radius: ${e.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64px;
  box-shadow: ${e.shadows.lg};
  
  ${a.maxMd} {
    width: 150px;
    height: 150px;
    font-size: 48px;
  }
  
  ${a.maxSm} {
    width: 120px;
    height: 120px;
    font-size: 36px;
  }
`,I=r.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: ${e.spacing[3]};
  margin-bottom: ${e.spacing[4]};
  padding: ${e.spacing[4]};
  background: rgba(255, 255, 255, 0.05);
  border-radius: ${e.borderRadius.lg};
  border: 1px solid rgba(255, 255, 255, 0.1);
`,U=r.div`
  text-align: center;
  
  .value {
    color: ${e.colors.secondary[400]};
    font-size: ${e.fontSizes.xl};
    font-weight: ${e.fontWeights.bold};
    margin-bottom: ${e.spacing[1]};
    
    ${a.maxMd} {
      font-size: ${e.fontSizes.lg};
    }
  }
  
  .label {
    color: ${e.colors.text.secondary};
    font-size: ${e.fontSizes.sm};
    
    ${a.maxMd} {
      font-size: ${e.fontSizes.xs};
    }
  }
`,q=r.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${e.spacing[3]};
  margin-bottom: ${e.spacing[4]};
  
  ${a.maxSm} {
    grid-template-columns: 1fr;
  }
`,T=r(g)`
  background: ${s=>s.$color};
  color: white;
  justify-content: flex-start;
  gap: ${e.spacing[2]};
  
  &:hover {
    background: ${s=>s.$color};
    opacity: 0.9;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
`,_=r.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: ${e.borderRadius.base};
  padding: ${e.spacing[3]};
  margin-bottom: ${e.spacing[4]};
  
  .label {
    color: ${e.colors.text.secondary};
    font-size: ${e.fontSizes.sm};
    margin-bottom: ${e.spacing[1]};
  }
  
  .url {
    color: ${e.colors.text.primary};
    font-size: ${e.fontSizes.sm};
    font-family: monospace;
    word-break: break-all;
    background: rgba(0, 0, 0, 0.2);
    padding: ${e.spacing[2]};
    border-radius: ${e.borderRadius.base};
    margin-bottom: ${e.spacing[2]};
  }
`,H=({isVisible:s,shareData:t,onClose:p})=>{const[b,m]=h.useState(!1),$=async i=>{if(!t)return;const n=t.url||window.location.href,l=`${t.title}
${t.description}`;switch(i){case"wechat":if(navigator.share)try{await navigator.share({title:t.title,text:t.description,url:n})}catch{console.log("分享取消或失败")}else c(n);break;case"weibo":const d=`https://service.weibo.com/share/share.php?url=${encodeURIComponent(n)}&title=${encodeURIComponent(l)}`;window.open(d,"_blank");break;case"qq":const f=`https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(n)}&title=${encodeURIComponent(t.title)}&summary=${encodeURIComponent(t.description)}`;window.open(f,"_blank");break;case"copy_link":c(n);break}},c=async i=>{try{await navigator.clipboard.writeText(i),m(!0),setTimeout(()=>m(!1),2e3)}catch(n){console.error("复制失败:",n)}},x=i=>{switch(i){case"score":return"🏆";case"achievement":return"🏅";case"level_completion":return"🎯";case"combo":return"⚡";default:return"🎮"}},u=()=>{if(!t)return null;const{data:i}=t,n=[];return i.score&&n.push({label:"分数",value:i.score.toLocaleString()}),i.level&&n.push({label:"关卡",value:i.level}),i.combo&&n.push({label:"连击",value:`${i.combo}x`}),o.jsx(I,{children:n.map((l,d)=>o.jsxs(U,{children:[o.jsx("div",{className:"value",children:l.value}),o.jsx("div",{className:"label",children:l.label})]},d))})};return t?o.jsx(w,{$visible:s,onClick:p,children:o.jsxs(y,{$visible:s,onClick:i=>i.stopPropagation(),children:[o.jsxs(S,{children:[o.jsx(z,{children:"分享成就"}),o.jsx(j,{onClick:p,children:"×"})]}),o.jsxs(k,{children:[o.jsx(M,{children:x(t.type)}),o.jsx(R,{children:t.title}),o.jsx(C,{children:t.description}),u()]}),o.jsx(q,{children:Object.entries(v).map(([i,n])=>o.jsxs(T,{$color:n.color,onClick:()=>$(i),disabled:!n.available,children:[o.jsx("span",{children:n.icon}),o.jsx("span",{children:n.name})]},i))}),t.url&&o.jsxs(_,{children:[o.jsx("div",{className:"label",children:"分享链接"}),o.jsx("div",{className:"url",children:t.url}),o.jsx(g,{variant:"ghost",size:"sm",onClick:()=>c(t.url),fullWidth:!0,children:b?"已复制!":"复制链接"})]})]})}):null};export{H as default};
//# sourceMappingURL=ShareModal-GIcMGaE5.js.map
