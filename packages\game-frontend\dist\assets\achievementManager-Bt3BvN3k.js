var n=Object.defineProperty;var i=(l,e,t)=>e in l?n(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var c=(l,e,t)=>i(l,typeof e!="symbol"?e+"":e,t);import{a as d}from"./achievements-BKGiRpq5.js";class m{constructor(e,t){c(this,"achievements",new Map);c(this,"playerStats");c(this,"onAchievementUnlocked");d.forEach(s=>{this.achievements.set(s.id,{...s})}),this.playerStats={totalScore:0,totalGamesPlayed:0,totalGamesWon:0,totalTimePlayed:0,levelsCompleted:0,highestLevel:0,perfectLevels:0,maxCombo:0,totalCombos:0,comboCount:{},specialGemsCreated:{},specialGemsUsed:{},powerupsUsed:{},powerupsPurchased:{},gemsMatched:0,totalMoves:0,fastestLevel:1/0,achievementsUnlocked:0,badgesCollected:[],titlesEarned:[],...e},this.onAchievementUnlocked=t}updateStats(e){const t={...this.playerStats};return Object.assign(this.playerStats,e),this.checkAchievements(t)}recordGameEnd(e){const t={totalScore:this.playerStats.totalScore+e.score,totalGamesPlayed:this.playerStats.totalGamesPlayed+1,totalTimePlayed:this.playerStats.totalTimePlayed+e.timePlayed,totalMoves:this.playerStats.totalMoves+e.moves,maxCombo:Math.max(this.playerStats.maxCombo,e.maxCombo),totalCombos:this.playerStats.totalCombos+(e.maxCombo>1?1:0)};if(e.won&&(t.totalGamesWon=this.playerStats.totalGamesWon+1,t.levelsCompleted=this.playerStats.levelsCompleted+1,t.highestLevel=Math.max(this.playerStats.highestLevel,e.level),e.perfect&&(t.perfectLevels=this.playerStats.perfectLevels+1),e.timePlayed<this.playerStats.fastestLevel&&(t.fastestLevel=e.timePlayed)),e.maxCombo>1){const a={...this.playerStats.comboCount};a[e.maxCombo]=(a[e.maxCombo]||0)+1,t.comboCount=a}const s={...this.playerStats.specialGemsCreated};Object.entries(e.specialGemsCreated).forEach(([a,r])=>{s[a]=(s[a]||0)+r}),t.specialGemsCreated=s;const o={...this.playerStats.powerupsUsed};return Object.entries(e.powerupsUsed).forEach(([a,r])=>{o[a]=(o[a]||0)+r}),t.powerupsUsed=o,this.updateStats(t)}checkAchievements(e){const t=[];for(const s of this.achievements.values()){if(s.unlocked)continue;if(this.checkAchievementCondition(s)){s.unlocked=!0,s.unlockedAt=new Date,s.progress=100;const a=this.createUnlockedEvent(s);t.push(a),this.applyRewards(s),this.onAchievementUnlocked&&this.onAchievementUnlocked(a)}else s.progress=this.calculateProgress(s)}return t}checkAchievementCondition(e){const{condition:t}=e,s=this.playerStats;switch(t.type){case"single_game_score":return!1;case"total_score":return s.totalScore>=t.target;case"levels_completed":return s.levelsCompleted>=t.target;case"perfect_levels":return s.perfectLevels>=t.target;case"max_combo":return s.maxCombo>=t.target;case"games_won":return s.totalGamesWon>=t.target;case"total_time_played":return s.totalTimePlayed>=t.target;case"fastest_level":return s.fastestLevel<=t.target;case"total_powerups_used":return Object.values(s.powerupsUsed).reduce((a,r)=>a+r,0)>=t.target;default:if(t.type.startsWith("special_gems_created_")){const a=t.type.replace("special_gems_created_","");return(s.specialGemsCreated[a]||0)>=t.target}return!1}}calculateProgress(e){const{condition:t}=e,s=this.playerStats;let o=0;switch(t.type){case"total_score":o=s.totalScore;break;case"levels_completed":o=s.levelsCompleted;break;case"perfect_levels":o=s.perfectLevels;break;case"max_combo":o=s.maxCombo;break;case"games_won":o=s.totalGamesWon;break;case"total_time_played":o=s.totalTimePlayed;break;case"total_powerups_used":o=Object.values(s.powerupsUsed).reduce((a,r)=>a+r,0);break;default:if(t.type.startsWith("special_gems_created_")){const a=t.type.replace("special_gems_created_","");o=s.specialGemsCreated[a]||0}break}return Math.min(100,o/t.target*100)}createUnlockedEvent(e){return{achievement:{...e},timestamp:new Date,newRewards:{coins:e.rewards.coins||0,powerups:e.rewards.powerups||[],titles:e.rewards.title?[e.rewards.title]:[],badges:e.rewards.badge?[e.rewards.badge]:[]}}}applyRewards(e){this.playerStats.achievementsUnlocked++,e.rewards.title&&this.playerStats.titlesEarned.push(e.rewards.title),e.rewards.badge&&this.playerStats.badgesCollected.push(e.rewards.badge)}checkSingleGameScore(e){const t=[];for(const s of this.achievements.values())if(!(s.unlocked||s.condition.type!=="single_game_score")&&e>=s.condition.target){s.unlocked=!0,s.unlockedAt=new Date,s.progress=100;const o=this.createUnlockedEvent(s);t.push(o),this.applyRewards(s),this.onAchievementUnlocked&&this.onAchievementUnlocked(o)}return t}getAllAchievements(){return Array.from(this.achievements.values()).filter(e=>!e.hidden||e.unlocked).sort((e,t)=>e.order-t.order)}getUnlockedAchievements(){return this.getAllAchievements().filter(e=>e.unlocked)}getInProgressAchievements(){return this.getAllAchievements().filter(e=>!e.unlocked&&e.progress>0)}getPlayerStats(){return{...this.playerStats}}getCompletionRate(){const e=this.getAllAchievements().length,t=this.getUnlockedAchievements().length;return e>0?t/e*100:0}}export{m as AchievementManager};
//# sourceMappingURL=achievementManager-Bt3BvN3k.js.map
