import React from 'react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
// import { LevelCard as UILevelCard } from '../components/UI/Card'
// import Button from '../components/UI/Button'
import { theme, media } from '../styles/theme'

const Container = styled.div`
  flex: 1;
  padding: 2rem;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;

  ${media.maxMd} {
    padding: 1rem;
  }

  ${media.maxSm} {
    padding: 0.5rem;
  }
`

const Title = styled.h1`
  color: ${theme.colors.secondary[400]};
  font-size: ${theme.fontSizes['4xl']};
  margin-bottom: 3rem;
  text-align: center;
  font-weight: ${theme.fontWeights.bold};
  font-family: ${theme.fonts.primary};
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.6);
  background: linear-gradient(135deg, ${theme.colors.secondary[300]} 0%, ${theme.colors.secondary[500]} 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  ${media.maxMd} {
    font-size: ${theme.fontSizes['3xl']};
    margin-bottom: 2rem;
  }

  ${media.maxSm} {
    font-size: ${theme.fontSizes['2xl']};
    margin-bottom: 1.5rem;
  }
`

const LevelsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto 3rem;

  ${media.maxMd} {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  ${media.maxSm} {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
`

const LevelCard = styled.div<{ isLocked: boolean; isCompleted: boolean }>`
  aspect-ratio: 1;
  background: ${props =>
    props.isCompleted
      ? `linear-gradient(135deg, ${theme.colors.success} 0%, #059669 100%)`
      : props.isLocked
        ? `linear-gradient(135deg, ${theme.colors.gray[700]} 0%, ${theme.colors.gray[800]} 100%)`
        : `linear-gradient(135deg, ${theme.colors.primary[500]} 0%, ${theme.colors.primary[600]} 100%)`
  };
  border-radius: ${theme.borderRadius.xl};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: ${props => props.isLocked ? 'not-allowed' : 'pointer'};
  transition: all ${theme.transitions.base} ease-in-out;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: ${theme.shadows.lg};

  &:hover {
    ${props => !props.isLocked && `
      transform: translateY(-6px);
      box-shadow: ${theme.shadows.xl}, ${props.isCompleted
        ? '0 0 30px rgba(16, 185, 129, 0.4)'
        : '0 0 30px rgba(99, 102, 241, 0.4)'
      };
      border-color: ${theme.colors.secondary[400]};
    `}
  }

  &:active {
    ${props => !props.isLocked && `
      transform: translateY(-2px);
    `}
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
  }

  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border-radius: ${theme.borderRadius.lg};
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
    pointer-events: none;
  }
`

const LevelNumber = styled.div`
  font-size: ${theme.fontSizes.xl};
  font-weight: ${theme.fontWeights.bold};
  color: ${theme.colors.text.primary};
  margin-bottom: ${theme.spacing[2]};
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  ${media.maxSm} {
    font-size: ${theme.fontSizes.lg};
  }
`

const LevelStars = styled.div`
  display: flex;
  gap: ${theme.spacing[1]};
  margin-bottom: ${theme.spacing[1]};
`

const Star = styled.div<{ filled: boolean }>`
  width: 14px;
  height: 14px;
  color: ${props => props.filled ? theme.colors.secondary[400] : 'rgba(255, 255, 255, 0.3)'};
  font-size: 14px;
  filter: ${props => props.filled ? 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.6))' : 'none'};
  transition: all ${theme.transitions.fast} ease-in-out;

  ${media.maxSm} {
    width: 12px;
    height: 12px;
    font-size: 12px;
  }
`

const LockIcon = styled.div`
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
`

const LevelInfo = styled.div`
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 0.25rem;
`

// Mock levels data
const mockLevels = Array.from({ length: 50 }, (_, index) => ({
  id: index + 1,
  levelNumber: index + 1,
  targetScore: 1000 + (index * 500),
  maxMoves: 20 - Math.floor(index / 10),
  difficulty: Math.floor(index / 10) + 1,
  isLocked: index > 4, // First 5 levels unlocked
  userProgress: index < 3 ? {
    isCompleted: true,
    stars: Math.floor(Math.random() * 3) + 1,
    bestScore: 1000 + (index * 600)
  } : null
}))

const LevelsPage: React.FC = () => {
  const navigate = useNavigate()

  // In a real app, this would fetch from the API
  const { data: levels = mockLevels, isLoading } = useQuery(
    'levels',
    async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch('/api/levels', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
        
        if (!response.ok) {
          throw new Error('Failed to fetch levels')
        }
        
        return response.json()
      } catch (error) {
        console.warn('Backend not available, using mock data')
        return mockLevels
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  )

  const handleLevelClick = (level: any) => {
    if (level.isLocked) return
    
    // Navigate to game with selected level
    navigate(`/game?level=${level.levelNumber}`)
  }

  if (isLoading) {
    return (
      <Container>
        <Title>关卡选择</Title>
        <div style={{ textAlign: 'center', color: 'white', marginTop: '2rem' }}>
          正在加载关卡...
        </div>
      </Container>
    )
  }

  return (
    <Container>
      <Title>关卡选择</Title>
      <LevelsGrid>
        {levels.map((level: any) => (
          <LevelCard
            key={level.id}
            isLocked={level.isLocked}
            isCompleted={!!level.userProgress?.isCompleted}
            onClick={() => handleLevelClick(level)}
          >
            {level.isLocked ? (
              <>
                <LockIcon>🔒</LockIcon>
                <LevelInfo>已锁定</LevelInfo>
              </>
            ) : (
              <>
                <LevelNumber>{level.levelNumber}</LevelNumber>
                {level.userProgress?.isCompleted && (
                  <LevelStars>
                    {[1, 2, 3].map(star => (
                      <Star key={star} filled={star <= (level.userProgress?.stars || 0)}>
                        ⭐
                      </Star>
                    ))}
                  </LevelStars>
                )}
                <LevelInfo>
                  目标: {level.targetScore?.toLocaleString()}<br />
                  步数: {level.maxMoves}
                </LevelInfo>
              </>
            )}
          </LevelCard>
        ))}
      </LevelsGrid>
    </Container>
  )
}

export default LevelsPage
