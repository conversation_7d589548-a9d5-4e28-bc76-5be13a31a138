{"version": 3, "file": "powerUpManager-DAya6dWG.js", "sources": ["../../src/utils/powerUpManager.ts"], "sourcesContent": ["import { \n  PowerUpType, \n  PowerUpResult, \n  PowerUpUsageParams, \n  PowerUpEffect,\n  POWER_UPS \n} from '../types/powerups'\n\nexport class PowerUpManager {\n  private activeEffects: Map<PowerUpType, PowerUpEffect> = new Map()\n  public onEffectChange?: (effects: PowerUpEffect[]) => void\n\n  constructor(onEffectChange?: (effects: PowerUpEffect[]) => void) {\n    this.onEffectChange = onEffectChange\n  }\n\n  // 使用道具\n  public usePowerUp(params: PowerUpUsageParams): PowerUpResult {\n    const { type, position, board } = params\n\n    switch (type) {\n      case 'bomb':\n        return this.useBomb(position!, board!)\n      \n      case 'rainbow':\n        return this.useRainbow(position!, board!)\n      \n      case 'hammer':\n        return this.useHammer(position!, board!)\n      \n      case 'shuffle':\n        return this.useShuffle(board!)\n      \n      case 'time_extend':\n        return this.useTimeExtend()\n      \n      case 'score_boost':\n        return this.useScoreBoost()\n      \n      case 'hint':\n        return this.useHint(board!)\n      \n      case 'freeze':\n        return this.useFreeze()\n      \n      default:\n        return {\n          success: false,\n          message: '未知的道具类型'\n        }\n    }\n  }\n\n  // 炸弹道具 - 消除3x3区域\n  private useBomb(position: { row: number; col: number }, board: number[][]): PowerUpResult {\n    const { row, col } = position\n    const gemsRemoved: Array<{ row: number; col: number }> = []\n    let scoreGained = 0\n\n    // 计算3x3区域\n    for (let r = Math.max(0, row - 1); r <= Math.min(board.length - 1, row + 1); r++) {\n      for (let c = Math.max(0, col - 1); c <= Math.min(board[0].length - 1, col + 1); c++) {\n        if (board[r][c] !== 0) {\n          gemsRemoved.push({ row: r, col: c })\n          scoreGained += 10 // 每个宝石10分\n        }\n      }\n    }\n\n    return {\n      success: true,\n      message: `炸弹消除了 ${gemsRemoved.length} 个宝石！`,\n      effects: {\n        gemsRemoved,\n        scoreGained\n      }\n    }\n  }\n\n  // 彩虹宝石 - 消除同色宝石\n  private useRainbow(position: { row: number; col: number }, board: number[][]): PowerUpResult {\n    const { row, col } = position\n    const targetColor = board[row][col]\n    \n    if (targetColor === 0) {\n      return {\n        success: false,\n        message: '请选择一个有效的宝石'\n      }\n    }\n\n    const gemsRemoved: Array<{ row: number; col: number }> = []\n    let scoreGained = 0\n\n    // 找到所有同色宝石\n    for (let r = 0; r < board.length; r++) {\n      for (let c = 0; c < board[0].length; c++) {\n        if (board[r][c] === targetColor) {\n          gemsRemoved.push({ row: r, col: c })\n          scoreGained += 15 // 每个宝石15分\n        }\n      }\n    }\n\n    return {\n      success: true,\n      message: `彩虹宝石消除了 ${gemsRemoved.length} 个同色宝石！`,\n      effects: {\n        gemsRemoved,\n        scoreGained\n      }\n    }\n  }\n\n  // 锤子道具 - 消除单个宝石\n  private useHammer(position: { row: number; col: number }, board: number[][]): PowerUpResult {\n    const { row, col } = position\n    \n    if (board[row][col] === 0) {\n      return {\n        success: false,\n        message: '请选择一个有效的宝石'\n      }\n    }\n\n    return {\n      success: true,\n      message: '锤子消除了选中的宝石！',\n      effects: {\n        gemsRemoved: [{ row, col }],\n        scoreGained: 5\n      }\n    }\n  }\n\n  // 洗牌道具\n  private useShuffle(_board: number[][]): PowerUpResult {\n    return {\n      success: true,\n      message: '棋盘已重新洗牌！',\n      effects: {\n        boardShuffled: true\n      }\n    }\n  }\n\n  // 时间延长道具\n  private useTimeExtend(): PowerUpResult {\n    return {\n      success: true,\n      message: '时间延长了30秒！',\n      effects: {\n        timeAdded: 30\n      }\n    }\n  }\n\n  // 分数加成道具\n  private useScoreBoost(): PowerUpResult {\n    const effect: PowerUpEffect = {\n      type: 'score_boost',\n      startTime: Date.now(),\n      duration: 60000, // 60秒\n      multiplier: 2,\n      active: true\n    }\n\n    this.activeEffects.set('score_boost', effect)\n    this.notifyEffectChange()\n\n    // 设置定时器移除效果\n    setTimeout(() => {\n      this.removeEffect('score_boost')\n    }, 60000)\n\n    return {\n      success: true,\n      message: '分数加成已激活！接下来60秒内分数翻倍！',\n      effects: {\n        effectApplied: effect\n      }\n    }\n  }\n\n  // 提示道具\n  private useHint(_board: number[][]): PowerUpResult {\n    // 这里应该调用游戏引擎的提示功能\n    return {\n      success: true,\n      message: '已显示可能的移动提示！'\n    }\n  }\n\n  // 时间冰冻道具\n  private useFreeze(): PowerUpResult {\n    const effect: PowerUpEffect = {\n      type: 'freeze',\n      startTime: Date.now(),\n      duration: 15000, // 15秒\n      active: true\n    }\n\n    this.activeEffects.set('freeze', effect)\n    this.notifyEffectChange()\n\n    // 设置定时器移除效果\n    setTimeout(() => {\n      this.removeEffect('freeze')\n    }, 15000)\n\n    return {\n      success: true,\n      message: '时间已冰冻15秒！',\n      effects: {\n        effectApplied: effect\n      }\n    }\n  }\n\n  // 获取活跃效果\n  public getActiveEffects(): PowerUpEffect[] {\n    return Array.from(this.activeEffects.values())\n  }\n\n  // 检查是否有特定效果\n  public hasEffect(type: PowerUpType): boolean {\n    return this.activeEffects.has(type)\n  }\n\n  // 获取特定效果\n  public getEffect(type: PowerUpType): PowerUpEffect | undefined {\n    return this.activeEffects.get(type)\n  }\n\n  // 移除效果\n  private removeEffect(type: PowerUpType): void {\n    this.activeEffects.delete(type)\n    this.notifyEffectChange()\n  }\n\n  // 通知效果变化\n  private notifyEffectChange(): void {\n    if (this.onEffectChange) {\n      this.onEffectChange(this.getActiveEffects())\n    }\n  }\n\n  // 清除所有效果\n  public clearAllEffects(): void {\n    this.activeEffects.clear()\n    this.notifyEffectChange()\n  }\n\n  // 计算分数倍数\n  public getScoreMultiplier(): number {\n    const scoreBoost = this.getEffect('score_boost')\n    return scoreBoost?.active ? (scoreBoost.multiplier || 1) : 1\n  }\n\n  // 检查时间是否冰冻\n  public isTimeFrozen(): boolean {\n    const freeze = this.getEffect('freeze')\n    return freeze?.active || false\n  }\n\n  // 更新效果状态（每帧调用）\n  public update(): void {\n    const now = Date.now()\n    let hasChanges = false\n\n    for (const [type, effect] of this.activeEffects.entries()) {\n      if (effect.duration && now - effect.startTime >= effect.duration) {\n        this.activeEffects.delete(type)\n        hasChanges = true\n      }\n    }\n\n    if (hasChanges) {\n      this.notifyEffectChange()\n    }\n  }\n\n  // 获取道具信息\n  public getPowerUpInfo(type: PowerUpType) {\n    return POWER_UPS[type]\n  }\n}\n"], "names": ["PowerUpManager", "onEffectChange", "__publicField", "params", "type", "position", "board", "row", "col", "gemsRemoved", "scoreGained", "c", "targetColor", "r", "_board", "effect", "scoreBoost", "freeze", "now", "has<PERSON><PERSON><PERSON>", "POWER_UPS"], "mappings": "+MAQO,MAAMA,CAAe,CAI1B,YAAYC,EAAqD,CAHzDC,EAAA,yBAAqD,KACtDA,EAAA,uBAGL,KAAK,eAAiBD,CACxB,CAGO,WAAWE,EAA2C,CAC3D,KAAM,CAAE,KAAAC,EAAM,SAAAC,EAAU,MAAAC,CAAA,EAAUH,EAElC,OAAQC,EAAA,CACN,IAAK,OACH,OAAO,KAAK,QAAQC,EAAWC,CAAM,EAEvC,IAAK,UACH,OAAO,KAAK,WAAWD,EAAWC,CAAM,EAE1C,IAAK,SACH,OAAO,KAAK,UAAUD,EAAWC,CAAM,EAEzC,IAAK,UACH,OAAO,KAAK,WAAWA,CAAM,EAE/B,IAAK,cACH,OAAO,KAAK,cAAA,EAEd,IAAK,cACH,OAAO,KAAK,cAAA,EAEd,IAAK,OACH,OAAO,KAAK,QAAQA,CAAM,EAE5B,IAAK,SACH,OAAO,KAAK,UAAA,EAEd,QACE,MAAO,CACL,QAAS,GACT,QAAS,SAAA,CACX,CAEN,CAGQ,QAAQD,EAAwCC,EAAkC,CACxF,KAAM,CAAE,IAAAC,EAAK,IAAAC,CAAA,EAAQH,EACfI,EAAmD,CAAA,EACzD,IAAIC,EAAc,EAGlB,QAAS,EAAI,KAAK,IAAI,EAAGH,EAAM,CAAC,EAAG,GAAK,KAAK,IAAID,EAAM,OAAS,EAAGC,EAAM,CAAC,EAAG,IAC3E,QAASI,EAAI,KAAK,IAAI,EAAGH,EAAM,CAAC,EAAGG,GAAK,KAAK,IAAIL,EAAM,CAAC,EAAE,OAAS,EAAGE,EAAM,CAAC,EAAGG,IAC1EL,EAAM,CAAC,EAAEK,CAAC,IAAM,IAClBF,EAAY,KAAK,CAAE,IAAK,EAAG,IAAKE,EAAG,EACnCD,GAAe,IAKrB,MAAO,CACL,QAAS,GACT,QAAS,SAASD,EAAY,MAAM,QACpC,QAAS,CACP,YAAAA,EACA,YAAAC,CAAA,CACF,CAEJ,CAGQ,WAAWL,EAAwCC,EAAkC,CAC3F,KAAM,CAAE,IAAAC,EAAK,IAAAC,CAAA,EAAQH,EACfO,EAAcN,EAAMC,CAAG,EAAEC,CAAG,EAElC,GAAII,IAAgB,EAClB,MAAO,CACL,QAAS,GACT,QAAS,YAAA,EAIb,MAAMH,EAAmD,CAAA,EACzD,IAAIC,EAAc,EAGlB,QAASG,EAAI,EAAGA,EAAIP,EAAM,OAAQO,IAChC,QAASF,EAAI,EAAGA,EAAIL,EAAM,CAAC,EAAE,OAAQK,IAC/BL,EAAMO,CAAC,EAAEF,CAAC,IAAMC,IAClBH,EAAY,KAAK,CAAE,IAAKI,EAAG,IAAKF,EAAG,EACnCD,GAAe,IAKrB,MAAO,CACL,QAAS,GACT,QAAS,WAAWD,EAAY,MAAM,UACtC,QAAS,CACP,YAAAA,EACA,YAAAC,CAAA,CACF,CAEJ,CAGQ,UAAUL,EAAwCC,EAAkC,CAC1F,KAAM,CAAE,IAAAC,EAAK,IAAAC,CAAA,EAAQH,EAErB,OAAIC,EAAMC,CAAG,EAAEC,CAAG,IAAM,EACf,CACL,QAAS,GACT,QAAS,YAAA,EAIN,CACL,QAAS,GACT,QAAS,cACT,QAAS,CACP,YAAa,CAAC,CAAE,IAAAD,EAAK,IAAAC,EAAK,EAC1B,YAAa,CAAA,CACf,CAEJ,CAGQ,WAAWM,EAAmC,CACpD,MAAO,CACL,QAAS,GACT,QAAS,WACT,QAAS,CACP,cAAe,EAAA,CACjB,CAEJ,CAGQ,eAA+B,CACrC,MAAO,CACL,QAAS,GACT,QAAS,YACT,QAAS,CACP,UAAW,EAAA,CACb,CAEJ,CAGQ,eAA+B,CACrC,MAAMC,EAAwB,CAC5B,KAAM,cACN,UAAW,KAAK,IAAA,EAChB,SAAU,IACV,WAAY,EACZ,OAAQ,EAAA,EAGV,YAAK,cAAc,IAAI,cAAeA,CAAM,EAC5C,KAAK,mBAAA,EAGL,WAAW,IAAM,CACf,KAAK,aAAa,aAAa,CACjC,EAAG,GAAK,EAED,CACL,QAAS,GACT,QAAS,uBACT,QAAS,CACP,cAAeA,CAAA,CACjB,CAEJ,CAGQ,QAAQD,EAAmC,CAEjD,MAAO,CACL,QAAS,GACT,QAAS,aAAA,CAEb,CAGQ,WAA2B,CACjC,MAAMC,EAAwB,CAC5B,KAAM,SACN,UAAW,KAAK,IAAA,EAChB,SAAU,KACV,OAAQ,EAAA,EAGV,YAAK,cAAc,IAAI,SAAUA,CAAM,EACvC,KAAK,mBAAA,EAGL,WAAW,IAAM,CACf,KAAK,aAAa,QAAQ,CAC5B,EAAG,IAAK,EAED,CACL,QAAS,GACT,QAAS,YACT,QAAS,CACP,cAAeA,CAAA,CACjB,CAEJ,CAGO,kBAAoC,CACzC,OAAO,MAAM,KAAK,KAAK,cAAc,QAAQ,CAC/C,CAGO,UAAUX,EAA4B,CAC3C,OAAO,KAAK,cAAc,IAAIA,CAAI,CACpC,CAGO,UAAUA,EAA8C,CAC7D,OAAO,KAAK,cAAc,IAAIA,CAAI,CACpC,CAGQ,aAAaA,EAAyB,CAC5C,KAAK,cAAc,OAAOA,CAAI,EAC9B,KAAK,mBAAA,CACP,CAGQ,oBAA2B,CAC7B,KAAK,gBACP,KAAK,eAAe,KAAK,kBAAkB,CAE/C,CAGO,iBAAwB,CAC7B,KAAK,cAAc,MAAA,EACnB,KAAK,mBAAA,CACP,CAGO,oBAA6B,CAClC,MAAMY,EAAa,KAAK,UAAU,aAAa,EAC/C,OAAOA,GAAA,MAAAA,EAAY,QAAUA,EAAW,YAAc,CACxD,CAGO,cAAwB,CAC7B,MAAMC,EAAS,KAAK,UAAU,QAAQ,EACtC,OAAOA,GAAA,YAAAA,EAAQ,SAAU,EAC3B,CAGO,QAAe,CACpB,MAAMC,EAAM,KAAK,IAAA,EACjB,IAAIC,EAAa,GAEjB,SAAW,CAACf,EAAMW,CAAM,IAAK,KAAK,cAAc,UAC1CA,EAAO,UAAYG,EAAMH,EAAO,WAAaA,EAAO,WACtD,KAAK,cAAc,OAAOX,CAAI,EAC9Be,EAAa,IAIbA,GACF,KAAK,mBAAA,CAET,CAGO,eAAef,EAAmB,CACvC,OAAOgB,EAAUhB,CAAI,CACvB,CACF"}