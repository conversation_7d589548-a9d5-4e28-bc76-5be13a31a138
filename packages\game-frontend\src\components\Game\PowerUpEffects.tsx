import React, { useEffect, useState } from 'react'
import styled, { keyframes, css } from 'styled-components'
import { PowerUpEffect, POWER_UPS } from '../../types/powerups'
import { theme, media } from '../../styles/theme'

interface PowerUpEffectsProps {
  effects: PowerUpEffect[]
}

const pulse = keyframes`
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
`

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`

// const slideOut = keyframes`
  // from {
  //   opacity: 1;
  //   transform: translateX(0);
  // }
  // to {
  //   opacity: 0;
  //   transform: translateX(-100%);
  // }
// `

const Container = styled.div`
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: ${theme.zIndex.fixed};
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
  pointer-events: none;
  
  ${media.maxMd} {
    top: 10px;
    left: 10px;
  }
`

const EffectCard = styled.div<{ $isExpiring: boolean }>`
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing[3]};
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  min-width: 200px;
  box-shadow: ${theme.shadows.lg};
  animation: ${slideIn} 0.3s ease-out;
  
  ${props => props.$isExpiring && css`
    animation: ${pulse} 0.5s ease-in-out infinite;
  `}
  
  ${media.maxMd} {
    padding: ${theme.spacing[2]};
    min-width: 160px;
  }
`

const EffectIcon = styled.div`
  font-size: 24px;
  flex-shrink: 0;
  
  ${media.maxMd} {
    font-size: 20px;
  }
`

const EffectContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[1]};
`

const EffectName = styled.div`
  color: ${theme.colors.text.primary};
  font-size: ${theme.fontSizes.sm};
  font-weight: ${theme.fontWeights.semibold};
  
  ${media.maxMd} {
    font-size: ${theme.fontSizes.xs};
  }
`

const EffectTimer = styled.div`
  color: ${theme.colors.text.secondary};
  font-size: ${theme.fontSizes.xs};
  font-family: ${theme.fonts.mono};
  
  ${media.maxMd} {
    font-size: 10px;
  }
`

const ProgressBar = styled.div`
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: ${theme.borderRadius.full};
  overflow: hidden;
`

const ProgressFill = styled.div<{ $progress: number; $color: string }>`
  height: 100%;
  background: ${props => props.$color};
  width: ${props => props.$progress}%;
  transition: width 0.1s linear;
  border-radius: ${theme.borderRadius.full};
  box-shadow: 0 0 10px ${props => props.$color}40;
`

const getEffectColor = (type: string): string => {
  switch (type) {
    case 'score_boost':
      return theme.colors.secondary[400]
    case 'freeze':
      return '#60a5fa'
    case 'time_extend':
      return theme.colors.success
    default:
      return theme.colors.primary[400]
  }
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`
  }
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const PowerUpEffects: React.FC<PowerUpEffectsProps> = ({ effects }) => {
  const [timers, setTimers] = useState<Record<string, number>>({})

  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now()
      const newTimers: Record<string, number> = {}

      effects.forEach(effect => {
        if (effect.duration) {
          const elapsed = now - effect.startTime
          const remaining = Math.max(0, effect.duration - elapsed)
          newTimers[effect.type] = Math.ceil(remaining / 1000)
        }
      })

      setTimers(newTimers)
    }, 100)

    return () => clearInterval(interval)
  }, [effects])

  if (effects.length === 0) {
    return null
  }

  return (
    <Container>
      {effects.map(effect => {
        const powerUp = POWER_UPS[effect.type]
        const remainingTime = timers[effect.type] || 0
        const isExpiring = remainingTime <= 5 && remainingTime > 0
        const progress = effect.duration 
          ? ((effect.duration - (Date.now() - effect.startTime)) / effect.duration) * 100
          : 100
        const color = getEffectColor(effect.type)

        return (
          <EffectCard key={effect.type} $isExpiring={isExpiring}>
            <EffectIcon>{powerUp.icon}</EffectIcon>
            <EffectContent>
              <EffectName>{powerUp.name}</EffectName>
              {effect.duration && (
                <>
                  <EffectTimer>{formatTime(remainingTime)}</EffectTimer>
                  <ProgressBar>
                    <ProgressFill $progress={Math.max(0, progress)} $color={color} />
                  </ProgressBar>
                </>
              )}
              {effect.multiplier && (
                <EffectTimer>倍数: {effect.multiplier}x</EffectTimer>
              )}
            </EffectContent>
          </EffectCard>
        )
      })}
    </Container>
  )
}

export default PowerUpEffects
