const e=[{id:"score_1k",name:"初出茅庐",description:"单局得分达到1,000分",icon:"🌟",category:"score",rarity:"common",condition:{type:"single_game_score",target:1e3},rewards:{coins:50},unlocked:!1,progress:0,order:1},{id:"score_10k",name:"小有成就",description:"单局得分达到10,000分",icon:"⭐",category:"score",rarity:"rare",condition:{type:"single_game_score",target:1e4},rewards:{coins:200,powerups:[{type:"bomb",quantity:3}]},unlocked:!1,progress:0,order:2},{id:"score_100k",name:"分数大师",description:"单局得分达到100,000分",icon:"🏆",category:"score",rarity:"epic",condition:{type:"single_game_score",target:1e5},rewards:{coins:1e3,title:"分数大师"},unlocked:!1,progress:0,order:3},{id:"combo_5",name:"连击新手",description:"达成5连击",icon:"🔥",category:"combo",rarity:"common",condition:{type:"max_combo",target:5},rewards:{coins:30},unlocked:!1,progress:0,order:10},{id:"combo_10",name:"连击高手",description:"达成10连击",icon:"💥",category:"combo",rarity:"rare",condition:{type:"max_combo",target:10},rewards:{coins:100,powerups:[{type:"rainbow",quantity:1}]},unlocked:!1,progress:0,order:11},{id:"combo_20",name:"连击传说",description:"达成20连击",icon:"⚡",category:"combo",rarity:"legendary",condition:{type:"max_combo",target:20},rewards:{coins:500,title:"连击传说",badge:"combo_master"},unlocked:!1,progress:0,order:12},{id:"level_10",name:"探索者",description:"完成10个关卡",icon:"🗺️",category:"level",rarity:"common",condition:{type:"levels_completed",target:10},rewards:{coins:100},unlocked:!1,progress:0,order:20},{id:"level_50",name:"冒险家",description:"完成50个关卡",icon:"🎒",category:"level",rarity:"rare",condition:{type:"levels_completed",target:50},rewards:{coins:500,powerups:[{type:"shuffle",quantity:5}]},unlocked:!1,progress:0,order:21},{id:"perfect_10",name:"完美主义者",description:"三星通关10个关卡",icon:"✨",category:"level",rarity:"epic",condition:{type:"perfect_levels",target:10},rewards:{coins:800,title:"完美主义者"},unlocked:!1,progress:0,order:22},{id:"special_bomb_10",name:"爆破专家",description:"创造10个炸弹宝石",icon:"💣",category:"special",rarity:"common",condition:{type:"special_gems_created_bomb",target:10},rewards:{coins:80},unlocked:!1,progress:0,order:30},{id:"special_rainbow_5",name:"彩虹收集者",description:"创造5个彩虹宝石",icon:"🌈",category:"special",rarity:"rare",condition:{type:"special_gems_created_rainbow",target:5},rewards:{coins:200,powerups:[{type:"rainbow",quantity:2}]},unlocked:!1,progress:0,order:31},{id:"powerup_user",name:"道具使用者",description:"使用50个道具",icon:"🔧",category:"powerup",rarity:"common",condition:{type:"total_powerups_used",target:50},rewards:{coins:150},unlocked:!1,progress:0,order:40},{id:"speed_runner",name:"速度之星",description:"在30秒内完成一个关卡",icon:"⚡",category:"time",rarity:"rare",condition:{type:"fastest_level",target:30},rewards:{coins:300,title:"速度之星"},unlocked:!1,progress:0,order:50},{id:"first_win",name:"首次胜利",description:"完成你的第一个关卡",icon:"🎉",category:"milestone",rarity:"common",condition:{type:"games_won",target:1},rewards:{coins:100,powerups:[{type:"hint",quantity:5}]},unlocked:!1,progress:0,order:100},{id:"dedication",name:"坚持不懈",description:"游戏总时长达到1小时",icon:"⏰",category:"milestone",rarity:"rare",condition:{type:"total_time_played",target:3600},rewards:{coins:500,badge:"dedicated_player"},unlocked:!1,progress:0,order:101}],r={common:{color:"#9ca3af",bgColor:"rgba(156, 163, 175, 0.1)",borderColor:"rgba(156, 163, 175, 0.3)",name:"普通",glow:"0 0 10px rgba(156, 163, 175, 0.3)"},rare:{color:"#3b82f6",bgColor:"rgba(59, 130, 246, 0.1)",borderColor:"rgba(59, 130, 246, 0.3)",name:"稀有",glow:"0 0 15px rgba(59, 130, 246, 0.4)"},epic:{color:"#8b5cf6",bgColor:"rgba(139, 92, 246, 0.1)",borderColor:"rgba(139, 92, 246, 0.3)",name:"史诗",glow:"0 0 20px rgba(139, 92, 246, 0.5)"},legendary:{color:"#f59e0b",bgColor:"rgba(245, 158, 11, 0.1)",borderColor:"rgba(245, 158, 11, 0.3)",name:"传说",glow:"0 0 25px rgba(245, 158, 11, 0.6)"}};export{r as A,e as a};
//# sourceMappingURL=achievements-BKGiRpq5.js.map
