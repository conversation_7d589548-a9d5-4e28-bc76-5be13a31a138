import sqlite3 from 'sqlite3';
export declare class Database {
    private static instance;
    private db;
    private constructor();
    static getInstance(): Database;
    static initialize(): Promise<void>;
    private connect;
    getDb(): sqlite3.Database;
    run(sql: string, params?: any[]): Promise<sqlite3.RunResult>;
    get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
    all<T = any>(sql: string, params?: any[]): Promise<T[]>;
    private createTables;
    private seedInitialData;
    static close(): void;
}
//# sourceMappingURL=database.d.ts.map