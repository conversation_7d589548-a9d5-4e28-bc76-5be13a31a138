{"version": 3, "file": "achievements-BKGiRpq5.js", "sources": ["../../src/types/achievements.ts"], "sourcesContent": ["export type AchievementCategory = \n  | 'score'        // 分数相关\n  | 'combo'        // 连击相关\n  | 'level'        // 关卡相关\n  | 'special'      // 特殊宝石相关\n  | 'powerup'      // 道具相关\n  | 'time'         // 时间相关\n  | 'collection'   // 收集相关\n  | 'milestone'    // 里程碑\n\nexport type AchievementRarity = 'common' | 'rare' | 'epic' | 'legendary'\n\nexport interface Achievement {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: AchievementCategory\n  rarity: AchievementRarity\n  \n  // 解锁条件\n  condition: {\n    type: string\n    target: number\n    current?: number\n  }\n  \n  // 奖励\n  rewards: {\n    coins?: number\n    powerups?: Array<{ type: string; quantity: number }>\n    title?: string\n    badge?: string\n  }\n  \n  // 状态\n  unlocked: boolean\n  unlockedAt?: Date\n  progress: number // 0-100\n  \n  // 显示相关\n  hidden?: boolean // 隐藏成就，直到解锁\n  order: number\n}\n\nexport interface AchievementProgress {\n  achievementId: string\n  current: number\n  target: number\n  lastUpdated: Date\n}\n\nexport interface PlayerStats {\n  // 基础统计\n  totalScore: number\n  totalGamesPlayed: number\n  totalGamesWon: number\n  totalTimePlayed: number // 秒\n  \n  // 关卡统计\n  levelsCompleted: number\n  highestLevel: number\n  perfectLevels: number // 三星通关\n  \n  // 连击统计\n  maxCombo: number\n  totalCombos: number\n  comboCount: Record<number, number> // 各连击数的次数\n  \n  // 特殊宝石统计\n  specialGemsCreated: Record<string, number>\n  specialGemsUsed: Record<string, number>\n  \n  // 道具统计\n  powerupsUsed: Record<string, number>\n  powerupsPurchased: Record<string, number>\n  \n  // 其他统计\n  gemsMatched: number\n  totalMoves: number\n  fastestLevel: number // 最快通关时间（秒）\n  \n  // 收集统计\n  achievementsUnlocked: number\n  badgesCollected: string[]\n  titlesEarned: string[]\n}\n\n// 预定义成就\nexport const ACHIEVEMENTS: Achievement[] = [\n  // 分数相关\n  {\n    id: 'score_1k',\n    name: '初出茅庐',\n    description: '单局得分达到1,000分',\n    icon: '🌟',\n    category: 'score',\n    rarity: 'common',\n    condition: { type: 'single_game_score', target: 1000 },\n    rewards: { coins: 50 },\n    unlocked: false,\n    progress: 0,\n    order: 1\n  },\n  {\n    id: 'score_10k',\n    name: '小有成就',\n    description: '单局得分达到10,000分',\n    icon: '⭐',\n    category: 'score',\n    rarity: 'rare',\n    condition: { type: 'single_game_score', target: 10000 },\n    rewards: { coins: 200, powerups: [{ type: 'bomb', quantity: 3 }] },\n    unlocked: false,\n    progress: 0,\n    order: 2\n  },\n  {\n    id: 'score_100k',\n    name: '分数大师',\n    description: '单局得分达到100,000分',\n    icon: '🏆',\n    category: 'score',\n    rarity: 'epic',\n    condition: { type: 'single_game_score', target: 100000 },\n    rewards: { coins: 1000, title: '分数大师' },\n    unlocked: false,\n    progress: 0,\n    order: 3\n  },\n  \n  // 连击相关\n  {\n    id: 'combo_5',\n    name: '连击新手',\n    description: '达成5连击',\n    icon: '🔥',\n    category: 'combo',\n    rarity: 'common',\n    condition: { type: 'max_combo', target: 5 },\n    rewards: { coins: 30 },\n    unlocked: false,\n    progress: 0,\n    order: 10\n  },\n  {\n    id: 'combo_10',\n    name: '连击高手',\n    description: '达成10连击',\n    icon: '💥',\n    category: 'combo',\n    rarity: 'rare',\n    condition: { type: 'max_combo', target: 10 },\n    rewards: { coins: 100, powerups: [{ type: 'rainbow', quantity: 1 }] },\n    unlocked: false,\n    progress: 0,\n    order: 11\n  },\n  {\n    id: 'combo_20',\n    name: '连击传说',\n    description: '达成20连击',\n    icon: '⚡',\n    category: 'combo',\n    rarity: 'legendary',\n    condition: { type: 'max_combo', target: 20 },\n    rewards: { coins: 500, title: '连击传说', badge: 'combo_master' },\n    unlocked: false,\n    progress: 0,\n    order: 12\n  },\n  \n  // 关卡相关\n  {\n    id: 'level_10',\n    name: '探索者',\n    description: '完成10个关卡',\n    icon: '🗺️',\n    category: 'level',\n    rarity: 'common',\n    condition: { type: 'levels_completed', target: 10 },\n    rewards: { coins: 100 },\n    unlocked: false,\n    progress: 0,\n    order: 20\n  },\n  {\n    id: 'level_50',\n    name: '冒险家',\n    description: '完成50个关卡',\n    icon: '🎒',\n    category: 'level',\n    rarity: 'rare',\n    condition: { type: 'levels_completed', target: 50 },\n    rewards: { coins: 500, powerups: [{ type: 'shuffle', quantity: 5 }] },\n    unlocked: false,\n    progress: 0,\n    order: 21\n  },\n  {\n    id: 'perfect_10',\n    name: '完美主义者',\n    description: '三星通关10个关卡',\n    icon: '✨',\n    category: 'level',\n    rarity: 'epic',\n    condition: { type: 'perfect_levels', target: 10 },\n    rewards: { coins: 800, title: '完美主义者' },\n    unlocked: false,\n    progress: 0,\n    order: 22\n  },\n  \n  // 特殊宝石相关\n  {\n    id: 'special_bomb_10',\n    name: '爆破专家',\n    description: '创造10个炸弹宝石',\n    icon: '💣',\n    category: 'special',\n    rarity: 'common',\n    condition: { type: 'special_gems_created_bomb', target: 10 },\n    rewards: { coins: 80 },\n    unlocked: false,\n    progress: 0,\n    order: 30\n  },\n  {\n    id: 'special_rainbow_5',\n    name: '彩虹收集者',\n    description: '创造5个彩虹宝石',\n    icon: '🌈',\n    category: 'special',\n    rarity: 'rare',\n    condition: { type: 'special_gems_created_rainbow', target: 5 },\n    rewards: { coins: 200, powerups: [{ type: 'rainbow', quantity: 2 }] },\n    unlocked: false,\n    progress: 0,\n    order: 31\n  },\n  \n  // 道具相关\n  {\n    id: 'powerup_user',\n    name: '道具使用者',\n    description: '使用50个道具',\n    icon: '🔧',\n    category: 'powerup',\n    rarity: 'common',\n    condition: { type: 'total_powerups_used', target: 50 },\n    rewards: { coins: 150 },\n    unlocked: false,\n    progress: 0,\n    order: 40\n  },\n  \n  // 时间相关\n  {\n    id: 'speed_runner',\n    name: '速度之星',\n    description: '在30秒内完成一个关卡',\n    icon: '⚡',\n    category: 'time',\n    rarity: 'rare',\n    condition: { type: 'fastest_level', target: 30 },\n    rewards: { coins: 300, title: '速度之星' },\n    unlocked: false,\n    progress: 0,\n    order: 50\n  },\n  \n  // 里程碑\n  {\n    id: 'first_win',\n    name: '首次胜利',\n    description: '完成你的第一个关卡',\n    icon: '🎉',\n    category: 'milestone',\n    rarity: 'common',\n    condition: { type: 'games_won', target: 1 },\n    rewards: { coins: 100, powerups: [{ type: 'hint', quantity: 5 }] },\n    unlocked: false,\n    progress: 0,\n    order: 100\n  },\n  {\n    id: 'dedication',\n    name: '坚持不懈',\n    description: '游戏总时长达到1小时',\n    icon: '⏰',\n    category: 'milestone',\n    rarity: 'rare',\n    condition: { type: 'total_time_played', target: 3600 },\n    rewards: { coins: 500, badge: 'dedicated_player' },\n    unlocked: false,\n    progress: 0,\n    order: 101\n  }\n]\n\n// 成就稀有度配置\nexport const ACHIEVEMENT_RARITY_CONFIG = {\n  common: {\n    color: '#9ca3af',\n    bgColor: 'rgba(156, 163, 175, 0.1)',\n    borderColor: 'rgba(156, 163, 175, 0.3)',\n    name: '普通',\n    glow: '0 0 10px rgba(156, 163, 175, 0.3)'\n  },\n  rare: {\n    color: '#3b82f6',\n    bgColor: 'rgba(59, 130, 246, 0.1)',\n    borderColor: 'rgba(59, 130, 246, 0.3)',\n    name: '稀有',\n    glow: '0 0 15px rgba(59, 130, 246, 0.4)'\n  },\n  epic: {\n    color: '#8b5cf6',\n    bgColor: 'rgba(139, 92, 246, 0.1)',\n    borderColor: 'rgba(139, 92, 246, 0.3)',\n    name: '史诗',\n    glow: '0 0 20px rgba(139, 92, 246, 0.5)'\n  },\n  legendary: {\n    color: '#f59e0b',\n    bgColor: 'rgba(245, 158, 11, 0.1)',\n    borderColor: 'rgba(245, 158, 11, 0.3)',\n    name: '传说',\n    glow: '0 0 25px rgba(245, 158, 11, 0.6)'\n  }\n}\n\n// 成就解锁事件\nexport interface AchievementUnlockedEvent {\n  achievement: Achievement\n  timestamp: Date\n  newRewards: {\n    coins: number\n    powerups: Array<{ type: string; quantity: number }>\n    titles: string[]\n    badges: string[]\n  }\n}\n"], "names": ["ACHIEVEMENTS", "ACHIEVEMENT_RARITY_CONFIG"], "mappings": "AAyFO,MAAMA,EAA8B,CAEzC,CACE,GAAI,WACJ,KAAM,OACN,YAAa,eACb,KAAM,KACN,SAAU,QACV,OAAQ,SACR,UAAW,CAAE,KAAM,oBAAqB,OAAQ,GAAA,EAChD,QAAS,CAAE,MAAO,EAAA,EAClB,SAAU,GACV,SAAU,EACV,MAAO,CAAA,EAET,CACE,GAAI,YACJ,KAAM,OACN,YAAa,gBACb,KAAM,IACN,SAAU,QACV,OAAQ,OACR,UAAW,CAAE,KAAM,oBAAqB,OAAQ,GAAA,EAChD,QAAS,CAAE,MAAO,IAAK,SAAU,CAAC,CAAE,KAAM,OAAQ,SAAU,CAAA,CAAG,CAAA,EAC/D,SAAU,GACV,SAAU,EACV,MAAO,CAAA,EAET,CACE,GAAI,aACJ,KAAM,OACN,YAAa,iBACb,KAAM,KACN,SAAU,QACV,OAAQ,OACR,UAAW,CAAE,KAAM,oBAAqB,OAAQ,GAAA,EAChD,QAAS,CAAE,MAAO,IAAM,MAAO,MAAA,EAC/B,SAAU,GACV,SAAU,EACV,MAAO,CAAA,EAIT,CACE,GAAI,UACJ,KAAM,OACN,YAAa,QACb,KAAM,KACN,SAAU,QACV,OAAQ,SACR,UAAW,CAAE,KAAM,YAAa,OAAQ,CAAA,EACxC,QAAS,CAAE,MAAO,EAAA,EAClB,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAET,CACE,GAAI,WACJ,KAAM,OACN,YAAa,SACb,KAAM,KACN,SAAU,QACV,OAAQ,OACR,UAAW,CAAE,KAAM,YAAa,OAAQ,EAAA,EACxC,QAAS,CAAE,MAAO,IAAK,SAAU,CAAC,CAAE,KAAM,UAAW,SAAU,CAAA,CAAG,CAAA,EAClE,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAET,CACE,GAAI,WACJ,KAAM,OACN,YAAa,SACb,KAAM,IACN,SAAU,QACV,OAAQ,YACR,UAAW,CAAE,KAAM,YAAa,OAAQ,EAAA,EACxC,QAAS,CAAE,MAAO,IAAK,MAAO,OAAQ,MAAO,cAAA,EAC7C,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAIT,CACE,GAAI,WACJ,KAAM,MACN,YAAa,UACb,KAAM,MACN,SAAU,QACV,OAAQ,SACR,UAAW,CAAE,KAAM,mBAAoB,OAAQ,EAAA,EAC/C,QAAS,CAAE,MAAO,GAAA,EAClB,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAET,CACE,GAAI,WACJ,KAAM,MACN,YAAa,UACb,KAAM,KACN,SAAU,QACV,OAAQ,OACR,UAAW,CAAE,KAAM,mBAAoB,OAAQ,EAAA,EAC/C,QAAS,CAAE,MAAO,IAAK,SAAU,CAAC,CAAE,KAAM,UAAW,SAAU,CAAA,CAAG,CAAA,EAClE,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAET,CACE,GAAI,aACJ,KAAM,QACN,YAAa,YACb,KAAM,IACN,SAAU,QACV,OAAQ,OACR,UAAW,CAAE,KAAM,iBAAkB,OAAQ,EAAA,EAC7C,QAAS,CAAE,MAAO,IAAK,MAAO,OAAA,EAC9B,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAIT,CACE,GAAI,kBACJ,KAAM,OACN,YAAa,YACb,KAAM,KACN,SAAU,UACV,OAAQ,SACR,UAAW,CAAE,KAAM,4BAA6B,OAAQ,EAAA,EACxD,QAAS,CAAE,MAAO,EAAA,EAClB,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAET,CACE,GAAI,oBACJ,KAAM,QACN,YAAa,WACb,KAAM,KACN,SAAU,UACV,OAAQ,OACR,UAAW,CAAE,KAAM,+BAAgC,OAAQ,CAAA,EAC3D,QAAS,CAAE,MAAO,IAAK,SAAU,CAAC,CAAE,KAAM,UAAW,SAAU,CAAA,CAAG,CAAA,EAClE,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAIT,CACE,GAAI,eACJ,KAAM,QACN,YAAa,UACb,KAAM,KACN,SAAU,UACV,OAAQ,SACR,UAAW,CAAE,KAAM,sBAAuB,OAAQ,EAAA,EAClD,QAAS,CAAE,MAAO,GAAA,EAClB,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAIT,CACE,GAAI,eACJ,KAAM,OACN,YAAa,cACb,KAAM,IACN,SAAU,OACV,OAAQ,OACR,UAAW,CAAE,KAAM,gBAAiB,OAAQ,EAAA,EAC5C,QAAS,CAAE,MAAO,IAAK,MAAO,MAAA,EAC9B,SAAU,GACV,SAAU,EACV,MAAO,EAAA,EAIT,CACE,GAAI,YACJ,KAAM,OACN,YAAa,YACb,KAAM,KACN,SAAU,YACV,OAAQ,SACR,UAAW,CAAE,KAAM,YAAa,OAAQ,CAAA,EACxC,QAAS,CAAE,MAAO,IAAK,SAAU,CAAC,CAAE,KAAM,OAAQ,SAAU,CAAA,CAAG,CAAA,EAC/D,SAAU,GACV,SAAU,EACV,MAAO,GAAA,EAET,CACE,GAAI,aACJ,KAAM,OACN,YAAa,aACb,KAAM,IACN,SAAU,YACV,OAAQ,OACR,UAAW,CAAE,KAAM,oBAAqB,OAAQ,IAAA,EAChD,QAAS,CAAE,MAAO,IAAK,MAAO,kBAAA,EAC9B,SAAU,GACV,SAAU,EACV,MAAO,GAAA,CAEX,EAGaC,EAA4B,CACvC,OAAQ,CACN,MAAO,UACP,QAAS,2BACT,YAAa,2BACb,KAAM,KACN,KAAM,mCAAA,EAER,KAAM,CACJ,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,KACN,KAAM,kCAAA,EAER,KAAM,CACJ,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,KACN,KAAM,kCAAA,EAER,UAAW,CACT,MAAO,UACP,QAAS,0BACT,YAAa,0BACb,KAAM,KACN,KAAM,kCAAA,CAEV"}