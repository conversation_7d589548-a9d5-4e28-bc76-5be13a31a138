import React from 'react'
import styled from 'styled-components'
import { PowerUpType, PowerUpInstance, POWER_UPS, RARITY_CONFIG } from '../../types/powerups'
import { theme, media } from '../../styles/theme'
import Button from '../UI/Button'
import Card from '../UI/Card'

interface PowerUpPanelProps {
  powerUps: PowerUpInstance[]
  onUsePowerUp: (type: PowerUpType) => void
  selectedPowerUp: PowerUpType | null
  onSelectPowerUp: (type: PowerUpType | null) => void
  disabled?: boolean
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[4]};
  background: ${theme.colors.background.card};
  border-radius: ${theme.borderRadius.xl};
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  ${media.maxMd} {
    padding: ${theme.spacing[3]};
    gap: ${theme.spacing[2]};
  }
`

const Title = styled.h3`
  color: ${theme.colors.text.primary};
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.semibold};
  margin: 0;
  text-align: center;
  
  ${media.maxMd} {
    font-size: ${theme.fontSizes.base};
  }
`

const PowerUpGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: ${theme.spacing[2]};
  
  ${media.maxMd} {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: ${theme.spacing[1]};
  }
`

const PowerUpCard = styled(Card)<{ 
  $selected: boolean
  $disabled: boolean
  $rarity: string
}>`
  padding: ${theme.spacing[3]};
  cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};
  text-align: center;
  transition: all ${theme.transitions.base} ease-in-out;
  position: relative;
  min-height: 80px;
  
  ${props => {
    const rarityConfig = RARITY_CONFIG[props.$rarity as keyof typeof RARITY_CONFIG]
    return `
      border: 2px solid ${props.$selected ? rarityConfig.color : rarityConfig.borderColor};
      background: ${props.$selected ? rarityConfig.bgColor : 'rgba(255, 255, 255, 0.05)'};
    `
  }}
  
  ${props => props.$disabled && `
    opacity: 0.5;
    filter: grayscale(50%);
  `}
  
  ${props => !props.$disabled && `
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${theme.shadows.lg};
      border-color: ${RARITY_CONFIG[props.$rarity as keyof typeof RARITY_CONFIG].color};
    }
  `}
  
  ${media.maxMd} {
    padding: ${theme.spacing[2]};
    min-height: 70px;
  }
`

const PowerUpIcon = styled.div`
  font-size: 24px;
  margin-bottom: ${theme.spacing[1]};
  
  ${media.maxMd} {
    font-size: 20px;
  }
`

const PowerUpName = styled.div`
  font-size: ${theme.fontSizes.xs};
  color: ${theme.colors.text.secondary};
  font-weight: ${theme.fontWeights.medium};
  line-height: 1.2;
  
  ${media.maxMd} {
    font-size: 10px;
  }
`

const PowerUpQuantity = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  background: ${theme.colors.secondary[500]};
  color: ${theme.colors.gray[900]};
  border-radius: ${theme.borderRadius.full};
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${theme.fontSizes.xs};
  font-weight: ${theme.fontWeights.bold};
  box-shadow: ${theme.shadows.md};
  
  ${media.maxMd} {
    width: 18px;
    height: 18px;
    font-size: 10px;
  }
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
  margin-top: ${theme.spacing[2]};
  
  ${media.maxMd} {
    flex-direction: column;
    gap: ${theme.spacing[1]};
  }
`

const PowerUpDescription = styled.div`
  background: rgba(0, 0, 0, 0.8);
  color: ${theme.colors.text.primary};
  padding: ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.lg};
  font-size: ${theme.fontSizes.sm};
  line-height: 1.4;
  margin-top: ${theme.spacing[2]};
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  ${media.maxMd} {
    padding: ${theme.spacing[2]};
    font-size: ${theme.fontSizes.xs};
  }
`

const PowerUpPanel: React.FC<PowerUpPanelProps> = ({
  powerUps,
  onUsePowerUp,
  selectedPowerUp,
  onSelectPowerUp,
  disabled = false
}) => {
  // const [showDescription, setShowDescription] = useState<PowerUpType | null>(null)

  const handlePowerUpClick = (type: PowerUpType) => {
    if (disabled) return
    
    if (selectedPowerUp === type) {
      onSelectPowerUp(null)
    } else {
      onSelectPowerUp(type)
      // setShowDescription(type)
    }
  }

  const handleUsePowerUp = () => {
    if (selectedPowerUp && !disabled) {
      onUsePowerUp(selectedPowerUp)
      onSelectPowerUp(null)
      // setShowDescription(null)
    }
  }

  const handleCancel = () => {
    onSelectPowerUp(null)
    // setShowDescription(null)
  }

  const getAvailablePowerUps = () => {
    return powerUps.filter(p => p.quantity > 0)
  }

  const selectedPowerUpInfo = selectedPowerUp ? POWER_UPS[selectedPowerUp] : null

  return (
    <Container>
      <Title>道具</Title>
      
      <PowerUpGrid>
        {getAvailablePowerUps().map((powerUpInstance) => {
          const powerUp = POWER_UPS[powerUpInstance.type]
          const isSelected = selectedPowerUp === powerUpInstance.type
          const isDisabled = disabled || powerUpInstance.quantity === 0
          
          return (
            <PowerUpCard
              key={powerUpInstance.type}
              $selected={isSelected}
              $disabled={isDisabled}
              $rarity={powerUp.rarity}
              onClick={() => handlePowerUpClick(powerUpInstance.type)}
              hoverable={!isDisabled}
            >
              <PowerUpIcon>{powerUp.icon}</PowerUpIcon>
              <PowerUpName>{powerUp.name.split(' ')[1]}</PowerUpName>
              <PowerUpQuantity>{powerUpInstance.quantity}</PowerUpQuantity>
            </PowerUpCard>
          )
        })}
      </PowerUpGrid>

      {selectedPowerUpInfo && (
        <>
          <PowerUpDescription>
            <strong>{selectedPowerUpInfo.name}</strong>
            <br />
            {selectedPowerUpInfo.description}
          </PowerUpDescription>
          
          <ActionButtons>
            <Button
              variant="primary"
              size="sm"
              onClick={handleUsePowerUp}
              disabled={disabled}
              fullWidth
            >
              使用道具
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              fullWidth
            >
              取消
            </Button>
          </ActionButtons>
        </>
      )}
      
      {getAvailablePowerUps().length === 0 && (
        <PowerUpDescription>
          暂无可用道具
        </PowerUpDescription>
      )}
    </Container>
  )
}

export default PowerUpPanel
