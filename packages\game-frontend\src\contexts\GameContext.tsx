import React, { createContext, useContext, useReducer, useState } from 'react'
import { useQuery, useMutation } from 'react-query'
import toast from 'react-hot-toast'
import { GameEngine } from '../utils/gameEngine'

// Mock data for development
const mockLevel = {
  id: 1,
  name: '第一关',
  difficulty: 'easy' as const,
  targetScore: 1000,
  maxMoves: 20,
  boardSize: 6,
  objectives: [
    { type: 'score' as const, target: 1000, description: '达到目标分数' },
    { type: 'collect_items' as const, target: 10, description: '收集星星' }
  ]
}

const generateMockBoard = (size: number = 6): number[][] => {
  const board: number[][] = []
  for (let i = 0; i < size; i++) {
    board[i] = []
    for (let j = 0; j < size; j++) {
      board[i][j] = Math.floor(Math.random() * 6) + 1 // 1-6 different gem types
    }
  }
  return board
}

// 游戏状态类型定义
export interface GameState {
  board: number[][]
  score: number
  moves: number
  maxMoves: number
  movesLeft: number
  level: number
  currentLevel?: number
  targetScore?: number
  status: 'idle' | 'loading' | 'playing' | 'won' | 'lost' | 'paused'
  gameStatus: 'playing' | 'won' | 'lost' | 'paused'
  selectedCell: { row: number; col: number } | null
  combo: number
  timeLeft?: number
  powerUps: {
    hammer: number
    bomb: number
    shuffle: number
    extraMoves: number
  }
}

// 游戏动作类型
type GameAction =
  | { type: 'INIT_GAME'; payload: { level: number; board: number[][]; maxMoves: number; targetScore?: number } }
  | { type: 'SELECT_CELL'; payload: { row: number; col: number } }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'MAKE_MOVE'; payload: { from: { row: number; col: number }; to: { row: number; col: number }; score?: number } }
  | { type: 'UPDATE_BOARD'; payload: { board: number[][]; score: number; combo: number } }
  | { type: 'SET_GAME_STATUS'; payload: 'playing' | 'won' | 'lost' | 'paused' }
  | { type: 'SET_STATUS'; payload: 'idle' | 'loading' | 'playing' | 'won' | 'lost' | 'paused' }
  | { type: 'USE_POWERUP'; payload: { type: keyof GameState['powerUps']; position?: { row: number; col: number } } }
  | { type: 'UPDATE_TIME'; payload: number }
  | { type: 'RESET_GAME' }

// 关卡信息类型
interface LevelInfo {
  id: number
  name: string
  description: string
  boardSize: { rows: number; cols: number }
  maxMoves: number
  targetScore: number
  timeLimit?: number
  objectives: {
    type: 'score' | 'clear_color' | 'collect_items'
    target: number
    description: string
  }[]
  powerUps: string[]
  difficulty: 'easy' | 'medium' | 'hard'
}

// Context 类型定义
interface GameContextType {
  gameState: GameState
  currentLevel: LevelInfo | null
  loading: boolean
  dispatch: React.Dispatch<GameAction>
  startGame: (levelId: number) => Promise<void>
  makeMove: (from: { row: number; col: number }, to: { row: number; col: number }, onMatchFound?: (matches: any[]) => void) => Promise<void>
  usePowerUp: (type: keyof GameState['powerUps'], position?: { row: number; col: number }) => Promise<void>
  pauseGame: () => void
  resumeGame: () => void
  resetGame: () => void
  submitScore: () => Promise<void>
}

// 初始游戏状态
const initialGameState: GameState = {
  board: [],
  score: 0,
  moves: 0,
  maxMoves: 30,
  movesLeft: 30,
  level: 1,
  currentLevel: 1,
  targetScore: 1000,
  status: 'idle',
  gameStatus: 'playing',
  selectedCell: null,
  combo: 0,
  powerUps: {
    hammer: 3,
    bomb: 2,
    shuffle: 1,
    extraMoves: 1,
  },
}

// 游戏状态 reducer
const gameReducer = (state: GameState, action: GameAction): GameState => {
  switch (action.type) {
    case 'INIT_GAME':
      return {
        ...initialGameState,
        board: action.payload.board,
        level: action.payload.level,
        currentLevel: action.payload.level,
        maxMoves: action.payload.maxMoves,
        movesLeft: action.payload.maxMoves,
        targetScore: action.payload.targetScore || 1000,
        status: 'playing',
        gameStatus: 'playing',
      }

    case 'SELECT_CELL':
      return {
        ...state,
        selectedCell: action.payload,
      }

    case 'CLEAR_SELECTION':
      return {
        ...state,
        selectedCell: null,
      }

    case 'MAKE_MOVE':
      return {
        ...state,
        moves: state.moves + 1,
        movesLeft: state.movesLeft - 1,
        selectedCell: null,
      }

    case 'UPDATE_BOARD':
      return {
        ...state,
        board: action.payload.board,
        score: state.score + action.payload.score,
        combo: action.payload.combo,
      }

    case 'SET_GAME_STATUS':
      return {
        ...state,
        gameStatus: action.payload,
        status: action.payload === 'playing' ? 'playing' : action.payload === 'won' ? 'won' : action.payload === 'lost' ? 'lost' : 'paused',
      }

    case 'SET_STATUS':
      return {
        ...state,
        status: action.payload,
        gameStatus: action.payload === 'playing' ? 'playing' : action.payload === 'won' ? 'won' : action.payload === 'lost' ? 'lost' : 'playing',
      }

    case 'USE_POWERUP':
      const powerUpType = action.payload.type
      if (state.powerUps[powerUpType] > 0) {
        return {
          ...state,
          powerUps: {
            ...state.powerUps,
            [powerUpType]: state.powerUps[powerUpType] - 1,
          },
        }
      }
      return state

    case 'UPDATE_TIME':
      return {
        ...state,
        timeLeft: action.payload,
      }

    case 'RESET_GAME':
      return initialGameState

    default:
      return state
  }
}

// API 函数
const gameAPI = {
  // 获取关卡信息
  getLevel: async (levelId: number): Promise<LevelInfo> => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/levels/${levelId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to get level info')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock level data')
      return {
        ...mockLevel,
        id: levelId,
        description: `关卡 ${levelId} - 挑战你的技巧`,
        powerUps: ['hammer', 'bomb', 'shuffle'],
        boardSize: { rows: mockLevel.boardSize, cols: mockLevel.boardSize }
      }
    }
  },

  // 开始游戏
  startGame: async (levelId: number): Promise<{ board: number[][]; maxMoves: number }> => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/game/start', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ levelId }),
      })

      if (!response.ok) {
        throw new Error('Failed to start game')
      }

      return response.json()
    } catch (error) {
      // Mock response when backend is not available
      console.warn('Backend not available, using mock game data')
      return {
        board: generateMockBoard(6),
        maxMoves: 20
      }
    }
  },

  // 执行移动
  makeMove: async (gameId: string, from: { row: number; col: number }, to: { row: number; col: number }) => {
    const token = localStorage.getItem('token')
    const response = await fetch('/api/game/move', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ gameId, from, to }),
    })

    if (!response.ok) {
      throw new Error('Invalid move')
    }

    return response.json()
  },

  // 使用道具
  usePowerUp: async (gameId: string, type: string, position?: { row: number; col: number }) => {
    const token = localStorage.getItem('token')
    const response = await fetch('/api/game/powerup', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ gameId, type, position }),
    })

    if (!response.ok) {
      throw new Error('Failed to use power-up')
    }

    return response.json()
  },

  // 提交分数
  submitScore: async (gameId: string, score: number, moves: number) => {
    const token = localStorage.getItem('token')
    const response = await fetch('/api/game/submit', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ gameId, score, moves }),
    })

    if (!response.ok) {
      throw new Error('Failed to submit score')
    }

    return response.json()
  },
}

// 创建 Context
const GameContext = createContext<GameContextType | undefined>(undefined)

// GameProvider 组件
export const GameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [gameState, dispatch] = useReducer(gameReducer, initialGameState)
  const [currentGameId, setCurrentGameId] = useState<string | null>(null)
  // const queryClient = useQueryClient()

  // 获取当前关卡信息
  const { data: currentLevel, isLoading } = useQuery(
    ['level', gameState.level],
    () => gameAPI.getLevel(gameState.level),
    {
      enabled: gameState.level > 0,
      retry: false,
    }
  )

  // 开始游戏 mutation
  const startGameMutation = useMutation(gameAPI.startGame, {
    onSuccess: (data, levelId) => {
      dispatch({
        type: 'INIT_GAME',
        payload: {
          level: levelId,
          board: data.board,
          maxMoves: data.maxMoves,
        },
      })
      setCurrentGameId('mock-game-id')
      toast.success('游戏开始！')
    },
    onError: (error: Error) => {
      toast.error(error.message || '游戏启动失败')
    },
  })

  // 执行移动 mutation
  // const makeMoveMutation = useMutation(
  //   ({ from, to }: { from: { row: number; col: number }; to: { row: number; col: number } }) =>
  //     gameAPI.makeMove(currentGameId!, from, to),
  //   {
  //     onSuccess: (data) => {
  //       dispatch({ type: 'MAKE_MOVE', payload: data.move })
  //       dispatch({
  //         type: 'UPDATE_BOARD',
  //         payload: {
  //           board: data.board,
  //           score: data.scoreGained,
  //           combo: data.combo,
  //         },
  //       })

  //       if (data.gameStatus) {
  //         dispatch({ type: 'SET_GAME_STATUS', payload: data.gameStatus })
  //       }
  //     },
  //     onError: (error: Error) => {
  //       toast.error(error.message || '无效移动')
  //     },
  //   }
  // )

  // Context 方法实现
  const startGame = async (levelId: number) => {
    await startGameMutation.mutateAsync(levelId)
  }

  const makeMove = async (from: { row: number; col: number }, to: { row: number; col: number }, onMatchFound?: (matches: any[]) => void) => {
    if (!gameState.board) return

    // 使用前端游戏引擎处理移动
    const engine = new GameEngine(gameState.board, 6)
    const result = engine.makeMove(from, to)

    if (result.isValid) {
      // 如果有匹配回调，先调用它
      if (onMatchFound && result.matches && result.matches.length > 0) {
        onMatchFound(result.matches)
      }

      // 更新游戏状态
      dispatch({
        type: 'UPDATE_BOARD',
        payload: {
          board: result.newBoard,
          score: result.score,
          combo: result.cascade ? 1 : 0,
        },
      })

      dispatch({ type: 'MAKE_MOVE', payload: { from, to, score: result.score } })

      // 检查游戏结束条件
      if (gameState.movesLeft <= 1) {
        if (gameState.score >= (gameState.targetScore || 0)) {
          dispatch({ type: 'SET_GAME_STATUS', payload: 'won' })
          toast.success('恭喜过关！')
        } else {
          dispatch({ type: 'SET_GAME_STATUS', payload: 'lost' })
          toast.error('游戏结束')
        }
      }

      if (result.cascade) {
        toast.success('连击！')
      }
    } else {
      toast.error('无效移动')
    }
  }

  const usePowerUp = async (type: keyof GameState['powerUps'], position?: { row: number; col: number }) => {
    if (!currentGameId) return
    
    try {
      const result = await gameAPI.usePowerUp(currentGameId, type, position)
      dispatch({ type: 'USE_POWERUP', payload: { type, position } })
      dispatch({
        type: 'UPDATE_BOARD',
        payload: {
          board: result.board,
          score: result.scoreGained || 0,
          combo: 0,
        },
      })
      toast.success('道具使用成功！')
    } catch (error) {
      toast.error('道具使用失败')
    }
  }

  const pauseGame = () => {
    dispatch({ type: 'SET_GAME_STATUS', payload: 'paused' })
  }

  const resumeGame = () => {
    dispatch({ type: 'SET_GAME_STATUS', payload: 'playing' })
  }

  const resetGame = () => {
    dispatch({ type: 'RESET_GAME' })
    setCurrentGameId(null)
  }

  const submitScore = async () => {
    if (!currentGameId) return
    
    try {
      await gameAPI.submitScore(currentGameId, gameState.score, gameState.moves)
      toast.success('分数提交成功！')
    } catch (error) {
      toast.error('分数提交失败')
    }
  }

  const value: GameContextType = {
    gameState,
    currentLevel: currentLevel || null,
    loading: isLoading,
    dispatch,
    startGame,
    makeMove,
    usePowerUp,
    pauseGame,
    resumeGame,
    resetGame,
    submitScore,
  }

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>
}

// useGame hook
export const useGame = (): GameContextType => {
  const context = useContext(GameContext)
  if (context === undefined) {
    throw new Error('useGame must be used within a GameProvider')
  }
  return context
}
