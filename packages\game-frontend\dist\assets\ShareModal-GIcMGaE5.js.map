{"version": 3, "file": "ShareModal-GIcMGaE5.js", "sources": ["../../src/components/Game/ShareModal.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport styled from 'styled-components'\nimport { ShareData, SHARE_PLATFORMS } from '../../types/leaderboard'\nimport { theme, media } from '../../styles/theme'\nimport { Button } from '../UI/Button'\n\ninterface ShareModalProps {\n  isVisible: boolean\n  shareData: ShareData | null\n  onClose: () => void\n}\n\nconst Overlay = styled.div<{ $visible: boolean }>`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(5px);\n  z-index: ${theme.zIndex.modal};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: ${props => props.$visible ? 1 : 0};\n  visibility: ${props => props.$visible ? 'visible' : 'hidden'};\n  transition: all ${theme.transitions.base} ease-in-out;\n`\n\nconst Modal = styled.div<{ $visible: boolean }>`\n  background: ${theme.colors.background.card};\n  border-radius: ${theme.borderRadius.xl};\n  padding: ${theme.spacing[6]};\n  max-width: 500px;\n  width: 90vw;\n  max-height: 80vh;\n  overflow-y: auto;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  box-shadow: ${theme.shadows.xl};\n  transform: ${props => props.$visible ? 'scale(1)' : 'scale(0.9)'};\n  transition: all ${theme.transitions.base} ease-in-out;\n  \n  ${media.maxMd} {\n    padding: ${theme.spacing[4]};\n  }\n  \n  ${media.maxSm} {\n    padding: ${theme.spacing[3]};\n    width: 95vw;\n  }\n`\n\nconst Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${theme.spacing[4]};\n`\n\nconst Title = styled.h2`\n  color: ${theme.colors.text.primary};\n  font-size: ${theme.fontSizes.xl};\n  font-weight: ${theme.fontWeights.bold};\n  margin: 0;\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.lg};\n  }\n`\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  color: ${theme.colors.text.secondary};\n  font-size: ${theme.fontSizes.xl};\n  cursor: pointer;\n  padding: 0;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: ${theme.borderRadius.full};\n  transition: all ${theme.transitions.fast} ease-in-out;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    color: ${theme.colors.text.primary};\n  }\n`\n\nconst ShareContent = styled.div`\n  text-align: center;\n  margin-bottom: ${theme.spacing[6]};\n`\n\nconst ShareTitle = styled.h3`\n  color: ${theme.colors.text.primary};\n  font-size: ${theme.fontSizes.lg};\n  font-weight: ${theme.fontWeights.semibold};\n  margin: 0 0 ${theme.spacing[2]} 0;\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.base};\n  }\n`\n\nconst ShareDescription = styled.p`\n  color: ${theme.colors.text.secondary};\n  font-size: ${theme.fontSizes.base};\n  margin: 0 0 ${theme.spacing[4]} 0;\n  line-height: 1.5;\n  \n  ${media.maxMd} {\n    font-size: ${theme.fontSizes.sm};\n  }\n`\n\nconst ShareImage = styled.div`\n  width: 200px;\n  height: 200px;\n  margin: 0 auto ${theme.spacing[4]} auto;\n  background: linear-gradient(135deg, ${theme.colors.secondary[500]} 0%, ${theme.colors.secondary[600]} 100%);\n  border-radius: ${theme.borderRadius.xl};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 64px;\n  box-shadow: ${theme.shadows.lg};\n  \n  ${media.maxMd} {\n    width: 150px;\n    height: 150px;\n    font-size: 48px;\n  }\n  \n  ${media.maxSm} {\n    width: 120px;\n    height: 120px;\n    font-size: 36px;\n  }\n`\n\nconst ShareStats = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: ${theme.spacing[3]};\n  margin-bottom: ${theme.spacing[4]};\n  padding: ${theme.spacing[4]};\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: ${theme.borderRadius.lg};\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`\n\nconst StatItem = styled.div`\n  text-align: center;\n  \n  .value {\n    color: ${theme.colors.secondary[400]};\n    font-size: ${theme.fontSizes.xl};\n    font-weight: ${theme.fontWeights.bold};\n    margin-bottom: ${theme.spacing[1]};\n    \n    ${media.maxMd} {\n      font-size: ${theme.fontSizes.lg};\n    }\n  }\n  \n  .label {\n    color: ${theme.colors.text.secondary};\n    font-size: ${theme.fontSizes.sm};\n    \n    ${media.maxMd} {\n      font-size: ${theme.fontSizes.xs};\n    }\n  }\n`\n\nconst PlatformGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: ${theme.spacing[3]};\n  margin-bottom: ${theme.spacing[4]};\n  \n  ${media.maxSm} {\n    grid-template-columns: 1fr;\n  }\n`\n\nconst PlatformButton = styled(Button)<{ $color: string }>`\n  background: ${props => props.$color};\n  color: white;\n  justify-content: flex-start;\n  gap: ${theme.spacing[2]};\n  \n  &:hover {\n    background: ${props => props.$color};\n    opacity: 0.9;\n    transform: translateY(-1px);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`\n\nconst ShareUrl = styled.div`\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: ${theme.borderRadius.base};\n  padding: ${theme.spacing[3]};\n  margin-bottom: ${theme.spacing[4]};\n  \n  .label {\n    color: ${theme.colors.text.secondary};\n    font-size: ${theme.fontSizes.sm};\n    margin-bottom: ${theme.spacing[1]};\n  }\n  \n  .url {\n    color: ${theme.colors.text.primary};\n    font-size: ${theme.fontSizes.sm};\n    font-family: monospace;\n    word-break: break-all;\n    background: rgba(0, 0, 0, 0.2);\n    padding: ${theme.spacing[2]};\n    border-radius: ${theme.borderRadius.base};\n    margin-bottom: ${theme.spacing[2]};\n  }\n`\n\nconst ShareModal: React.FC<ShareModalProps> = ({\n  isVisible,\n  shareData,\n  onClose\n}) => {\n  const [copied, setCopied] = useState(false)\n\n  const handleShare = async (platform: string) => {\n    if (!shareData) return\n\n    const shareUrl = shareData.url || window.location.href\n    const shareText = `${shareData.title}\\n${shareData.description}`\n\n    switch (platform) {\n      case 'wechat':\n        // 微信分享需要特殊处理\n        if (navigator.share) {\n          try {\n            await navigator.share({\n              title: shareData.title,\n              text: shareData.description,\n              url: shareUrl\n            })\n          } catch (err) {\n            console.log('分享取消或失败')\n          }\n        } else {\n          copyToClipboard(shareUrl)\n        }\n        break\n        \n      case 'weibo':\n        const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`\n        window.open(weiboUrl, '_blank')\n        break\n        \n      case 'qq':\n        const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareData.title)}&summary=${encodeURIComponent(shareData.description)}`\n        window.open(qqUrl, '_blank')\n        break\n        \n      case 'copy_link':\n        copyToClipboard(shareUrl)\n        break\n    }\n  }\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      setCopied(true)\n      setTimeout(() => setCopied(false), 2000)\n    } catch (err) {\n      console.error('复制失败:', err)\n    }\n  }\n\n  const getShareIcon = (type: string) => {\n    switch (type) {\n      case 'score': return '🏆'\n      case 'achievement': return '🏅'\n      case 'level_completion': return '🎯'\n      case 'combo': return '⚡'\n      default: return '🎮'\n    }\n  }\n\n  const renderShareStats = () => {\n    if (!shareData) return null\n\n    const { data } = shareData\n    const stats = []\n\n    if (data.score) {\n      stats.push({ label: '分数', value: data.score.toLocaleString() })\n    }\n    if (data.level) {\n      stats.push({ label: '关卡', value: data.level })\n    }\n    if (data.combo) {\n      stats.push({ label: '连击', value: `${data.combo}x` })\n    }\n\n    return (\n      <ShareStats>\n        {stats.map((stat, index) => (\n          <StatItem key={index}>\n            <div className=\"value\">{stat.value}</div>\n            <div className=\"label\">{stat.label}</div>\n          </StatItem>\n        ))}\n      </ShareStats>\n    )\n  }\n\n  if (!shareData) return null\n\n  return (\n    <Overlay $visible={isVisible} onClick={onClose}>\n      <Modal $visible={isVisible} onClick={e => e.stopPropagation()}>\n        <Header>\n          <Title>分享成就</Title>\n          <CloseButton onClick={onClose}>×</CloseButton>\n        </Header>\n        \n        <ShareContent>\n          <ShareImage>\n            {getShareIcon(shareData.type)}\n          </ShareImage>\n          \n          <ShareTitle>{shareData.title}</ShareTitle>\n          <ShareDescription>{shareData.description}</ShareDescription>\n          \n          {renderShareStats()}\n        </ShareContent>\n        \n        <PlatformGrid>\n          {Object.entries(SHARE_PLATFORMS).map(([key, platform]) => (\n            <PlatformButton\n              key={key}\n              $color={platform.color}\n              onClick={() => handleShare(key)}\n              disabled={!platform.available}\n            >\n              <span>{platform.icon}</span>\n              <span>{platform.name}</span>\n            </PlatformButton>\n          ))}\n        </PlatformGrid>\n        \n        {shareData.url && (\n          <ShareUrl>\n            <div className=\"label\">分享链接</div>\n            <div className=\"url\">{shareData.url}</div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => copyToClipboard(shareData.url!)}\n              fullWidth\n            >\n              {copied ? '已复制!' : '复制链接'}\n            </Button>\n          </ShareUrl>\n        )}\n      </Modal>\n    </Overlay>\n  )\n}\n\nexport default ShareModal\n"], "names": ["Overlay", "styled", "theme", "props", "Modal", "media", "Header", "Title", "CloseButton", "ShareContent", "ShareTitle", "ShareDescription", "ShareImage", "ShareStats", "StatItem", "PlatformGrid", "PlatformButton", "<PERSON><PERSON>", "ShareUrl", "ShareModal", "isVisible", "shareData", "onClose", "copied", "setCopied", "useState", "handleShare", "platform", "shareUrl", "shareText", "copyToClipboard", "weiboUrl", "qqUrl", "text", "err", "getShareIcon", "type", "renderShareStats", "data", "stats", "jsx", "stat", "index", "jsxs", "e", "SHARE_PLATFORMS", "key"], "mappings": "wPAYA,MAAMA,EAAUC,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAQVC,EAAM,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA,aAIlBC,GAASA,EAAM,SAAW,EAAI,CAAC;AAAA,gBAC5BA,GAASA,EAAM,SAAW,UAAY,QAAQ;AAAA,oBAC1CD,EAAM,YAAY,IAAI;AAAA,EAGpCE,EAAQH,EAAO;AAAA,gBACLC,EAAM,OAAO,WAAW,IAAI;AAAA,mBACzBA,EAAM,aAAa,EAAE;AAAA,aAC3BA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMbA,EAAM,QAAQ,EAAE;AAAA,eACjBC,GAASA,EAAM,SAAW,WAAa,YAAY;AAAA,oBAC9CD,EAAM,YAAY,IAAI;AAAA;AAAA,IAEtCG,EAAM,KAAK;AAAA,eACAH,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG3BG,EAAM,KAAK;AAAA,eACAH,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,EAKzBI,EAASL,EAAO;AAAA;AAAA;AAAA;AAAA,mBAIHC,EAAM,QAAQ,CAAC,CAAC;AAAA,EAG7BK,EAAQN,EAAO;AAAA,WACVC,EAAM,OAAO,KAAK,OAAO;AAAA,eACrBA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA,IAGnCG,EAAM,KAAK;AAAA,iBACEH,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BM,EAAcP,EAAO;AAAA;AAAA;AAAA,WAGhBC,EAAM,OAAO,KAAK,SAAS;AAAA,eACvBA,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQdA,EAAM,aAAa,IAAI;AAAA,oBACtBA,EAAM,YAAY,IAAI;AAAA;AAAA;AAAA;AAAA,aAI7BA,EAAM,OAAO,KAAK,OAAO;AAAA;AAAA,EAIhCO,EAAeR,EAAO;AAAA;AAAA,mBAETC,EAAM,QAAQ,CAAC,CAAC;AAAA,EAG7BQ,EAAaT,EAAO;AAAA,WACfC,EAAM,OAAO,KAAK,OAAO;AAAA,eACrBA,EAAM,UAAU,EAAE;AAAA,iBAChBA,EAAM,YAAY,QAAQ;AAAA,gBAC3BA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE5BG,EAAM,KAAK;AAAA,iBACEH,EAAM,UAAU,IAAI;AAAA;AAAA,EAI/BS,EAAmBV,EAAO;AAAA,WACrBC,EAAM,OAAO,KAAK,SAAS;AAAA,eACvBA,EAAM,UAAU,IAAI;AAAA,gBACnBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,IAG5BG,EAAM,KAAK;AAAA,iBACEH,EAAM,UAAU,EAAE;AAAA;AAAA,EAI7BU,EAAaX,EAAO;AAAA;AAAA;AAAA,mBAGPC,EAAM,QAAQ,CAAC,CAAC;AAAA,wCACKA,EAAM,OAAO,UAAU,GAAG,CAAC,QAAQA,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA,mBACnFA,EAAM,aAAa,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKxBA,EAAM,QAAQ,EAAE;AAAA;AAAA,IAE5BG,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMXA,EAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAOTQ,EAAaZ,EAAO;AAAA;AAAA;AAAA,SAGjBC,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACNA,EAAM,QAAQ,CAAC,CAAC;AAAA,aACtBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,mBAEVA,EAAM,aAAa,EAAE;AAAA;AAAA,EAIlCY,EAAWb,EAAO;AAAA;AAAA;AAAA;AAAA,aAIXC,EAAM,OAAO,UAAU,GAAG,CAAC;AAAA,iBACvBA,EAAM,UAAU,EAAE;AAAA,mBAChBA,EAAM,YAAY,IAAI;AAAA,qBACpBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,MAE/BG,EAAM,KAAK;AAAA,mBACEH,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,aAKxBA,EAAM,OAAO,KAAK,SAAS;AAAA,iBACvBA,EAAM,UAAU,EAAE;AAAA;AAAA,MAE7BG,EAAM,KAAK;AAAA,mBACEH,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA,EAK/Ba,EAAed,EAAO;AAAA;AAAA;AAAA,SAGnBC,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACNA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAE/BG,EAAM,KAAK;AAAA;AAAA;AAAA,EAKTW,EAAiBf,EAAOgB,CAAM;AAAA,gBACpBd,GAASA,EAAM,MAAM;AAAA;AAAA;AAAA,SAG5BD,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,kBAGPC,GAASA,EAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUjCe,EAAWjB,EAAO;AAAA;AAAA;AAAA,mBAGLC,EAAM,aAAa,IAAI;AAAA,aAC7BA,EAAM,QAAQ,CAAC,CAAC;AAAA,mBACVA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA,aAGtBA,EAAM,OAAO,KAAK,SAAS;AAAA,iBACvBA,EAAM,UAAU,EAAE;AAAA,qBACdA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIxBA,EAAM,OAAO,KAAK,OAAO;AAAA,iBACrBA,EAAM,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA,eAIpBA,EAAM,QAAQ,CAAC,CAAC;AAAA,qBACVA,EAAM,aAAa,IAAI;AAAA,qBACvBA,EAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAI/BiB,EAAwC,CAAC,CAC7C,UAAAC,EACA,UAAAC,EACA,QAAAC,CACF,IAAM,CACJ,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,EAAK,EAEpCC,EAAc,MAAOC,GAAqB,CAC9C,GAAI,CAACN,EAAW,OAEhB,MAAMO,EAAWP,EAAU,KAAO,OAAO,SAAS,KAC5CQ,EAAY,GAAGR,EAAU,KAAK;AAAA,EAAKA,EAAU,WAAW,GAE9D,OAAQM,EAAA,CACN,IAAK,SAEH,GAAI,UAAU,MACZ,GAAI,CACF,MAAM,UAAU,MAAM,CACpB,MAAON,EAAU,MACjB,KAAMA,EAAU,YAChB,IAAKO,CAAA,CACN,CACH,MAAc,CACZ,QAAQ,IAAI,SAAS,CACvB,MAEAE,EAAgBF,CAAQ,EAE1B,MAEF,IAAK,QACH,MAAMG,EAAW,iDAAiD,mBAAmBH,CAAQ,CAAC,UAAU,mBAAmBC,CAAS,CAAC,GACrI,OAAO,KAAKE,EAAU,QAAQ,EAC9B,MAEF,IAAK,KACH,MAAMC,EAAQ,wDAAwD,mBAAmBJ,CAAQ,CAAC,UAAU,mBAAmBP,EAAU,KAAK,CAAC,YAAY,mBAAmBA,EAAU,WAAW,CAAC,GACpM,OAAO,KAAKW,EAAO,QAAQ,EAC3B,MAEF,IAAK,YACHF,EAAgBF,CAAQ,EACxB,KAAA,CAEN,EAEME,EAAkB,MAAOG,GAAiB,CAC9C,GAAI,CACF,MAAM,UAAU,UAAU,UAAUA,CAAI,EACxCT,EAAU,EAAI,EACd,WAAW,IAAMA,EAAU,EAAK,EAAG,GAAI,CACzC,OAASU,EAAK,CACZ,QAAQ,MAAM,QAASA,CAAG,CAC5B,CACF,EAEMC,EAAgBC,GAAiB,CACrC,OAAQA,EAAA,CACN,IAAK,QAAS,MAAO,KACrB,IAAK,cAAe,MAAO,KAC3B,IAAK,mBAAoB,MAAO,KAChC,IAAK,QAAS,MAAO,IACrB,QAAS,MAAO,IAAA,CAEpB,EAEMC,EAAmB,IAAM,CAC7B,GAAI,CAAChB,EAAW,OAAO,KAEvB,KAAM,CAAE,KAAAiB,GAASjB,EACXkB,EAAQ,CAAA,EAEd,OAAID,EAAK,OACPC,EAAM,KAAK,CAAE,MAAO,KAAM,MAAOD,EAAK,MAAM,eAAA,EAAkB,EAE5DA,EAAK,OACPC,EAAM,KAAK,CAAE,MAAO,KAAM,MAAOD,EAAK,MAAO,EAE3CA,EAAK,OACPC,EAAM,KAAK,CAAE,MAAO,KAAM,MAAO,GAAGD,EAAK,KAAK,IAAK,EAInDE,MAAC3B,GACE,SAAA0B,EAAM,IAAI,CAACE,EAAMC,WACf5B,EAAA,CACC,SAAA,CAAA0B,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAS,SAAAC,EAAK,MAAM,EACnCD,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAS,WAAK,KAAA,CAAM,CAAA,GAFtBE,CAGf,CACD,EACH,CAEJ,EAEA,OAAKrB,EAGHmB,EAAAA,IAACxC,EAAA,CAAQ,SAAUoB,EAAW,QAASE,EACrC,SAAAqB,EAAAA,KAACvC,EAAA,CAAM,SAAUgB,EAAW,QAASwB,GAAKA,EAAE,kBAC1C,SAAA,CAAAD,OAACrC,EAAA,CACC,SAAA,CAAAkC,EAAAA,IAACjC,GAAM,SAAA,MAAA,CAAI,EACXiC,EAAAA,IAAChC,EAAA,CAAY,QAASc,EAAS,SAAA,GAAA,CAAC,CAAA,EAClC,SAECb,EAAA,CACC,SAAA,CAAA+B,EAAAA,IAAC5B,EAAA,CACE,SAAAuB,EAAad,EAAU,IAAI,EAC9B,EAEAmB,EAAAA,IAAC9B,EAAA,CAAY,SAAAW,EAAU,KAAA,CAAM,EAC7BmB,EAAAA,IAAC7B,EAAA,CAAkB,SAAAU,EAAU,WAAA,CAAY,EAExCgB,EAAA,CAAiB,EACpB,EAEAG,EAAAA,IAACzB,EAAA,CACE,SAAA,OAAO,QAAQ8B,CAAe,EAAE,IAAI,CAAC,CAACC,EAAKnB,CAAQ,IAClDgB,EAAAA,KAAC3B,EAAA,CAEC,OAAQW,EAAS,MACjB,QAAS,IAAMD,EAAYoB,CAAG,EAC9B,SAAU,CAACnB,EAAS,UAEpB,SAAA,CAAAa,EAAAA,IAAC,OAAA,CAAM,WAAS,IAAA,CAAK,EACrBA,EAAAA,IAAC,OAAA,CAAM,SAAAb,EAAS,IAAA,CAAK,CAAA,CAAA,EANhBmB,CAAA,CAQR,EACH,EAECzB,EAAU,KACTsB,EAAAA,KAACzB,EAAA,CACC,SAAA,CAAAsB,EAAAA,IAAC,MAAA,CAAI,UAAU,QAAQ,SAAA,OAAI,EAC3BA,EAAAA,IAAC,MAAA,CAAI,UAAU,MAAO,WAAU,IAAI,EACpCA,EAAAA,IAACvB,EAAA,CACC,QAAQ,QACR,KAAK,KACL,QAAS,IAAMa,EAAgBT,EAAU,GAAI,EAC7C,UAAS,GAER,WAAS,OAAS,MAAA,CAAA,CACrB,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CACF,EAlDqB,IAoDzB"}