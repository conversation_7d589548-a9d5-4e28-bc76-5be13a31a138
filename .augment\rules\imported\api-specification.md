---
type: "agent_requested"
description: "Example description"
---
# API 接口规范

## 基础信息
- 基础URL: `http://localhost:3001/api`
- 认证方式: JWT Token (Header: `Authorization: Bearer <token>`)
- 响应格式: JSON

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

## 1. 用户认证 API

### 1.1 用户注册
```
POST /auth/register
Content-Type: application/json

{
  "username": "player123",
  "email": "<EMAIL>",
  "password": "password123",
  "displayName": "玩家123"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "player123",
      "displayName": "玩家123",
      "coins": 0,
      "gems": 0,
      "currentLevel": 1
    },
    "token": "jwt_token_here"
  }
}
```

### 1.2 用户登录
```
POST /auth/login
Content-Type: application/json

{
  "username": "player123",
  "password": "password123"
}
```

### 1.3 刷新Token
```
POST /auth/refresh
Authorization: Bearer <refresh_token>
```

## 2. 用户信息 API

### 2.1 获取用户信息
```
GET /user/profile
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "id": 1,
    "username": "player123",
    "displayName": "玩家123",
    "coins": 1500,
    "gems": 10,
    "currentLevel": 15,
    "totalScore": 45000,
    "avatarUrl": null
  }
}
```

### 2.2 更新用户信息
```
PUT /user/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "displayName": "新昵称",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

## 3. 关卡系统 API

### 3.1 获取关卡列表
```
GET /levels?page=1&limit=20
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "levels": [
      {
        "id": 1,
        "levelNumber": 1,
        "boardSize": 6,
        "maxMoves": 20,
        "targetScore": 1000,
        "targetType": "score",
        "difficultyLevel": 1,
        "userProgress": {
          "bestScore": 1200,
          "stars": 2,
          "isCompleted": true
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100
    }
  }
}
```

### 3.2 获取单个关卡详情
```
GET /levels/:levelNumber
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "level": {
      "id": 1,
      "levelNumber": 1,
      "boardSize": 6,
      "maxMoves": 20,
      "targetScore": 1000,
      "targetType": "score",
      "targetData": {},
      "obstacles": [],
      "specialGems": [],
      "difficultyLevel": 1,
      "rewardCoins": 10,
      "rewardItems": []
    },
    "userProgress": {
      "bestScore": 0,
      "stars": 0,
      "isCompleted": false,
      "playCount": 0
    }
  }
}
```

## 4. 游戏会话 API

### 4.1 开始游戏
```
POST /game/start
Authorization: Bearer <token>
Content-Type: application/json

{
  "levelNumber": 1
}

Response:
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "board": [
      [1, 2, 3, 4, 5, 1],
      [2, 3, 4, 5, 1, 2],
      // ... 6x6 board data
    ],
    "level": {
      "maxMoves": 20,
      "targetScore": 1000,
      "targetType": "score"
    }
  }
}
```

### 4.2 执行移动
```
POST /game/move
Authorization: Bearer <token>
Content-Type: application/json

{
  "sessionId": "session_uuid",
  "from": {"x": 0, "y": 0},
  "to": {"x": 0, "y": 1}
}

Response:
{
  "success": true,
  "data": {
    "board": [...], // 更新后的棋盘
    "score": 150,
    "movesLeft": 19,
    "matches": [
      {
        "gems": [{"x": 0, "y": 0}, {"x": 1, "y": 0}, {"x": 2, "y": 0}],
        "type": "horizontal",
        "score": 30
      }
    ],
    "specialGems": [], // 生成的特殊宝石
    "isGameOver": false,
    "isWin": false
  }
}
```

### 4.3 使用道具
```
POST /game/use-item
Authorization: Bearer <token>
Content-Type: application/json

{
  "sessionId": "session_uuid",
  "itemCode": "hammer",
  "position": {"x": 2, "y": 3}
}
```

### 4.4 结束游戏
```
POST /game/finish
Authorization: Bearer <token>
Content-Type: application/json

{
  "sessionId": "session_uuid",
  "finalScore": 1200,
  "movesUsed": 18,
  "isWin": true
}

Response:
{
  "success": true,
  "data": {
    "stars": 2,
    "rewards": {
      "coins": 15,
      "gems": 1,
      "items": []
    },
    "newRecord": true,
    "levelUp": false
  }
}
```

## 5. 道具系统 API

### 5.1 获取用户背包
```
GET /inventory
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "items": [
      {
        "itemCode": "hammer",
        "name": "魔法锤",
        "quantity": 3,
        "type": "tool",
        "rarity": "common"
      }
    ],
    "capacity": 20,
    "used": 5
  }
}
```

### 5.2 购买道具
```
POST /shop/buy
Authorization: Bearer <token>
Content-Type: application/json

{
  "itemCode": "hammer",
  "quantity": 1,
  "paymentType": "coins" // or "gems"
}
```

## 6. 任务系统 API

### 6.1 获取任务列表
```
GET /quests
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "daily": [
      {
        "questCode": "daily_complete_3",
        "name": "完成3个关卡",
        "description": "今日完成任意3个关卡",
        "targetValue": 3,
        "currentProgress": 1,
        "rewards": {
          "coins": 100,
          "gems": 0,
          "items": []
        },
        "expiresAt": "2024-01-02T00:00:00Z"
      }
    ],
    "achievements": []
  }
}
```

### 6.2 领取任务奖励
```
POST /quests/:questCode/claim
Authorization: Bearer <token>
```

## 7. 管理后台 API

### 7.1 关卡管理
```
GET /admin/levels
POST /admin/levels
PUT /admin/levels/:id
DELETE /admin/levels/:id
```

### 7.2 道具管理
```
GET /admin/items
POST /admin/items
PUT /admin/items/:id
DELETE /admin/items/:id
```

### 7.3 用户管理
```
GET /admin/users
GET /admin/users/:id
PUT /admin/users/:id/status
```

### 7.4 数据统计
```
GET /admin/stats/overview
GET /admin/stats/levels
GET /admin/stats/users
```

## 错误代码

| 代码 | 描述 |
|------|------|
| AUTH_001 | 无效的认证令牌 |
| AUTH_002 | 令牌已过期 |
| USER_001 | 用户名已存在 |
| USER_002 | 用户不存在 |
| GAME_001 | 无效的游戏会话 |
| GAME_002 | 游戏已结束 |
| ITEM_001 | 道具不足 |
| ITEM_002 | 道具不存在 |
| LEVEL_001 | 关卡不存在 |
| LEVEL_002 | 关卡未解锁 |
