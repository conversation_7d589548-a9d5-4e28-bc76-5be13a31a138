import { Router, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { Database } from '../database/database';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取用户信息
router.get('/profile', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  const user = await db.get(
    `SELECT id, username, email, display_name, avatar_url, coins, gems, 
            current_level, total_score, created_at, last_login 
     FROM users WHERE id = ?`,
    [userId]
  );

  if (!user) {
    throw createError(404, 'USER_002', '用户不存在');
  }

  res.json({
    success: true,
    data: {
      id: user.id,
      username: user.username,
      email: user.email,
      displayName: user.display_name,
      avatarUrl: user.avatar_url,
      coins: user.coins,
      gems: user.gems,
      currentLevel: user.current_level,
      totalScore: user.total_score,
      createdAt: user.created_at,
      lastLogin: user.last_login
    }
  });
}));

// 更新用户信息
const updateProfileSchema = Joi.object({
  displayName: Joi.string().max(50).optional(),
  avatarUrl: Joi.string().uri().optional(),
  email: Joi.string().email().optional()
});

router.put('/profile', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = updateProfileSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const userId = req.user!.id;
  const { displayName, avatarUrl, email } = value;

  // 检查邮箱是否已被其他用户使用
  if (email) {
    const existingEmail = await db.get(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [email, userId]
    );

    if (existingEmail) {
      throw createError(409, 'USER_003', '邮箱已被使用');
    }
  }

  // 构建更新语句
  const updates: string[] = [];
  const params: any[] = [];

  if (displayName !== undefined) {
    updates.push('display_name = ?');
    params.push(displayName);
  }
  if (avatarUrl !== undefined) {
    updates.push('avatar_url = ?');
    params.push(avatarUrl);
  }
  if (email !== undefined) {
    updates.push('email = ?');
    params.push(email);
  }

  if (updates.length === 0) {
    throw createError(400, 'VALIDATION_ERROR', '没有提供要更新的字段');
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  params.push(userId);

  await db.run(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    params
  );

  // 返回更新后的用户信息
  const updatedUser = await db.get(
    `SELECT id, username, email, display_name, avatar_url, coins, gems, 
            current_level, total_score 
     FROM users WHERE id = ?`,
    [userId]
  );

  res.json({
    success: true,
    data: {
      id: updatedUser.id,
      username: updatedUser.username,
      email: updatedUser.email,
      displayName: updatedUser.display_name,
      avatarUrl: updatedUser.avatar_url,
      coins: updatedUser.coins,
      gems: updatedUser.gems,
      currentLevel: updatedUser.current_level,
      totalScore: updatedUser.total_score
    },
    message: '用户信息更新成功'
  });
}));

// 获取用户统计信息
router.get('/stats', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  // 获取基础统计
  const stats = await db.get(
    `SELECT 
       COUNT(*) as totalLevelsPlayed,
       COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completedLevels,
       MAX(level_number) as highestLevel,
       SUM(best_score) as totalScore,
       AVG(stars) as averageStars
     FROM user_progress WHERE user_id = ?`,
    [userId]
  );

  // 获取最近游戏记录
  const recentGames = await db.all(
    `SELECT level_number, best_score, stars, last_played_at 
     FROM user_progress 
     WHERE user_id = ? 
     ORDER BY last_played_at DESC 
     LIMIT 10`,
    [userId]
  );

  res.json({
    success: true,
    data: {
      totalLevelsPlayed: stats.totalLevelsPlayed || 0,
      completedLevels: stats.completedLevels || 0,
      highestLevel: stats.highestLevel || 0,
      totalScore: stats.totalScore || 0,
      averageStars: parseFloat((stats.averageStars || 0).toFixed(2)),
      recentGames: recentGames.map(game => ({
        levelNumber: game.level_number,
        bestScore: game.best_score,
        stars: game.stars,
        lastPlayedAt: game.last_played_at
      }))
    }
  });
}));

export { router as userRoutes };
