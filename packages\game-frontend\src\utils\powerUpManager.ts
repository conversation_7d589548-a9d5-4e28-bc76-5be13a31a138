import { 
  PowerUpType, 
  PowerUpResult, 
  PowerUpUsageParams, 
  PowerUpEffect,
  POWER_UPS 
} from '../types/powerups'

export class PowerUpManager {
  private activeEffects: Map<PowerUpType, PowerUpEffect> = new Map()
  public onEffectChange?: (effects: PowerUpEffect[]) => void

  constructor(onEffectChange?: (effects: PowerUpEffect[]) => void) {
    this.onEffectChange = onEffectChange
  }

  // 使用道具
  public usePowerUp(params: PowerUpUsageParams): PowerUpResult {
    const { type, position, board } = params

    switch (type) {
      case 'bomb':
        return this.useBomb(position!, board!)
      
      case 'rainbow':
        return this.useRainbow(position!, board!)
      
      case 'hammer':
        return this.useHammer(position!, board!)
      
      case 'shuffle':
        return this.useShuffle(board!)
      
      case 'time_extend':
        return this.useTimeExtend()
      
      case 'score_boost':
        return this.useScoreBoost()
      
      case 'hint':
        return this.useHint(board!)
      
      case 'freeze':
        return this.useFreeze()
      
      default:
        return {
          success: false,
          message: '未知的道具类型'
        }
    }
  }

  // 炸弹道具 - 消除3x3区域
  private useBomb(position: { row: number; col: number }, board: number[][]): PowerUpResult {
    const { row, col } = position
    const gemsRemoved: Array<{ row: number; col: number }> = []
    let scoreGained = 0

    // 计算3x3区域
    for (let r = Math.max(0, row - 1); r <= Math.min(board.length - 1, row + 1); r++) {
      for (let c = Math.max(0, col - 1); c <= Math.min(board[0].length - 1, col + 1); c++) {
        if (board[r][c] !== 0) {
          gemsRemoved.push({ row: r, col: c })
          scoreGained += 10 // 每个宝石10分
        }
      }
    }

    return {
      success: true,
      message: `炸弹消除了 ${gemsRemoved.length} 个宝石！`,
      effects: {
        gemsRemoved,
        scoreGained
      }
    }
  }

  // 彩虹宝石 - 消除同色宝石
  private useRainbow(position: { row: number; col: number }, board: number[][]): PowerUpResult {
    const { row, col } = position
    const targetColor = board[row][col]
    
    if (targetColor === 0) {
      return {
        success: false,
        message: '请选择一个有效的宝石'
      }
    }

    const gemsRemoved: Array<{ row: number; col: number }> = []
    let scoreGained = 0

    // 找到所有同色宝石
    for (let r = 0; r < board.length; r++) {
      for (let c = 0; c < board[0].length; c++) {
        if (board[r][c] === targetColor) {
          gemsRemoved.push({ row: r, col: c })
          scoreGained += 15 // 每个宝石15分
        }
      }
    }

    return {
      success: true,
      message: `彩虹宝石消除了 ${gemsRemoved.length} 个同色宝石！`,
      effects: {
        gemsRemoved,
        scoreGained
      }
    }
  }

  // 锤子道具 - 消除单个宝石
  private useHammer(position: { row: number; col: number }, board: number[][]): PowerUpResult {
    const { row, col } = position
    
    if (board[row][col] === 0) {
      return {
        success: false,
        message: '请选择一个有效的宝石'
      }
    }

    return {
      success: true,
      message: '锤子消除了选中的宝石！',
      effects: {
        gemsRemoved: [{ row, col }],
        scoreGained: 5
      }
    }
  }

  // 洗牌道具
  private useShuffle(_board: number[][]): PowerUpResult {
    return {
      success: true,
      message: '棋盘已重新洗牌！',
      effects: {
        boardShuffled: true
      }
    }
  }

  // 时间延长道具
  private useTimeExtend(): PowerUpResult {
    return {
      success: true,
      message: '时间延长了30秒！',
      effects: {
        timeAdded: 30
      }
    }
  }

  // 分数加成道具
  private useScoreBoost(): PowerUpResult {
    const effect: PowerUpEffect = {
      type: 'score_boost',
      startTime: Date.now(),
      duration: 60000, // 60秒
      multiplier: 2,
      active: true
    }

    this.activeEffects.set('score_boost', effect)
    this.notifyEffectChange()

    // 设置定时器移除效果
    setTimeout(() => {
      this.removeEffect('score_boost')
    }, 60000)

    return {
      success: true,
      message: '分数加成已激活！接下来60秒内分数翻倍！',
      effects: {
        effectApplied: effect
      }
    }
  }

  // 提示道具
  private useHint(_board: number[][]): PowerUpResult {
    // 这里应该调用游戏引擎的提示功能
    return {
      success: true,
      message: '已显示可能的移动提示！'
    }
  }

  // 时间冰冻道具
  private useFreeze(): PowerUpResult {
    const effect: PowerUpEffect = {
      type: 'freeze',
      startTime: Date.now(),
      duration: 15000, // 15秒
      active: true
    }

    this.activeEffects.set('freeze', effect)
    this.notifyEffectChange()

    // 设置定时器移除效果
    setTimeout(() => {
      this.removeEffect('freeze')
    }, 15000)

    return {
      success: true,
      message: '时间已冰冻15秒！',
      effects: {
        effectApplied: effect
      }
    }
  }

  // 获取活跃效果
  public getActiveEffects(): PowerUpEffect[] {
    return Array.from(this.activeEffects.values())
  }

  // 检查是否有特定效果
  public hasEffect(type: PowerUpType): boolean {
    return this.activeEffects.has(type)
  }

  // 获取特定效果
  public getEffect(type: PowerUpType): PowerUpEffect | undefined {
    return this.activeEffects.get(type)
  }

  // 移除效果
  private removeEffect(type: PowerUpType): void {
    this.activeEffects.delete(type)
    this.notifyEffectChange()
  }

  // 通知效果变化
  private notifyEffectChange(): void {
    if (this.onEffectChange) {
      this.onEffectChange(this.getActiveEffects())
    }
  }

  // 清除所有效果
  public clearAllEffects(): void {
    this.activeEffects.clear()
    this.notifyEffectChange()
  }

  // 计算分数倍数
  public getScoreMultiplier(): number {
    const scoreBoost = this.getEffect('score_boost')
    return scoreBoost?.active ? (scoreBoost.multiplier || 1) : 1
  }

  // 检查时间是否冰冻
  public isTimeFrozen(): boolean {
    const freeze = this.getEffect('freeze')
    return freeze?.active || false
  }

  // 更新效果状态（每帧调用）
  public update(): void {
    const now = Date.now()
    let hasChanges = false

    for (const [type, effect] of this.activeEffects.entries()) {
      if (effect.duration && now - effect.startTime >= effect.duration) {
        this.activeEffects.delete(type)
        hasChanges = true
      }
    }

    if (hasChanges) {
      this.notifyEffectChange()
    }
  }

  // 获取道具信息
  public getPowerUpInfo(type: PowerUpType) {
    return POWER_UPS[type]
  }
}
