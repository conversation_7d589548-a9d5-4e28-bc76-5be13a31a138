import { Router, Response } from 'express';
import Jo<PERSON> from 'joi';
import { v4 as uuidv4 } from 'uuid';
import { Database } from '../database/database';
import { GameEngine, Position } from '../game/gameEngine';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 游戏会话存储（生产环境应使用Redis）
const gameSessions = new Map<string, {
  userId: number;
  levelNumber: number;
  engine: GameEngine;
  score: number;
  movesLeft: number;
  targetScore: number;
  targetType: string;
  startTime: number;
}>();

// 所有路由都需要认证
router.use(authenticateToken);

// 开始游戏
const startGameSchema = Joi.object({
  levelNumber: Joi.number().integer().min(1).required()
});

router.post('/start', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = startGameSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const userId = req.user!.id;
  const { levelNumber } = value;

  // 获取关卡信息
  const level = await db.get(
    'SELECT * FROM levels WHERE level_number = ? AND is_active = 1',
    [levelNumber]
  );

  if (!level) {
    throw createError(404, 'LEVEL_001', '关卡不存在');
  }

  // 检查关卡是否解锁
  if (levelNumber > 1) {
    const prevProgress = await db.get(
      'SELECT is_completed FROM user_progress WHERE user_id = ? AND level_number = ?',
      [userId, levelNumber - 1]
    );
    
    if (!prevProgress?.is_completed) {
      throw createError(403, 'LEVEL_002', '关卡未解锁');
    }
  }

  // 创建游戏引擎
  const engine = new GameEngine(level.board_size);
  const sessionId = uuidv4();

  // 存储游戏会话
  gameSessions.set(sessionId, {
    userId,
    levelNumber,
    engine,
    score: 0,
    movesLeft: level.max_moves,
    targetScore: level.target_score,
    targetType: level.target_type,
    startTime: Date.now()
  });

  // 保存游戏会话到数据库
  await db.run(
    `INSERT OR REPLACE INTO game_sessions 
     (id, user_id, level_number, session_data, moves_made, current_score, started_at, is_active)
     VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)`,
    [sessionId, userId, levelNumber, JSON.stringify({
      board: engine.getBoard(),
      movesLeft: level.max_moves,
      targetScore: level.target_score,
      targetType: level.target_type
    }), 0, 0]
  );

  res.json({
    success: true,
    data: {
      sessionId,
      board: engine.getBoard(),
      level: {
        levelNumber,
        boardSize: level.board_size,
        maxMoves: level.max_moves,
        targetScore: level.target_score,
        targetType: level.target_type,
        targetData: level.target_data ? JSON.parse(level.target_data) : null
      },
      gameState: {
        score: 0,
        movesLeft: level.max_moves,
        isGameOver: false,
        isWin: false
      }
    },
    message: '游戏开始'
  });
}));

// 执行移动
const moveSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  from: Joi.object({
    x: Joi.number().integer().min(0).required(),
    y: Joi.number().integer().min(0).required()
  }).required(),
  to: Joi.object({
    x: Joi.number().integer().min(0).required(),
    y: Joi.number().integer().min(0).required()
  }).required()
});

router.post('/move', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = moveSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const userId = req.user!.id;
  const { sessionId, from, to } = value;

  // 获取游戏会话
  const session = gameSessions.get(sessionId);
  if (!session || session.userId !== userId) {
    throw createError(404, 'GAME_001', '无效的游戏会话');
  }

  if (session.movesLeft <= 0) {
    throw createError(400, 'GAME_002', '没有剩余步数');
  }

  // 执行移动
  const moveSuccess = session.engine.swapGems(from as Position, to as Position);
  if (!moveSuccess) {
    throw createError(400, 'GAME_003', '无效的移动');
  }

  // 处理连锁反应
  const cascadeResult = session.engine.processCascade();
  session.score += cascadeResult.totalScore;
  session.movesLeft--;

  // 检查游戏状态
  let isGameOver = false;
  let isWin = false;

  if (session.targetType === 'score' && session.score >= session.targetScore) {
    isWin = true;
    isGameOver = true;
  } else if (session.movesLeft <= 0) {
    isGameOver = true;
    isWin = session.score >= session.targetScore;
  }

  // 更新数据库中的会话
  await db.run(
    `UPDATE game_sessions 
     SET session_data = ?, moves_made = ?, current_score = ?, updated_at = CURRENT_TIMESTAMP
     WHERE id = ?`,
    [JSON.stringify({
      board: session.engine.getBoard(),
      movesLeft: session.movesLeft,
      score: session.score
    }), session.levelNumber - session.movesLeft, session.score, sessionId]
  );

  res.json({
    success: true,
    data: {
      board: session.engine.getBoard(),
      score: session.score,
      movesLeft: session.movesLeft,
      matches: cascadeResult.matches,
      scoreGained: cascadeResult.totalScore,
      isGameOver,
      isWin,
      gameState: {
        targetScore: session.targetScore,
        targetType: session.targetType
      }
    }
  });
}));

// 使用道具
const useItemSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  itemCode: Joi.string().required(),
  position: Joi.object({
    x: Joi.number().integer().min(0).required(),
    y: Joi.number().integer().min(0).required()
  }).optional()
});

router.post('/use-item', asyncHandler(async (req: AuthRequest, res: Response) => {
  const { error, value } = useItemSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const userId = req.user!.id;
  const { sessionId, itemCode, position } = value;

  // 获取游戏会话
  const session = gameSessions.get(sessionId);
  if (!session || session.userId !== userId) {
    throw createError(404, 'GAME_001', '无效的游戏会话');
  }

  // 检查用户是否拥有该道具
  const inventory = await db.get(
    'SELECT quantity FROM user_inventory WHERE user_id = ? AND item_code = ?',
    [userId, itemCode]
  );

  if (!inventory || inventory.quantity <= 0) {
    throw createError(400, 'ITEM_001', '道具不足');
  }

  // 获取道具信息
  const item = await db.get(
    'SELECT * FROM items WHERE item_code = ? AND is_active = 1',
    [itemCode]
  );

  if (!item) {
    throw createError(404, 'ITEM_002', '道具不存在');
  }

  const effectData = JSON.parse(item.effect_data);
  let scoreGained = 0;

  // 应用道具效果
  switch (effectData.type) {
    case 'single_destroy':
      if (position) {
        session.engine.getBoard()[position.y][position.x] = 0;
        scoreGained = 50;
      }
      break;
    
    case 'add_moves':
      session.movesLeft += effectData.value || 5;
      break;
    
    case 'color_clear':
      if (position) {
        const targetColor = session.engine.getBoard()[position.y][position.x];
        const board = session.engine.getBoard();
        let cleared = 0;
        
        for (let y = 0; y < board.length; y++) {
          for (let x = 0; x < board[y].length; x++) {
            if (board[y][x] === targetColor) {
              board[y][x] = 0;
              cleared++;
            }
          }
        }
        
        session.engine.setBoard(board);
        scoreGained = cleared * 20;
      }
      break;
  }

  // 扣除道具
  await db.run(
    'UPDATE user_inventory SET quantity = quantity - 1 WHERE user_id = ? AND item_code = ?',
    [userId, itemCode]
  );

  // 应用重力和填充
  session.engine.applyGravity();
  session.engine.fillEmpty();

  // 处理可能的连锁反应
  const cascadeResult = session.engine.processCascade();
  session.score += cascadeResult.totalScore + scoreGained;

  res.json({
    success: true,
    data: {
      board: session.engine.getBoard(),
      score: session.score,
      movesLeft: session.movesLeft,
      scoreGained: cascadeResult.totalScore + scoreGained,
      matches: cascadeResult.matches
    },
    message: '道具使用成功'
  });
}));

export { router as gameRoutes };
