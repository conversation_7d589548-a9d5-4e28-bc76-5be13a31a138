---
type: "agent_requested"
description: "Example description"
---
# 星光对对碰项目开发规则

## 项目概述
星光对对碰是一个基于Web的Match-3消除游戏，包含游戏前端、管理后台和后端API三个主要模块。

## 技术栈要求

### 前端技术栈
- **框架**: React 18 + TypeScript 5.0+
- **构建工具**: Vite (开发环境)
- **状态管理**: Zustand 或 Redux Toolkit
- **UI组件**: Ant Design 或 Material-UI
- **游戏渲染**: HTML5 Canvas + PixiJS
- **动画**: Framer Motion 或 React Spring
- **HTTP客户端**: Axios + React Query
- **测试**: Jest + React Testing Library + Cypress

### 后端技术栈
- **运行时**: Node.js 18+ LTS
- **框架**: Express.js + TypeScript
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **缓存**: Redis
- **认证**: JWT + bcryptjs
- **验证**: <PERSON><PERSON> 或 <PERSON>
- **文档**: Swagger/OpenAPI
- **测试**: Jest + Supertest
- **日志**: Winston

## 代码组织规则

### 1. 目录结构必须遵循
```
packages/
├── shared/                 # 共享代码 (类型、常量、工具)
│   ├── types/             # TypeScript类型定义
│   ├── constants/         # 项目常量
│   ├── utils/             # 通用工具函数
│   └── validators/        # 数据验证规则
├── game-frontend/         # 游戏前端
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── stores/        # 状态管理
│   │   ├── services/      # API服务
│   │   ├── game/          # 游戏引擎
│   │   └── utils/         # 前端工具
│   └── tests/             # 测试文件
├── admin-panel/           # 管理后台
│   └── src/               # 管理后台源码
└── backend-api/           # 后端API
    ├── src/
    │   ├── controllers/   # 控制器
    │   ├── services/      # 业务逻辑
    │   ├── models/        # 数据模型
    │   ├── middleware/    # 中间件
    │   ├── routes/        # 路由定义
    │   ├── database/      # 数据库相关
    │   └── utils/         # 后端工具
    └── tests/             # 测试文件
```

### 2. 文件命名规则
- **React组件**: `PascalCase.tsx` (例: `GameBoard.tsx`)
- **Hooks**: `use + PascalCase.ts` (例: `useGameState.ts`)
- **服务类**: `PascalCase + Service.ts` (例: `UserService.ts`)
- **工具函数**: `camelCase.ts` (例: `gameUtils.ts`)
- **常量文件**: `UPPER_SNAKE_CASE.ts` (例: `GAME_CONSTANTS.ts`)
- **类型定义**: `types.ts` 或 `PascalCase.types.ts`

## TypeScript规则

### 1. 强制要求
- **禁止使用 `any` 类型** - 必须使用具体类型或 `unknown`
- **必须明确函数返回类型** - 所有函数都要声明返回类型
- **接口优于类型别名** - 对象结构使用 `interface`，联合类型使用 `type`
- **严格空值检查** - 启用 `strictNullChecks`

### 2. 类型定义规范
```typescript
// ✅ 正确：明确的接口定义
interface User {
  readonly id: string;
  username: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ 正确：泛型API响应
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: ValidationError[];
}

// ✅ 正确：联合类型
type GameStatus = 'idle' | 'playing' | 'paused' | 'completed' | 'failed';

// ❌ 错误：使用any
// const userData: any = {};

// ❌ 错误：缺少返回类型
// function getUser(id: string) { ... }
```

## React开发规则

### 1. 组件结构规范
```typescript
// 必须按此顺序组织代码
interface ComponentProps {
  // Props定义
}

export const Component: React.FC<ComponentProps> = (props) => {
  // 1. 解构props
  const { prop1, prop2 } = props;
  
  // 2. Hooks (按顺序: useState, useEffect, 自定义hooks)
  const [state, setState] = useState<StateType>(initialValue);
  
  // 3. 事件处理函数
  const handleEvent = useCallback(() => {
    // 处理逻辑
  }, [dependencies]);
  
  // 4. 副作用
  useEffect(() => {
    // 副作用逻辑
  }, [dependencies]);
  
  // 5. 渲染函数 (如果复杂)
  const renderContent = () => {
    // 渲染逻辑
  };
  
  // 6. 返回JSX
  return (
    <div>
      {/* JSX内容 */}
    </div>
  );
};
```

### 2. 性能优化要求
- **必须使用 `React.memo`** - 对于纯展示组件
- **必须使用 `useCallback`** - 对于传递给子组件的函数
- **必须使用 `useMemo`** - 对于复杂计算
- **禁止在render中创建对象** - 避免不必要的重渲染

## 后端开发规则

### 1. 分层架构要求
```typescript
// Controller层 - 只处理HTTP请求/响应
export class UserController {
  constructor(private userService: UserService) {}
  
  async getUser(req: Request, res: Response): Promise<void> {
    const user = await this.userService.findById(req.params.id);
    res.json({ success: true, data: user });
  }
}

// Service层 - 业务逻辑
export class UserService {
  constructor(private userRepository: UserRepository) {}
  
  async findById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }
}

// Repository层 - 数据访问
export class UserRepository {
  async findById(id: string): Promise<User | null> {
    // 数据库查询逻辑
  }
}
```

### 2. 错误处理要求
- **必须使用自定义错误类** - 继承自 `AppError`
- **必须记录错误日志** - 使用结构化日志
- **必须返回标准错误格式** - 统一的错误响应结构

## 数据库规则

### 1. 查询规范
- **必须使用参数化查询** - 防止SQL注入
- **必须添加适当索引** - 优化查询性能
- **必须实现软删除** - 使用 `is_deleted` 字段
- **必须记录时间戳** - `created_at`, `updated_at`

### 2. 事务处理
```typescript
// ✅ 正确：使用事务
async function transferCoins(fromUserId: string, toUserId: string, amount: number): Promise<void> {
  const transaction = await db.beginTransaction();
  try {
    await userRepository.updateCoins(fromUserId, -amount, transaction);
    await userRepository.updateCoins(toUserId, amount, transaction);
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

## 测试规则

### 1. 测试覆盖率要求
- **单元测试覆盖率 ≥ 80%**
- **集成测试覆盖率 ≥ 60%**
- **关键业务逻辑覆盖率 = 100%**

### 2. 测试结构规范
```typescript
describe('UserService', () => {
  let userService: UserService;
  let mockRepository: jest.Mocked<UserRepository>;
  
  beforeEach(() => {
    // 设置测试环境
  });
  
  describe('findById', () => {
    it('should return user when found', async () => {
      // Arrange - 准备测试数据
      // Act - 执行测试
      // Assert - 验证结果
    });
    
    it('should return null when not found', async () => {
      // 测试边界情况
    });
    
    it('should throw error when database fails', async () => {
      // 测试错误情况
    });
  });
});
```

## Git工作流规则

### 1. 分支命名规范
```
main                    # 主分支 (受保护)
develop                 # 开发分支
feature/TASK-123-user-auth    # 功能分支
bugfix/TASK-456-login-error   # 修复分支
hotfix/security-patch         # 热修复分支
release/v1.0.0               # 发布分支
```

### 2. 提交信息规范
```
type(scope): description

feat(auth): 添加JWT认证功能
fix(game): 修复游戏状态同步问题
docs(api): 更新API文档
style(ui): 调整按钮样式
refactor(service): 重构用户服务
test(unit): 添加用户服务单元测试
chore(deps): 更新依赖包版本
```

### 3. 代码审查要求
- **所有代码必须经过审查** - 至少一人审查
- **必须通过所有测试** - CI/CD管道检查
- **必须符合代码规范** - ESLint + Prettier检查
- **必须更新相关文档** - API文档、README等

## 性能要求

### 1. 前端性能指标
- **首屏加载时间 < 2秒**
- **游戏帧率 ≥ 60 FPS**
- **包体积 < 1MB (gzipped)**
- **Lighthouse分数 ≥ 90**

### 2. 后端性能指标
- **API响应时间 < 200ms (P95)**
- **数据库查询时间 < 100ms**
- **并发用户数 ≥ 1000**
- **可用性 ≥ 99.9%**

## 安全要求

### 1. 认证授权
- **必须使用HTTPS** - 生产环境强制
- **必须验证JWT token** - 所有受保护接口
- **必须实现角色权限** - RBAC模型
- **必须记录安全日志** - 登录、权限变更等

### 2. 数据保护
- **必须加密敏感数据** - 密码、支付信息
- **必须验证输入数据** - 防止注入攻击
- **必须实现请求限流** - 防止暴力攻击
- **必须定期备份数据** - 自动化备份策略

## 部署规则

### 1. 环境管理
- **开发环境 (dev)** - 本地开发
- **测试环境 (staging)** - 集成测试
- **生产环境 (prod)** - 正式发布

### 2. 发布流程
1. **功能开发** - feature分支开发
2. **代码审查** - Pull Request审查
3. **自动测试** - CI管道执行
4. **部署测试** - staging环境验证
5. **生产发布** - 自动化部署
6. **监控告警** - 实时监控系统状态
