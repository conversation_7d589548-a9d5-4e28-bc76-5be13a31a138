import{a as u,j as r,m as s,t as e}from"./index-CNCEp3EQ.js";import{d as i}from"./ui-ldAE8JkK.js";import{c as $}from"./router-DMCr7QLp.js";import"./vendor-Dneogk0_.js";const g=i.div`
  flex: 1;
  padding: 2rem;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;

  ${s.maxMd} {
    padding: 1rem;
  }

  ${s.maxSm} {
    padding: 0.5rem;
  }
`,p=i.h1`
  color: ${e.colors.secondary[400]};
  font-size: ${e.fontSizes["4xl"]};
  margin-bottom: 3rem;
  text-align: center;
  font-weight: ${e.fontWeights.bold};
  font-family: ${e.fonts.primary};
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.6);
  background: linear-gradient(135deg, ${e.colors.secondary[300]} 0%, ${e.colors.secondary[500]} 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  ${s.maxMd} {
    font-size: ${e.fontSizes["3xl"]};
    margin-bottom: 2rem;
  }

  ${s.maxSm} {
    font-size: ${e.fontSizes["2xl"]};
    margin-bottom: 1.5rem;
  }
`,k=i.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto 3rem;

  ${s.maxMd} {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  ${s.maxSm} {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
`,v=i.div`
  aspect-ratio: 1;
  background: ${o=>o.isCompleted?`linear-gradient(135deg, ${e.colors.success} 0%, #059669 100%)`:o.isLocked?`linear-gradient(135deg, ${e.colors.gray[700]} 0%, ${e.colors.gray[800]} 100%)`:`linear-gradient(135deg, ${e.colors.primary[500]} 0%, ${e.colors.primary[600]} 100%)`};
  border-radius: ${e.borderRadius.xl};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: ${o=>o.isLocked?"not-allowed":"pointer"};
  transition: all ${e.transitions.base} ease-in-out;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: ${e.shadows.lg};

  &:hover {
    ${o=>!o.isLocked&&`
      transform: translateY(-6px);
      box-shadow: ${e.shadows.xl}, ${o.isCompleted?"0 0 30px rgba(16, 185, 129, 0.4)":"0 0 30px rgba(99, 102, 241, 0.4)"};
      border-color: ${e.colors.secondary[400]};
    `}
  }

  &:active {
    ${o=>!o.isLocked&&`
      transform: translateY(-2px);
    `}
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.8;
  }

  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border-radius: ${e.borderRadius.lg};
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
    pointer-events: none;
  }
`,y=i.div`
  font-size: ${e.fontSizes.xl};
  font-weight: ${e.fontWeights.bold};
  color: ${e.colors.text.primary};
  margin-bottom: ${e.spacing[2]};
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  ${s.maxSm} {
    font-size: ${e.fontSizes.lg};
  }
`,w=i.div`
  display: flex;
  gap: ${e.spacing[1]};
  margin-bottom: ${e.spacing[1]};
`,j=i.div`
  width: 14px;
  height: 14px;
  color: ${o=>o.filled?e.colors.secondary[400]:"rgba(255, 255, 255, 0.3)"};
  font-size: 14px;
  filter: ${o=>o.filled?"drop-shadow(0 0 4px rgba(251, 191, 36, 0.6))":"none"};
  transition: all ${e.transitions.fast} ease-in-out;

  ${s.maxSm} {
    width: 12px;
    height: 12px;
    font-size: 12px;
  }
`,L=i.div`
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
`,x=i.div`
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-top: 0.25rem;
`,f=Array.from({length:50},(o,a)=>({id:a+1,levelNumber:a+1,targetScore:1e3+a*500,maxMoves:20-Math.floor(a/10),difficulty:Math.floor(a/10)+1,isLocked:a>4,userProgress:a<3?{isCompleted:!0,stars:Math.floor(Math.random()*3)+1,bestScore:1e3+a*600}:null})),N=()=>{const o=$(),{data:a=f,isLoading:h}=u("levels",async()=>{try{const t=localStorage.getItem("token"),n=await fetch("/api/levels",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)throw new Error("Failed to fetch levels");return n.json()}catch{return console.warn("Backend not available, using mock data"),f}},{staleTime:5*60*1e3}),b=t=>{t.isLocked||o(`/game?level=${t.levelNumber}`)};return h?r.jsxs(g,{children:[r.jsx(p,{children:"关卡选择"}),r.jsx("div",{style:{textAlign:"center",color:"white",marginTop:"2rem"},children:"正在加载关卡..."})]}):r.jsxs(g,{children:[r.jsx(p,{children:"关卡选择"}),r.jsx(k,{children:a.map(t=>{var n,l,d;return r.jsx(v,{isLocked:t.isLocked,isCompleted:!!((n=t.userProgress)!=null&&n.isCompleted),onClick:()=>b(t),children:t.isLocked?r.jsxs(r.Fragment,{children:[r.jsx(L,{children:"🔒"}),r.jsx(x,{children:"已锁定"})]}):r.jsxs(r.Fragment,{children:[r.jsx(y,{children:t.levelNumber}),((l=t.userProgress)==null?void 0:l.isCompleted)&&r.jsx(w,{children:[1,2,3].map(c=>{var m;return r.jsx(j,{filled:c<=(((m=t.userProgress)==null?void 0:m.stars)||0),children:"⭐"},c)})}),r.jsxs(x,{children:["目标: ",(d=t.targetScore)==null?void 0:d.toLocaleString(),r.jsx("br",{}),"步数: ",t.maxMoves]})]})},t.id)})})]})};export{N as default};
//# sourceMappingURL=LevelsPage-D220u2A8.js.map
