import { Router, Response } from 'express';
import { Database } from '../database/database';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取用户背包
router.get('/', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  const items = await db.all(
    `SELECT 
       ui.item_code, ui.quantity, ui.acquired_at,
       i.name, i.description, i.type, i.rarity, i.icon_url, i.max_stack
     FROM user_inventory ui
     JOIN items i ON ui.item_code = i.item_code
     WHERE ui.user_id = ? AND ui.quantity > 0
     ORDER BY i.type, i.rarity, i.name`,
    [userId]
  );

  const maxCapacity = parseInt(process.env.MAX_INVENTORY_SIZE || '50');
  const usedSlots = items.length;

  res.json({
    success: true,
    data: {
      items: items.map(item => ({
        itemCode: item.item_code,
        name: item.name,
        description: item.description,
        type: item.type,
        rarity: item.rarity,
        quantity: item.quantity,
        maxStack: item.max_stack,
        iconUrl: item.icon_url,
        acquiredAt: item.acquired_at
      })),
      capacity: maxCapacity,
      used: usedSlots,
      available: maxCapacity - usedSlots
    }
  });
}));

// 获取商店道具列表
router.get('/shop', asyncHandler(async (req: AuthRequest, res: Response) => {
  const items = await db.all(
    `SELECT 
       item_code, name, description, type, rarity, 
       price_coins, price_gems, icon_url
     FROM items 
     WHERE is_purchasable = 1 AND is_active = 1
     ORDER BY type, rarity, price_coins`,
    []
  );

  res.json({
    success: true,
    data: {
      items: items.map(item => ({
        itemCode: item.item_code,
        name: item.name,
        description: item.description,
        type: item.type,
        rarity: item.rarity,
        priceCoins: item.price_coins,
        priceGems: item.price_gems,
        iconUrl: item.icon_url
      }))
    }
  });
}));

// 购买道具
router.post('/shop/buy', asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;
  const { itemCode, quantity = 1, paymentType = 'coins' } = req.body;

  if (!itemCode || quantity < 1) {
    throw createError(400, 'VALIDATION_ERROR', '无效的购买参数');
  }

  // 获取道具信息
  const item = await db.get(
    'SELECT * FROM items WHERE item_code = ? AND is_purchasable = 1 AND is_active = 1',
    [itemCode]
  );

  if (!item) {
    throw createError(404, 'ITEM_002', '道具不存在或不可购买');
  }

  // 获取用户信息
  const user = await db.get(
    'SELECT coins, gems FROM users WHERE id = ?',
    [userId]
  );

  if (!user) {
    throw createError(404, 'USER_002', '用户不存在');
  }

  // 计算总价
  const totalPrice = paymentType === 'gems' 
    ? item.price_gems * quantity 
    : item.price_coins * quantity;

  // 检查余额
  const currentBalance = paymentType === 'gems' ? user.gems : user.coins;
  if (currentBalance < totalPrice) {
    throw createError(400, 'INSUFFICIENT_BALANCE', '余额不足');
  }

  // 检查背包容量
  const currentInventory = await db.get(
    'SELECT quantity FROM user_inventory WHERE user_id = ? AND item_code = ?',
    [userId, itemCode]
  );

  const newQuantity = (currentInventory?.quantity || 0) + quantity;
  if (newQuantity > item.max_stack) {
    throw createError(400, 'INVENTORY_FULL', '超出道具最大堆叠数量');
  }

  // 开始事务
  await db.run('BEGIN TRANSACTION');

  try {
    // 扣除货币
    if (paymentType === 'gems') {
      await db.run(
        'UPDATE users SET gems = gems - ? WHERE id = ?',
        [totalPrice, userId]
      );
    } else {
      await db.run(
        'UPDATE users SET coins = coins - ? WHERE id = ?',
        [totalPrice, userId]
      );
    }

    // 添加道具到背包
    await db.run(
      `INSERT OR REPLACE INTO user_inventory (user_id, item_code, quantity, updated_at)
       VALUES (?, ?, COALESCE((SELECT quantity FROM user_inventory WHERE user_id = ? AND item_code = ?), 0) + ?, CURRENT_TIMESTAMP)`,
      [userId, itemCode, userId, itemCode, quantity]
    );

    await db.run('COMMIT');

    // 获取更新后的用户信息
    const updatedUser = await db.get(
      'SELECT coins, gems FROM users WHERE id = ?',
      [userId]
    );

    res.json({
      success: true,
      data: {
        itemCode,
        quantity,
        totalPrice,
        paymentType,
        newBalance: {
          coins: updatedUser.coins,
          gems: updatedUser.gems
        }
      },
      message: '购买成功'
    });

  } catch (error) {
    await db.run('ROLLBACK');
    throw error;
  }
}));

export { router as inventoryRoutes };
