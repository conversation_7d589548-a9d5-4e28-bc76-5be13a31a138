import{j as i,t as r,d as b}from"./index-CNCEp3EQ.js";import{l as a,d as e}from"./ui-ldAE8JkK.js";const l=o=>{switch(o){case"default":return a`
        ${b.glassmorphism}
        box-shadow: ${r.shadows.md};
      `;case"elevated":return a`
        background: ${r.colors.background.card};
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: ${r.shadows.xl};
      `;case"outlined":return a`
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: none;
      `;case"glass":return a`
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: ${r.shadows.inner}, ${r.shadows.lg};
      `;default:return a``}},c=o=>{switch(o){case"none":return a`padding: 0;`;case"sm":return a`padding: ${r.spacing[3]};`;case"md":return a`padding: ${r.spacing[4]};`;case"lg":return a`padding: ${r.spacing[6]};`;case"xl":return a`padding: ${r.spacing[8]};`;default:return a`padding: ${r.spacing[4]};`}},p=e.div`
  ${o=>l(o.$variant)}
  ${o=>c(o.$padding)}
  
  border-radius: ${r.borderRadius.xl};
  transition: all ${r.transitions.base} ease-in-out;
  position: relative;
  overflow: hidden;
  
  ${o=>o.$hoverable&&a`
    cursor: pointer;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: ${r.shadows["2xl"]};
      
      ${o.$variant==="default"&&a`
        background: ${r.colors.background.cardHover};
      `}
      
      ${o.$variant==="elevated"&&a`
        box-shadow: ${r.shadows["2xl"]}, ${r.shadows.glowGold};
      `}
      
      ${o.$variant==="outlined"&&a`
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.05);
      `}
      
      ${o.$variant==="glass"&&a`
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      `}
    }
    
    &:active {
      transform: translateY(-2px);
    }
  `}
  
  /* 添加微妙的光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0.5;
  }
  
  /* 添加边缘高光 */
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border-radius: ${r.borderRadius.lg};
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.6;
  }
`,d=({variant:o="default",padding:s="md",hoverable:n=!1,children:t,...g})=>i.jsx(p,{$variant:o,$padding:s,$hoverable:n,...g,children:t});e(d)`
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(251, 191, 36, 0.2);
  box-shadow: ${r.shadows.lg}, 0 0 30px rgba(251, 191, 36, 0.1);
  
  &:hover {
    border-color: rgba(251, 191, 36, 0.4);
    box-shadow: ${r.shadows.xl}, 0 0 40px rgba(251, 191, 36, 0.2);
  }
`;e(d)`
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.1) 0%, 
    rgba(139, 92, 246, 0.1) 100%
  );
  border: 1px solid rgba(99, 102, 241, 0.2);
  
  &:hover {
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: ${r.shadows.xl}, ${r.shadows.glowPurple};
  }
`;e(d)`
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1) 0%, 
    rgba(5, 150, 105, 0.1) 100%
  );
  border: 1px solid rgba(16, 185, 129, 0.2);
  
  &:hover {
    border-color: rgba(16, 185, 129, 0.4);
    box-shadow: ${r.shadows.xl}, 0 0 30px rgba(16, 185, 129, 0.2);
  }
`;export{d as C};
//# sourceMappingURL=Card-B65VmGcU.js.map
