import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import {
  LeaderboardType,
  LeaderboardData,
  Friend,
  LEADERBOARD_CONFIG,
  generateMockLeaderboardData,
  generateMockFriends
} from '../types/leaderboard'
import { theme, media } from '../styles/theme'
import { Card } from '../components/UI/Card'
import { Button } from '../components/UI/Button'

const Container = styled.div`
  min-height: 100vh;
  background: ${theme.colors.background.primary};
  padding: ${theme.spacing[6]} ${theme.spacing[4]};

  ${media.maxMd} {
    padding: ${theme.spacing[4]} ${theme.spacing[3]};
  }

  ${media.maxSm} {
    padding: ${theme.spacing[3]} ${theme.spacing[2]};
  }
`

const Header = styled.div`
  text-align: center;
  margin-bottom: ${theme.spacing[8]};

  ${media.maxMd} {
    margin-bottom: ${theme.spacing[6]};
  }
`

const Title = styled.h1`
  color: ${theme.colors.text.primary};
  font-size: ${theme.fontSizes['4xl']};
  font-weight: ${theme.fontWeights.bold};
  margin-bottom: ${theme.spacing[4]};
  text-shadow: ${theme.shadows.text};

  ${media.maxMd} {
    font-size: ${theme.fontSizes['3xl']};
  }

  ${media.maxSm} {
    font-size: ${theme.fontSizes['2xl']};
  }
`

const TabContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: ${theme.spacing[2]};
  margin-bottom: ${theme.spacing[6]};
  flex-wrap: wrap;

  ${media.maxMd} {
    margin-bottom: ${theme.spacing[4]};
  }
`

const TabButton = styled(Button)<{ $active: boolean }>`
  ${props => props.$active && `
    background: ${theme.colors.secondary[500]};
    color: white;

    &:hover {
      background: ${theme.colors.secondary[600]};
    }
  `}

  ${media.maxSm} {
    font-size: ${theme.fontSizes.xs};
    padding: ${theme.spacing[1]} ${theme.spacing[2]};
  }
`

const ContentContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: ${theme.spacing[6]};
  max-width: 1200px;
  margin: 0 auto;

  ${media.maxLg} {
    grid-template-columns: 1fr;
    gap: ${theme.spacing[4]};
  }
`

const LeaderboardContainer = styled(Card)`
  padding: ${theme.spacing[6]};

  ${media.maxMd} {
    padding: ${theme.spacing[4]};
  }

  ${media.maxSm} {
    padding: ${theme.spacing[3]};
  }
`

const LeaderboardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing[4]};

  h2 {
    color: ${theme.colors.text.primary};
    font-size: ${theme.fontSizes.xl};
    margin: 0;
    display: flex;
    align-items: center;
    gap: ${theme.spacing[2]};
  }

  .last-updated {
    color: ${theme.colors.text.secondary};
    font-size: ${theme.fontSizes.sm};
  }
`

const LeaderboardList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`

const LeaderboardEntry = styled.div<{ $isCurrentUser?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[3]};
  background: ${props => props.$isCurrentUser
    ? 'rgba(251, 191, 36, 0.1)'
    : 'rgba(255, 255, 255, 0.05)'
  };
  border: 1px solid ${props => props.$isCurrentUser
    ? 'rgba(251, 191, 36, 0.3)'
    : 'rgba(255, 255, 255, 0.1)'
  };
  border-radius: ${theme.borderRadius.lg};
  transition: all ${theme.transitions.base} ease-in-out;

  &:hover {
    background: ${props => props.$isCurrentUser
      ? 'rgba(251, 191, 36, 0.15)'
      : 'rgba(255, 255, 255, 0.08)'
    };
    transform: translateY(-1px);
  }

  ${media.maxSm} {
    padding: ${theme.spacing[2]};
    gap: ${theme.spacing[2]};
  }
`

const Rank = styled.div<{ $rank: number }>`
  font-size: ${theme.fontSizes.lg};
  font-weight: ${theme.fontWeights.bold};
  color: ${props => {
    if (props.$rank === 1) return '#ffd700'
    if (props.$rank === 2) return '#c0c0c0'
    if (props.$rank === 3) return '#cd7f32'
    return theme.colors.text.secondary
  }};
  min-width: 40px;
  text-align: center;

  ${media.maxSm} {
    font-size: ${theme.fontSizes.base};
    min-width: 30px;
  }
`

const LeaderboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<LeaderboardType>('global_score')
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardData | null>(null)
  const [friends, setFriends] = useState<Friend[]>([])
  const [loading, setLoading] = useState(false)

  // 加载排行榜数据
  useEffect(() => {
    setLoading(true)
    // 模拟API调用
    setTimeout(() => {
      setLeaderboardData(generateMockLeaderboardData(activeTab))
      setLoading(false)
    }, 500)
  }, [activeTab])

  // 加载好友数据
  useEffect(() => {
    setFriends(generateMockFriends())
  }, [])

  const tabs = Object.entries(LEADERBOARD_CONFIG).map(([key, config]) => ({
    key: key as LeaderboardType,
    ...config
  }))

  return (
    <Container>
      <Header>
        <Title>🏆 排行榜</Title>

        <TabContainer>
          {tabs.map(tab => (
            <TabButton
              key={tab.key}
              variant="ghost"
              size="sm"
              $active={activeTab === tab.key}
              onClick={() => setActiveTab(tab.key)}
            >
              {tab.icon} {tab.name}
            </TabButton>
          ))}
        </TabContainer>
      </Header>

      <ContentContainer>
        <LeaderboardContainer>
          {leaderboardData && (
            <>
              <LeaderboardHeader>
                <h2>
                  {LEADERBOARD_CONFIG[activeTab].icon} {LEADERBOARD_CONFIG[activeTab].name}
                </h2>
                <div className="last-updated">
                  最后更新: {leaderboardData.lastUpdated.toLocaleTimeString()}
                </div>
              </LeaderboardHeader>

              {loading ? (
                <div style={{ textAlign: 'center', padding: '2rem', color: theme.colors.text.secondary }}>
                  加载中...
                </div>
              ) : (
                <LeaderboardList>
                  {leaderboardData.entries.map(entry => (
                    <LeaderboardEntry
                      key={entry.id}
                      $isCurrentUser={entry.isCurrentUser}
                    >
                      <Rank $rank={entry.rank}>
                        {entry.rank <= 3 ? (
                          entry.rank === 1 ? '🥇' : entry.rank === 2 ? '🥈' : '🥉'
                        ) : (
                          entry.rank
                        )}
                      </Rank>

                      <img
                        src={entry.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${entry.userId}`}
                        alt={entry.username}
                        style={{
                          width: '48px',
                          height: '48px',
                          borderRadius: '50%',
                          border: '2px solid rgba(255, 255, 255, 0.2)'
                        }}
                      />

                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div style={{
                          color: theme.colors.text.primary,
                          fontWeight: theme.fontWeights.semibold,
                          fontSize: theme.fontSizes.base,
                          marginBottom: '2px'
                        }}>
                          {entry.username}
                          {entry.isCurrentUser && ' (你)'}
                        </div>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: theme.spacing[1],
                          fontSize: theme.fontSizes.xs,
                          color: theme.colors.text.secondary
                        }}>
                          <div style={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            background: entry.isOnline ? '#10b981' : '#6b7280'
                          }} />
                          {entry.isOnline ? '在线' : '离线'}
                          {entry.isFriend && ' • 好友'}
                        </div>
                      </div>

                      <div style={{ textAlign: 'right' }}>
                        <div style={{
                          color: theme.colors.secondary[400],
                          fontWeight: theme.fontWeights.bold,
                          fontSize: theme.fontSizes.lg
                        }}>
                          {formatValue(entry.value, activeTab)}
                        </div>
                        <div style={{
                          color: theme.colors.text.secondary,
                          fontSize: theme.fontSizes.xs
                        }}>
                          {LEADERBOARD_CONFIG[activeTab].valueLabel}
                        </div>
                      </div>
                    </LeaderboardEntry>
                  ))}
                </LeaderboardList>
              )}
            </>
          )}
        </LeaderboardContainer>

        {/* 好友面板 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing[4] }}>
          <Card style={{ padding: theme.spacing[4] }}>
            <h3 style={{
              color: theme.colors.text.primary,
              margin: '0 0 1rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing[2]
            }}>
              👥 好友动态
            </h3>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: theme.spacing[2]
            }}>
              {friends.slice(0, 5).map(friend => (
                <div key={friend.id} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: theme.spacing[2],
                  padding: theme.spacing[2],
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: theme.borderRadius.base
                }}>
                  <img
                    src={friend.avatar}
                    alt={friend.username}
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%'
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{
                      color: theme.colors.text.primary,
                      fontSize: theme.fontSizes.sm,
                      fontWeight: theme.fontWeights.medium
                    }}>
                      {friend.username}
                    </div>
                    <div style={{
                      color: theme.colors.text.secondary,
                      fontSize: theme.fontSizes.xs
                    }}>
                      等级 {friend.level} • {friend.totalScore.toLocaleString()} 分
                    </div>
                  </div>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: friend.isOnline ? '#10b981' : '#6b7280'
                  }} />
                </div>
              ))}
            </div>
          </Card>

          <Card style={{ padding: theme.spacing[4] }}>
            <h3 style={{
              color: theme.colors.text.primary,
              margin: '0 0 1rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing[2]
            }}>
              📊 我的排名
            </h3>
            {leaderboardData?.currentUserEntry && (
              <div style={{
                background: 'rgba(251, 191, 36, 0.1)',
                border: '1px solid rgba(251, 191, 36, 0.3)',
                borderRadius: theme.borderRadius.lg,
                padding: theme.spacing[3]
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: theme.spacing[2]
                }}>
                  <span style={{ color: theme.colors.text.primary }}>当前排名</span>
                  <span style={{
                    color: theme.colors.secondary[400],
                    fontWeight: theme.fontWeights.bold,
                    fontSize: theme.fontSizes.lg
                  }}>
                    #{leaderboardData.currentUserRank}
                  </span>
                </div>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <span style={{ color: theme.colors.text.secondary, fontSize: theme.fontSizes.sm }}>
                    {LEADERBOARD_CONFIG[activeTab].valueLabel}
                  </span>
                  <span style={{
                    color: theme.colors.secondary[400],
                    fontWeight: theme.fontWeights.semibold
                  }}>
                    {formatValue(leaderboardData.currentUserEntry.value, activeTab)}
                  </span>
                </div>
              </div>
            )}
          </Card>
        </div>
      </ContentContainer>
    </Container>
  )
}

// 格式化数值显示
function formatValue(value: number, type: LeaderboardType): string {
  switch (type) {
    case 'speed_run':
      return `${value}秒`
    case 'combo_record':
      return `${value}连击`
    case 'level_completion':
      return `${value}关`
    case 'achievement_count':
      return `${value}个`
    default:
      return value.toLocaleString()
  }
}

export default LeaderboardPage
