import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { Database } from '../database/database';
import { hashPassword, comparePassword } from '../utils/password';
import { generateToken, generateRefreshToken } from '../utils/jwt';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = Router();
const db = Database.getInstance();

// 验证模式
const registerSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  email: Joi.string().email().optional(),
  password: Joi.string().min(6).required(),
  displayName: Joi.string().max(50).optional()
});

const loginSchema = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required()
});

// 用户注册
router.post('/register', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = registerSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const { username, email, password, displayName } = value;

  // 检查用户名是否已存在
  const existingUser = await db.get(
    'SELECT id FROM users WHERE username = ?',
    [username]
  );

  if (existingUser) {
    throw createError(409, 'USER_001', '用户名已存在');
  }

  // 检查邮箱是否已存在
  if (email) {
    const existingEmail = await db.get(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingEmail) {
      throw createError(409, 'USER_003', '邮箱已被使用');
    }
  }

  // 创建用户
  const passwordHash = await hashPassword(password);
  const result = await db.run(
    `INSERT INTO users (username, email, password_hash, display_name, coins, gems) 
     VALUES (?, ?, ?, ?, ?, ?)`,
    [username, email, passwordHash, displayName || username, 100, 5]
  );

  const userId = result.lastID!;

  // 生成token
  const tokenPayload = { id: userId, username };
  const token = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  // 获取用户信息
  const user = await db.get(
    `SELECT id, username, display_name, coins, gems, current_level, total_score 
     FROM users WHERE id = ?`,
    [userId]
  );

  res.status(201).json({
    success: true,
    data: {
      user: {
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        coins: user.coins,
        gems: user.gems,
        currentLevel: user.current_level,
        totalScore: user.total_score
      },
      token,
      refreshToken
    },
    message: '注册成功'
  });
}));

// 用户登录
router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    throw createError(400, 'VALIDATION_ERROR', error.details[0].message);
  }

  const { username, password } = value;

  // 查找用户
  const user = await db.get(
    `SELECT id, username, password_hash, display_name, coins, gems, current_level, total_score 
     FROM users WHERE username = ?`,
    [username]
  );

  if (!user) {
    throw createError(401, 'USER_002', '用户名或密码错误');
  }

  // 验证密码
  const isValidPassword = await comparePassword(password, user.password_hash);
  if (!isValidPassword) {
    throw createError(401, 'USER_002', '用户名或密码错误');
  }

  // 更新最后登录时间
  await db.run(
    'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
    [user.id]
  );

  // 生成token
  const tokenPayload = { id: user.id, username: user.username };
  const token = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        coins: user.coins,
        gems: user.gems,
        currentLevel: user.current_level,
        totalScore: user.total_score
      },
      token,
      refreshToken
    },
    message: '登录成功'
  });
}));

// 刷新token
router.post('/refresh', authenticateToken, asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = req.user!;
  
  // 生成新的token
  const tokenPayload = { id: user.id, username: user.username };
  const token = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  res.json({
    success: true,
    data: {
      token,
      refreshToken
    },
    message: 'Token刷新成功'
  });
}));

// 验证token
router.get('/verify', authenticateToken, asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  // 获取最新用户信息
  const user = await db.get(
    `SELECT id, username, display_name, coins, gems, current_level, total_score
     FROM users WHERE id = ?`,
    [userId]
  );

  if (!user) {
    throw createError(404, 'USER_002', '用户不存在');
  }

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        coins: user.coins,
        gems: user.gems,
        currentLevel: user.current_level,
        totalScore: user.total_score
      }
    },
    message: 'Token验证成功'
  });
}));

// 获取当前用户信息 (frontend expects this endpoint)
router.get('/me', authenticateToken, asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user!.id;

  // 获取最新用户信息
  const user = await db.get(
    `SELECT id, username, email, display_name, coins, gems, current_level, total_score,
            created_at, last_login_at, is_active, is_premium
     FROM users WHERE id = ?`,
    [userId]
  );

  if (!user) {
    throw createError(404, 'USER_002', '用户不存在');
  }

  res.json({
    id: user.id,
    username: user.username,
    email: user.email,
    level: user.current_level,
    experience: user.total_score,
    coins: user.coins,
    gems: user.gems,
    isActive: user.is_active === 1,
    isPremium: user.is_premium === 1,
    createdAt: user.created_at,
    lastLoginAt: user.last_login_at,
  });
}));

export { router as authRoutes };
