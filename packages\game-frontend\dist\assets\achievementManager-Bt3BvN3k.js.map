{"version": 3, "file": "achievementManager-Bt3BvN3k.js", "sources": ["../../src/utils/achievementManager.ts"], "sourcesContent": ["import {\n  Achievement,\n  PlayerStats,\n  // AchievementProgress,\n  AchievementUnlockedEvent,\n  ACHIEVEMENTS\n} from '../types/achievements'\n\nexport class AchievementManager {\n  private achievements: Map<string, Achievement> = new Map()\n  private playerStats: PlayerStats\n  private onAchievementUnlocked?: (event: AchievementUnlockedEvent) => void\n\n  constructor(\n    initialStats?: Partial<PlayerStats>,\n    onAchievementUnlocked?: (event: AchievementUnlockedEvent) => void\n  ) {\n    // 初始化成就\n    ACHIEVEMENTS.forEach(achievement => {\n      this.achievements.set(achievement.id, { ...achievement })\n    })\n\n    // 初始化玩家统计\n    this.playerStats = {\n      totalScore: 0,\n      totalGamesPlayed: 0,\n      totalGamesWon: 0,\n      totalTimePlayed: 0,\n      levelsCompleted: 0,\n      highestLevel: 0,\n      perfectLevels: 0,\n      maxCombo: 0,\n      totalCombos: 0,\n      comboCount: {},\n      specialGemsCreated: {},\n      specialGemsUsed: {},\n      powerupsUsed: {},\n      powerupsPurchased: {},\n      gemsMatched: 0,\n      totalMoves: 0,\n      fastestLevel: Infinity,\n      achievementsUnlocked: 0,\n      badgesCollected: [],\n      titlesEarned: [],\n      ...initialStats\n    }\n\n    this.onAchievementUnlocked = onAchievementUnlocked\n  }\n\n  // 更新玩家统计并检查成就\n  public updateStats(updates: Partial<PlayerStats>): AchievementUnlockedEvent[] {\n    const oldStats = { ...this.playerStats }\n    \n    // 更新统计数据\n    Object.assign(this.playerStats, updates)\n    \n    // 检查成就解锁\n    return this.checkAchievements(oldStats)\n  }\n\n  // 记录游戏结束\n  public recordGameEnd(gameData: {\n    score: number\n    won: boolean\n    level: number\n    timePlayed: number\n    moves: number\n    maxCombo: number\n    specialGemsCreated: Record<string, number>\n    powerupsUsed: Record<string, number>\n    perfect?: boolean\n  }): AchievementUnlockedEvent[] {\n    const updates: Partial<PlayerStats> = {\n      totalScore: this.playerStats.totalScore + gameData.score,\n      totalGamesPlayed: this.playerStats.totalGamesPlayed + 1,\n      totalTimePlayed: this.playerStats.totalTimePlayed + gameData.timePlayed,\n      totalMoves: this.playerStats.totalMoves + gameData.moves,\n      maxCombo: Math.max(this.playerStats.maxCombo, gameData.maxCombo),\n      totalCombos: this.playerStats.totalCombos + (gameData.maxCombo > 1 ? 1 : 0)\n    }\n\n    if (gameData.won) {\n      updates.totalGamesWon = this.playerStats.totalGamesWon + 1\n      updates.levelsCompleted = this.playerStats.levelsCompleted + 1\n      updates.highestLevel = Math.max(this.playerStats.highestLevel, gameData.level)\n      \n      if (gameData.perfect) {\n        updates.perfectLevels = this.playerStats.perfectLevels + 1\n      }\n      \n      if (gameData.timePlayed < this.playerStats.fastestLevel) {\n        updates.fastestLevel = gameData.timePlayed\n      }\n    }\n\n    // 更新连击统计\n    if (gameData.maxCombo > 1) {\n      const comboCount = { ...this.playerStats.comboCount }\n      comboCount[gameData.maxCombo] = (comboCount[gameData.maxCombo] || 0) + 1\n      updates.comboCount = comboCount\n    }\n\n    // 更新特殊宝石统计\n    const specialGemsCreated = { ...this.playerStats.specialGemsCreated }\n    Object.entries(gameData.specialGemsCreated).forEach(([type, count]) => {\n      specialGemsCreated[type] = (specialGemsCreated[type] || 0) + count\n    })\n    updates.specialGemsCreated = specialGemsCreated\n\n    // 更新道具使用统计\n    const powerupsUsed = { ...this.playerStats.powerupsUsed }\n    Object.entries(gameData.powerupsUsed).forEach(([type, count]) => {\n      powerupsUsed[type] = (powerupsUsed[type] || 0) + count\n    })\n    updates.powerupsUsed = powerupsUsed\n\n    return this.updateStats(updates)\n  }\n\n  // 检查成就解锁\n  private checkAchievements(_oldStats: PlayerStats): AchievementUnlockedEvent[] {\n    const unlockedEvents: AchievementUnlockedEvent[] = []\n\n    for (const achievement of this.achievements.values()) {\n      if (achievement.unlocked) continue\n\n      const isUnlocked = this.checkAchievementCondition(achievement)\n      \n      if (isUnlocked) {\n        achievement.unlocked = true\n        achievement.unlockedAt = new Date()\n        achievement.progress = 100\n\n        const event = this.createUnlockedEvent(achievement)\n        unlockedEvents.push(event)\n\n        // 应用奖励\n        this.applyRewards(achievement)\n\n        // 触发回调\n        if (this.onAchievementUnlocked) {\n          this.onAchievementUnlocked(event)\n        }\n      } else {\n        // 更新进度\n        achievement.progress = this.calculateProgress(achievement)\n      }\n    }\n\n    return unlockedEvents\n  }\n\n  // 检查单个成就条件\n  private checkAchievementCondition(achievement: Achievement): boolean {\n    const { condition } = achievement\n    const stats = this.playerStats\n\n    switch (condition.type) {\n      case 'single_game_score':\n        // 这个需要在游戏结束时单独检查\n        return false\n      \n      case 'total_score':\n        return stats.totalScore >= condition.target\n      \n      case 'levels_completed':\n        return stats.levelsCompleted >= condition.target\n      \n      case 'perfect_levels':\n        return stats.perfectLevels >= condition.target\n      \n      case 'max_combo':\n        return stats.maxCombo >= condition.target\n      \n      case 'games_won':\n        return stats.totalGamesWon >= condition.target\n      \n      case 'total_time_played':\n        return stats.totalTimePlayed >= condition.target\n      \n      case 'fastest_level':\n        return stats.fastestLevel <= condition.target\n      \n      case 'total_powerups_used':\n        const totalPowerups = Object.values(stats.powerupsUsed).reduce((sum, count) => sum + count, 0)\n        return totalPowerups >= condition.target\n      \n      default:\n        // 处理特殊宝石相关条件\n        if (condition.type.startsWith('special_gems_created_')) {\n          const gemType = condition.type.replace('special_gems_created_', '')\n          return (stats.specialGemsCreated[gemType] || 0) >= condition.target\n        }\n        \n        return false\n    }\n  }\n\n  // 计算成就进度\n  private calculateProgress(achievement: Achievement): number {\n    const { condition } = achievement\n    const stats = this.playerStats\n\n    let current = 0\n\n    switch (condition.type) {\n      case 'total_score':\n        current = stats.totalScore\n        break\n      case 'levels_completed':\n        current = stats.levelsCompleted\n        break\n      case 'perfect_levels':\n        current = stats.perfectLevels\n        break\n      case 'max_combo':\n        current = stats.maxCombo\n        break\n      case 'games_won':\n        current = stats.totalGamesWon\n        break\n      case 'total_time_played':\n        current = stats.totalTimePlayed\n        break\n      case 'total_powerups_used':\n        current = Object.values(stats.powerupsUsed).reduce((sum, count) => sum + count, 0)\n        break\n      default:\n        if (condition.type.startsWith('special_gems_created_')) {\n          const gemType = condition.type.replace('special_gems_created_', '')\n          current = stats.specialGemsCreated[gemType] || 0\n        }\n        break\n    }\n\n    return Math.min(100, (current / condition.target) * 100)\n  }\n\n  // 创建解锁事件\n  private createUnlockedEvent(achievement: Achievement): AchievementUnlockedEvent {\n    return {\n      achievement: { ...achievement },\n      timestamp: new Date(),\n      newRewards: {\n        coins: achievement.rewards.coins || 0,\n        powerups: achievement.rewards.powerups || [],\n        titles: achievement.rewards.title ? [achievement.rewards.title] : [],\n        badges: achievement.rewards.badge ? [achievement.rewards.badge] : []\n      }\n    }\n  }\n\n  // 应用奖励\n  private applyRewards(achievement: Achievement): void {\n    this.playerStats.achievementsUnlocked++\n    \n    if (achievement.rewards.title) {\n      this.playerStats.titlesEarned.push(achievement.rewards.title)\n    }\n    \n    if (achievement.rewards.badge) {\n      this.playerStats.badgesCollected.push(achievement.rewards.badge)\n    }\n  }\n\n  // 检查单局分数成就\n  public checkSingleGameScore(score: number): AchievementUnlockedEvent[] {\n    const events: AchievementUnlockedEvent[] = []\n    \n    for (const achievement of this.achievements.values()) {\n      if (achievement.unlocked || achievement.condition.type !== 'single_game_score') {\n        continue\n      }\n      \n      if (score >= achievement.condition.target) {\n        achievement.unlocked = true\n        achievement.unlockedAt = new Date()\n        achievement.progress = 100\n        \n        const event = this.createUnlockedEvent(achievement)\n        events.push(event)\n        \n        this.applyRewards(achievement)\n        \n        if (this.onAchievementUnlocked) {\n          this.onAchievementUnlocked(event)\n        }\n      }\n    }\n    \n    return events\n  }\n\n  // 获取所有成就\n  public getAllAchievements(): Achievement[] {\n    return Array.from(this.achievements.values())\n      .filter(achievement => !achievement.hidden || achievement.unlocked)\n      .sort((a, b) => a.order - b.order)\n  }\n\n  // 获取已解锁成就\n  public getUnlockedAchievements(): Achievement[] {\n    return this.getAllAchievements().filter(achievement => achievement.unlocked)\n  }\n\n  // 获取进行中的成就\n  public getInProgressAchievements(): Achievement[] {\n    return this.getAllAchievements().filter(achievement => !achievement.unlocked && achievement.progress > 0)\n  }\n\n  // 获取玩家统计\n  public getPlayerStats(): PlayerStats {\n    return { ...this.playerStats }\n  }\n\n  // 获取成就完成率\n  public getCompletionRate(): number {\n    const total = this.getAllAchievements().length\n    const unlocked = this.getUnlockedAchievements().length\n    return total > 0 ? (unlocked / total) * 100 : 0\n  }\n}\n"], "names": ["AchievementManager", "initialStats", "onAchievementUnlocked", "__publicField", "ACHIEVEMENTS", "achievement", "updates", "oldStats", "gameData", "comboCount", "specialGemsCreated", "type", "count", "powerupsUsed", "_oldStats", "unlockedEvents", "event", "condition", "stats", "sum", "gemType", "current", "score", "events", "a", "b", "total", "unlocked"], "mappings": "mNAQO,MAAMA,CAAmB,CAK9B,YACEC,EACAC,EACA,CAPMC,EAAA,wBAA6C,KAC7CA,EAAA,oBACAA,EAAA,8BAONC,EAAa,QAAQC,GAAe,CAClC,KAAK,aAAa,IAAIA,EAAY,GAAI,CAAE,GAAGA,EAAa,CAC1D,CAAC,EAGD,KAAK,YAAc,CACjB,WAAY,EACZ,iBAAkB,EAClB,cAAe,EACf,gBAAiB,EACjB,gBAAiB,EACjB,aAAc,EACd,cAAe,EACf,SAAU,EACV,YAAa,EACb,WAAY,CAAA,EACZ,mBAAoB,CAAA,EACpB,gBAAiB,CAAA,EACjB,aAAc,CAAA,EACd,kBAAmB,CAAA,EACnB,YAAa,EACb,WAAY,EACZ,aAAc,IACd,qBAAsB,EACtB,gBAAiB,CAAA,EACjB,aAAc,CAAA,EACd,GAAGJ,CAAA,EAGL,KAAK,sBAAwBC,CAC/B,CAGO,YAAYI,EAA2D,CAC5E,MAAMC,EAAW,CAAE,GAAG,KAAK,WAAA,EAG3B,cAAO,OAAO,KAAK,YAAaD,CAAO,EAGhC,KAAK,kBAAkBC,CAAQ,CACxC,CAGO,cAAcC,EAUU,CAC7B,MAAMF,EAAgC,CACpC,WAAY,KAAK,YAAY,WAAaE,EAAS,MACnD,iBAAkB,KAAK,YAAY,iBAAmB,EACtD,gBAAiB,KAAK,YAAY,gBAAkBA,EAAS,WAC7D,WAAY,KAAK,YAAY,WAAaA,EAAS,MACnD,SAAU,KAAK,IAAI,KAAK,YAAY,SAAUA,EAAS,QAAQ,EAC/D,YAAa,KAAK,YAAY,aAAeA,EAAS,SAAW,EAAI,EAAI,EAAA,EAkB3E,GAfIA,EAAS,MACXF,EAAQ,cAAgB,KAAK,YAAY,cAAgB,EACzDA,EAAQ,gBAAkB,KAAK,YAAY,gBAAkB,EAC7DA,EAAQ,aAAe,KAAK,IAAI,KAAK,YAAY,aAAcE,EAAS,KAAK,EAEzEA,EAAS,UACXF,EAAQ,cAAgB,KAAK,YAAY,cAAgB,GAGvDE,EAAS,WAAa,KAAK,YAAY,eACzCF,EAAQ,aAAeE,EAAS,aAKhCA,EAAS,SAAW,EAAG,CACzB,MAAMC,EAAa,CAAE,GAAG,KAAK,YAAY,UAAA,EACzCA,EAAWD,EAAS,QAAQ,GAAKC,EAAWD,EAAS,QAAQ,GAAK,GAAK,EACvEF,EAAQ,WAAaG,CACvB,CAGA,MAAMC,EAAqB,CAAE,GAAG,KAAK,YAAY,kBAAA,EACjD,OAAO,QAAQF,EAAS,kBAAkB,EAAE,QAAQ,CAAC,CAACG,EAAMC,CAAK,IAAM,CACrEF,EAAmBC,CAAI,GAAKD,EAAmBC,CAAI,GAAK,GAAKC,CAC/D,CAAC,EACDN,EAAQ,mBAAqBI,EAG7B,MAAMG,EAAe,CAAE,GAAG,KAAK,YAAY,YAAA,EAC3C,cAAO,QAAQL,EAAS,YAAY,EAAE,QAAQ,CAAC,CAACG,EAAMC,CAAK,IAAM,CAC/DC,EAAaF,CAAI,GAAKE,EAAaF,CAAI,GAAK,GAAKC,CACnD,CAAC,EACDN,EAAQ,aAAeO,EAEhB,KAAK,YAAYP,CAAO,CACjC,CAGQ,kBAAkBQ,EAAoD,CAC5E,MAAMC,EAA6C,CAAA,EAEnD,UAAWV,KAAe,KAAK,aAAa,OAAA,EAAU,CACpD,GAAIA,EAAY,SAAU,SAI1B,GAFmB,KAAK,0BAA0BA,CAAW,EAE7C,CACdA,EAAY,SAAW,GACvBA,EAAY,eAAiB,KAC7BA,EAAY,SAAW,IAEvB,MAAMW,EAAQ,KAAK,oBAAoBX,CAAW,EAClDU,EAAe,KAAKC,CAAK,EAGzB,KAAK,aAAaX,CAAW,EAGzB,KAAK,uBACP,KAAK,sBAAsBW,CAAK,CAEpC,MAEEX,EAAY,SAAW,KAAK,kBAAkBA,CAAW,CAE7D,CAEA,OAAOU,CACT,CAGQ,0BAA0BV,EAAmC,CACnE,KAAM,CAAE,UAAAY,GAAcZ,EAChBa,EAAQ,KAAK,YAEnB,OAAQD,EAAU,KAAA,CAChB,IAAK,oBAEH,MAAO,GAET,IAAK,cACH,OAAOC,EAAM,YAAcD,EAAU,OAEvC,IAAK,mBACH,OAAOC,EAAM,iBAAmBD,EAAU,OAE5C,IAAK,iBACH,OAAOC,EAAM,eAAiBD,EAAU,OAE1C,IAAK,YACH,OAAOC,EAAM,UAAYD,EAAU,OAErC,IAAK,YACH,OAAOC,EAAM,eAAiBD,EAAU,OAE1C,IAAK,oBACH,OAAOC,EAAM,iBAAmBD,EAAU,OAE5C,IAAK,gBACH,OAAOC,EAAM,cAAgBD,EAAU,OAEzC,IAAK,sBAEH,OADsB,OAAO,OAAOC,EAAM,YAAY,EAAE,OAAO,CAACC,EAAKP,IAAUO,EAAMP,EAAO,CAAC,GACrEK,EAAU,OAEpC,QAEE,GAAIA,EAAU,KAAK,WAAW,uBAAuB,EAAG,CACtD,MAAMG,EAAUH,EAAU,KAAK,QAAQ,wBAAyB,EAAE,EAClE,OAAQC,EAAM,mBAAmBE,CAAO,GAAK,IAAMH,EAAU,MAC/D,CAEA,MAAO,EAAA,CAEb,CAGQ,kBAAkBZ,EAAkC,CAC1D,KAAM,CAAE,UAAAY,GAAcZ,EAChBa,EAAQ,KAAK,YAEnB,IAAIG,EAAU,EAEd,OAAQJ,EAAU,KAAA,CAChB,IAAK,cACHI,EAAUH,EAAM,WAChB,MACF,IAAK,mBACHG,EAAUH,EAAM,gBAChB,MACF,IAAK,iBACHG,EAAUH,EAAM,cAChB,MACF,IAAK,YACHG,EAAUH,EAAM,SAChB,MACF,IAAK,YACHG,EAAUH,EAAM,cAChB,MACF,IAAK,oBACHG,EAAUH,EAAM,gBAChB,MACF,IAAK,sBACHG,EAAU,OAAO,OAAOH,EAAM,YAAY,EAAE,OAAO,CAACC,EAAKP,IAAUO,EAAMP,EAAO,CAAC,EACjF,MACF,QACE,GAAIK,EAAU,KAAK,WAAW,uBAAuB,EAAG,CACtD,MAAMG,EAAUH,EAAU,KAAK,QAAQ,wBAAyB,EAAE,EAClEI,EAAUH,EAAM,mBAAmBE,CAAO,GAAK,CACjD,CACA,KAAA,CAGJ,OAAO,KAAK,IAAI,IAAMC,EAAUJ,EAAU,OAAU,GAAG,CACzD,CAGQ,oBAAoBZ,EAAoD,CAC9E,MAAO,CACL,YAAa,CAAE,GAAGA,CAAA,EAClB,cAAe,KACf,WAAY,CACV,MAAOA,EAAY,QAAQ,OAAS,EACpC,SAAUA,EAAY,QAAQ,UAAY,CAAA,EAC1C,OAAQA,EAAY,QAAQ,MAAQ,CAACA,EAAY,QAAQ,KAAK,EAAI,CAAA,EAClE,OAAQA,EAAY,QAAQ,MAAQ,CAACA,EAAY,QAAQ,KAAK,EAAI,CAAA,CAAC,CACrE,CAEJ,CAGQ,aAAaA,EAAgC,CACnD,KAAK,YAAY,uBAEbA,EAAY,QAAQ,OACtB,KAAK,YAAY,aAAa,KAAKA,EAAY,QAAQ,KAAK,EAG1DA,EAAY,QAAQ,OACtB,KAAK,YAAY,gBAAgB,KAAKA,EAAY,QAAQ,KAAK,CAEnE,CAGO,qBAAqBiB,EAA2C,CACrE,MAAMC,EAAqC,CAAA,EAE3C,UAAWlB,KAAe,KAAK,aAAa,OAAA,EAC1C,GAAI,EAAAA,EAAY,UAAYA,EAAY,UAAU,OAAS,sBAIvDiB,GAASjB,EAAY,UAAU,OAAQ,CACzCA,EAAY,SAAW,GACvBA,EAAY,eAAiB,KAC7BA,EAAY,SAAW,IAEvB,MAAMW,EAAQ,KAAK,oBAAoBX,CAAW,EAClDkB,EAAO,KAAKP,CAAK,EAEjB,KAAK,aAAaX,CAAW,EAEzB,KAAK,uBACP,KAAK,sBAAsBW,CAAK,CAEpC,CAGF,OAAOO,CACT,CAGO,oBAAoC,CACzC,OAAO,MAAM,KAAK,KAAK,aAAa,QAAQ,EACzC,OAAOlB,GAAe,CAACA,EAAY,QAAUA,EAAY,QAAQ,EACjE,KAAK,CAACmB,EAAGC,IAAMD,EAAE,MAAQC,EAAE,KAAK,CACrC,CAGO,yBAAyC,CAC9C,OAAO,KAAK,qBAAqB,OAAOpB,GAAeA,EAAY,QAAQ,CAC7E,CAGO,2BAA2C,CAChD,OAAO,KAAK,qBAAqB,OAAOA,GAAe,CAACA,EAAY,UAAYA,EAAY,SAAW,CAAC,CAC1G,CAGO,gBAA8B,CACnC,MAAO,CAAE,GAAG,KAAK,WAAA,CACnB,CAGO,mBAA4B,CACjC,MAAMqB,EAAQ,KAAK,mBAAA,EAAqB,OAClCC,EAAW,KAAK,wBAAA,EAA0B,OAChD,OAAOD,EAAQ,EAAKC,EAAWD,EAAS,IAAM,CAChD,CACF"}