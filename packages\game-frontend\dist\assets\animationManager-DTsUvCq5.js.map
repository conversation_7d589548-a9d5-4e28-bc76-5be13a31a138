{"version": 3, "file": "animationManager-DTsUvCq5.js", "sources": ["../../src/utils/animationManager.ts"], "sourcesContent": ["export interface Position {\n  row: number\n  col: number\n}\n\nexport interface AnimationFrame {\n  timestamp: number\n  progress: number // 0 to 1\n}\n\nexport interface GemAnimation {\n  id: string\n  type: 'swap' | 'fall' | 'eliminate' | 'spawn'\n  startTime: number\n  duration: number\n  from: Position\n  to: Position\n  gemType: number\n  isActive: boolean\n  onComplete?: () => void\n}\n\nexport interface ParticleEffect {\n  id: string\n  x: number\n  y: number\n  vx: number\n  vy: number\n  life: number\n  maxLife: number\n  color: string\n  size: number\n  type: 'star' | 'sparkle' | 'explosion'\n}\n\nexport class AnimationManager {\n  private animations: Map<string, GemAnimation> = new Map()\n  private particles: ParticleEffect[] = []\n  private animationId: number | null = null\n  private lastFrameTime: number = 0\n\n  constructor(private onAnimationUpdate: () => void) {}\n\n  // 开始动画循环\n  public start(): void {\n    if (this.animationId) return\n    \n    const animate = (timestamp: number) => {\n      this.update(timestamp)\n      this.onAnimationUpdate()\n      \n      if (this.hasActiveAnimations() || this.particles.length > 0) {\n        this.animationId = requestAnimationFrame(animate)\n      } else {\n        this.animationId = null\n      }\n    }\n    \n    this.animationId = requestAnimationFrame(animate)\n  }\n\n  // 停止动画循环\n  public stop(): void {\n    if (this.animationId) {\n      cancelAnimationFrame(this.animationId)\n      this.animationId = null\n    }\n  }\n\n  // 更新动画状态\n  public update(timestamp: number = performance.now()): void {\n    this.lastFrameTime = timestamp\n    \n    // 更新宝石动画\n    for (const [id, animation] of this.animations) {\n      const elapsed = timestamp - animation.startTime\n      const progress = Math.min(elapsed / animation.duration, 1)\n      \n      if (progress >= 1) {\n        animation.isActive = false\n        if (animation.onComplete) {\n          animation.onComplete()\n        }\n        this.animations.delete(id)\n      }\n    }\n    \n    // 更新粒子效果\n    this.particles = this.particles.filter(particle => {\n      particle.life -= 16 // 假设60fps，每帧约16ms\n      particle.x += particle.vx\n      particle.y += particle.vy\n      particle.vy += 0.2 // 重力\n      particle.vx *= 0.99 // 阻力\n      \n      return particle.life > 0\n    })\n  }\n\n  // 添加宝石交换动画\n  public addSwapAnimation(\n    gem1: { pos: Position; type: number },\n    gem2: { pos: Position; type: number },\n    duration: number = 300,\n    onComplete?: () => void\n  ): void {\n    const id1 = `swap_${gem1.pos.row}_${gem1.pos.col}_${Date.now()}`\n    const id2 = `swap_${gem2.pos.row}_${gem2.pos.col}_${Date.now()}_2`\n    \n    this.animations.set(id1, {\n      id: id1,\n      type: 'swap',\n      startTime: this.lastFrameTime,\n      duration,\n      from: gem1.pos,\n      to: gem2.pos,\n      gemType: gem1.type,\n      isActive: true,\n      onComplete: onComplete\n    })\n    \n    this.animations.set(id2, {\n      id: id2,\n      type: 'swap',\n      startTime: this.lastFrameTime,\n      duration,\n      from: gem2.pos,\n      to: gem1.pos,\n      gemType: gem2.type,\n      isActive: true\n    })\n    \n    // 不自动启动动画循环，由GameBoard的游戏循环控制\n  }\n\n  // 添加宝石下落动画\n  public addFallAnimation(\n    gemType: number,\n    from: Position,\n    to: Position,\n    duration: number = 400,\n    onComplete?: () => void\n  ): void {\n    const id = `fall_${to.row}_${to.col}_${Date.now()}`\n    \n    this.animations.set(id, {\n      id,\n      type: 'fall',\n      startTime: this.lastFrameTime,\n      duration,\n      from,\n      to,\n      gemType,\n      isActive: true,\n      onComplete\n    })\n    \n    // 不自动启动动画循环，由GameBoard的游戏循环控制\n  }\n\n  // 添加宝石消除动画\n  public addEliminateAnimation(\n    positions: Position[],\n    duration: number = 500,\n    onComplete?: () => void\n  ): void {\n    positions.forEach((pos, index) => {\n      const id = `eliminate_${pos.row}_${pos.col}_${Date.now()}`\n      \n      this.animations.set(id, {\n        id,\n        type: 'eliminate',\n        startTime: this.lastFrameTime + (index * 50), // 错开时间\n        duration,\n        from: pos,\n        to: pos,\n        gemType: 0,\n        isActive: true,\n        onComplete: index === positions.length - 1 ? onComplete : undefined\n      })\n    })\n    \n    // 添加粒子效果\n    positions.forEach(pos => {\n      this.addParticleEffect(pos, 'explosion')\n    })\n    \n    // 不自动启动动画循环，由GameBoard的游戏循环控制\n  }\n\n  // 添加粒子效果\n  public addParticleEffect(position: Position, type: ParticleEffect['type']): void {\n    const cellSize = 60 // 假设单元格大小\n    const centerX = position.col * cellSize + cellSize / 2\n    const centerY = position.row * cellSize + cellSize / 2\n    \n    const particleCount = type === 'explosion' ? 8 : 4\n    \n    for (let i = 0; i < particleCount; i++) {\n      const angle = (Math.PI * 2 * i) / particleCount\n      const speed = 2 + Math.random() * 3\n      \n      this.particles.push({\n        id: `particle_${Date.now()}_${i}`,\n        x: centerX,\n        y: centerY,\n        vx: Math.cos(angle) * speed,\n        vy: Math.sin(angle) * speed,\n        life: 1000 + Math.random() * 500,\n        maxLife: 1500,\n        color: this.getParticleColor(type),\n        size: 3 + Math.random() * 2,\n        type\n      })\n    }\n  }\n\n  // 获取粒子颜色\n  private getParticleColor(type: ParticleEffect['type']): string {\n    switch (type) {\n      case 'star':\n        return '#fbbf24'\n      case 'sparkle':\n        return '#60a5fa'\n      case 'explosion':\n        return '#f87171'\n      default:\n        return '#ffffff'\n    }\n  }\n\n  // 获取动画进度\n  public getAnimationProgress(id: string): number {\n    const animation = this.animations.get(id)\n    if (!animation) return 1\n    \n    const elapsed = this.lastFrameTime - animation.startTime\n    return Math.min(elapsed / animation.duration, 1)\n  }\n\n  // 获取当前位置（用于插值）\n  public getInterpolatedPosition(animation: GemAnimation): Position {\n    const elapsed = this.lastFrameTime - animation.startTime\n    const progress = Math.min(elapsed / animation.duration, 1)\n    \n    // 使用缓动函数\n    const easedProgress = this.easeInOutCubic(progress)\n    \n    return {\n      row: animation.from.row + (animation.to.row - animation.from.row) * easedProgress,\n      col: animation.from.col + (animation.to.col - animation.from.col) * easedProgress\n    }\n  }\n\n  // 缓动函数\n  private easeInOutCubic(t: number): number {\n    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2\n  }\n\n  // 检查是否有活跃动画\n  public hasActiveAnimations(): boolean {\n    return this.animations.size > 0\n  }\n\n  // 获取所有活跃动画\n  public getActiveAnimations(): GemAnimation[] {\n    return Array.from(this.animations.values())\n  }\n\n  // 获取所有粒子\n  public getParticles(): ParticleEffect[] {\n    return this.particles\n  }\n\n  // 清除所有动画\n  public clear(): void {\n    this.animations.clear()\n    this.particles.length = 0\n    this.stop()\n  }\n}\n"], "names": ["AnimationManager", "onAnimationUpdate", "__publicField", "animate", "timestamp", "id", "animation", "elapsed", "particle", "gem1", "gem2", "duration", "onComplete", "id1", "id2", "gemType", "from", "to", "positions", "pos", "index", "position", "type", "centerX", "centerY", "particleCount", "i", "angle", "speed", "progress", "easedProgress"], "mappings": "oKAmCO,MAAMA,CAAiB,CAM5B,YAAoBC,EAA+B,CAL3CC,EAAA,sBAA4C,KAC5CA,EAAA,iBAA8B,CAAA,GAC9BA,EAAA,mBAA6B,MAC7BA,EAAA,qBAAwB,GAEZ,KAAA,kBAAAD,CAAgC,CAG7C,OAAc,CACnB,GAAI,KAAK,YAAa,OAEtB,MAAME,EAAWC,GAAsB,CACrC,KAAK,OAAOA,CAAS,EACrB,KAAK,kBAAA,EAED,KAAK,oBAAA,GAAyB,KAAK,UAAU,OAAS,EACxD,KAAK,YAAc,sBAAsBD,CAAO,EAEhD,KAAK,YAAc,IAEvB,EAEA,KAAK,YAAc,sBAAsBA,CAAO,CAClD,CAGO,MAAa,CACd,KAAK,cACP,qBAAqB,KAAK,WAAW,EACrC,KAAK,YAAc,KAEvB,CAGO,OAAOC,EAAoB,YAAY,MAAa,CACzD,KAAK,cAAgBA,EAGrB,SAAW,CAACC,EAAIC,CAAS,IAAK,KAAK,WAAY,CAC7C,MAAMC,EAAUH,EAAYE,EAAU,UACrB,KAAK,IAAIC,EAAUD,EAAU,SAAU,CAAC,GAEzC,IACdA,EAAU,SAAW,GACjBA,EAAU,YACZA,EAAU,WAAA,EAEZ,KAAK,WAAW,OAAOD,CAAE,EAE7B,CAGA,KAAK,UAAY,KAAK,UAAU,OAAOG,IACrCA,EAAS,MAAQ,GACjBA,EAAS,GAAKA,EAAS,GACvBA,EAAS,GAAKA,EAAS,GACvBA,EAAS,IAAM,GACfA,EAAS,IAAM,IAERA,EAAS,KAAO,EACxB,CACH,CAGO,iBACLC,EACAC,EACAC,EAAmB,IACnBC,EACM,CACN,MAAMC,EAAM,QAAQJ,EAAK,IAAI,GAAG,IAAIA,EAAK,IAAI,GAAG,IAAI,KAAK,IAAA,CAAK,GACxDK,EAAM,QAAQJ,EAAK,IAAI,GAAG,IAAIA,EAAK,IAAI,GAAG,IAAI,KAAK,IAAA,CAAK,KAE9D,KAAK,WAAW,IAAIG,EAAK,CACvB,GAAIA,EACJ,KAAM,OACN,UAAW,KAAK,cAChB,SAAAF,EACA,KAAMF,EAAK,IACX,GAAIC,EAAK,IACT,QAASD,EAAK,KACd,SAAU,GACV,WAAAG,CAAA,CACD,EAED,KAAK,WAAW,IAAIE,EAAK,CACvB,GAAIA,EACJ,KAAM,OACN,UAAW,KAAK,cAChB,SAAAH,EACA,KAAMD,EAAK,IACX,GAAID,EAAK,IACT,QAASC,EAAK,KACd,SAAU,EAAA,CACX,CAGH,CAGO,iBACLK,EACAC,EACAC,EACAN,EAAmB,IACnBC,EACM,CACN,MAAMP,EAAK,QAAQY,EAAG,GAAG,IAAIA,EAAG,GAAG,IAAI,KAAK,IAAA,CAAK,GAEjD,KAAK,WAAW,IAAIZ,EAAI,CACtB,GAAAA,EACA,KAAM,OACN,UAAW,KAAK,cAChB,SAAAM,EACA,KAAAK,EACA,GAAAC,EACA,QAAAF,EACA,SAAU,GACV,WAAAH,CAAA,CACD,CAGH,CAGO,sBACLM,EACAP,EAAmB,IACnBC,EACM,CACNM,EAAU,QAAQ,CAACC,EAAKC,IAAU,CAChC,MAAMf,EAAK,aAAac,EAAI,GAAG,IAAIA,EAAI,GAAG,IAAI,KAAK,IAAA,CAAK,GAExD,KAAK,WAAW,IAAId,EAAI,CACtB,GAAAA,EACA,KAAM,YACN,UAAW,KAAK,cAAiBe,EAAQ,GACzC,SAAAT,EACA,KAAMQ,EACN,GAAIA,EACJ,QAAS,EACT,SAAU,GACV,WAAYC,IAAUF,EAAU,OAAS,EAAIN,EAAa,MAAA,CAC3D,CACH,CAAC,EAGDM,EAAU,QAAQC,GAAO,CACvB,KAAK,kBAAkBA,EAAK,WAAW,CACzC,CAAC,CAGH,CAGO,kBAAkBE,EAAoBC,EAAoC,CAE/E,MAAMC,EAAUF,EAAS,IAAM,GAAW,GACpCG,EAAUH,EAAS,IAAM,GAAW,GAAW,EAE/CI,EAAgBH,IAAS,YAAc,EAAI,EAEjD,QAASI,EAAI,EAAGA,EAAID,EAAeC,IAAK,CACtC,MAAMC,EAAS,KAAK,GAAK,EAAID,EAAKD,EAC5BG,EAAQ,EAAI,KAAK,OAAA,EAAW,EAElC,KAAK,UAAU,KAAK,CAClB,GAAI,YAAY,KAAK,IAAA,CAAK,IAAIF,CAAC,GAC/B,EAAGH,EACH,EAAGC,EACH,GAAI,KAAK,IAAIG,CAAK,EAAIC,EACtB,GAAI,KAAK,IAAID,CAAK,EAAIC,EACtB,KAAM,IAAO,KAAK,OAAA,EAAW,IAC7B,QAAS,KACT,MAAO,KAAK,iBAAiBN,CAAI,EACjC,KAAM,EAAI,KAAK,OAAA,EAAW,EAC1B,KAAAA,CAAA,CACD,CACH,CACF,CAGQ,iBAAiBA,EAAsC,CAC7D,OAAQA,EAAA,CACN,IAAK,OACH,MAAO,UACT,IAAK,UACH,MAAO,UACT,IAAK,YACH,MAAO,UACT,QACE,MAAO,SAAA,CAEb,CAGO,qBAAqBjB,EAAoB,CAC9C,MAAMC,EAAY,KAAK,WAAW,IAAID,CAAE,EACxC,GAAI,CAACC,EAAW,MAAO,GAEvB,MAAMC,EAAU,KAAK,cAAgBD,EAAU,UAC/C,OAAO,KAAK,IAAIC,EAAUD,EAAU,SAAU,CAAC,CACjD,CAGO,wBAAwBA,EAAmC,CAChE,MAAMC,EAAU,KAAK,cAAgBD,EAAU,UACzCuB,EAAW,KAAK,IAAItB,EAAUD,EAAU,SAAU,CAAC,EAGnDwB,EAAgB,KAAK,eAAeD,CAAQ,EAElD,MAAO,CACL,IAAKvB,EAAU,KAAK,KAAOA,EAAU,GAAG,IAAMA,EAAU,KAAK,KAAOwB,EACpE,IAAKxB,EAAU,KAAK,KAAOA,EAAU,GAAG,IAAMA,EAAU,KAAK,KAAOwB,CAAA,CAExE,CAGQ,eAAe,EAAmB,CACxC,OAAO,EAAI,GAAM,EAAI,EAAI,EAAI,EAAI,EAAI,KAAK,IAAI,GAAK,EAAI,EAAG,CAAC,EAAI,CACjE,CAGO,qBAA+B,CACpC,OAAO,KAAK,WAAW,KAAO,CAChC,CAGO,qBAAsC,CAC3C,OAAO,MAAM,KAAK,KAAK,WAAW,QAAQ,CAC5C,CAGO,cAAiC,CACtC,OAAO,KAAK,SACd,CAGO,OAAc,CACnB,KAAK,WAAW,MAAA,EAChB,KAAK,UAAU,OAAS,EACxB,KAAK,KAAA,CACP,CACF"}