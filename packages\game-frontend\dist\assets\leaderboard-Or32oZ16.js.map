{"version": 3, "file": "leaderboard-Or32oZ16.js", "sources": ["../../src/types/leaderboard.ts"], "sourcesContent": ["export type LeaderboardType = \n  | 'global_score'      // 全球总分排行\n  | 'weekly_score'      // 周分数排行\n  | 'daily_score'       // 日分数排行\n  | 'level_completion'  // 关卡完成数排行\n  | 'combo_record'      // 最高连击排行\n  | 'speed_run'         // 速通排行\n  | 'achievement_count' // 成就数量排行\n\nexport interface LeaderboardEntry {\n  id: string\n  userId: string\n  username: string\n  avatar?: string\n  rank: number\n  score: number\n  value: number // 根据排行榜类型的具体数值\n  \n  // 额外信息\n  level?: number\n  combo?: number\n  time?: number // 秒\n  achievements?: number\n  \n  // 时间戳\n  createdAt: Date\n  updatedAt: Date\n  \n  // 用户状态\n  isOnline?: boolean\n  isFriend?: boolean\n  isCurrentUser?: boolean\n}\n\nexport interface LeaderboardData {\n  type: LeaderboardType\n  entries: LeaderboardEntry[]\n  totalEntries: number\n  currentUserRank?: number\n  currentUserEntry?: LeaderboardEntry\n  lastUpdated: Date\n}\n\nexport interface Friend {\n  id: string\n  userId: string\n  username: string\n  avatar?: string\n  level: number\n  totalScore: number\n  lastActive: Date\n  isOnline: boolean\n  \n  // 好友关系\n  friendshipId: string\n  friendedAt: Date\n  status: 'pending' | 'accepted' | 'blocked'\n  \n  // 最近活动\n  recentAchievements: Array<{\n    id: string\n    name: string\n    icon: string\n    unlockedAt: Date\n  }>\n  \n  // 游戏统计\n  stats: {\n    gamesPlayed: number\n    gamesWon: number\n    maxCombo: number\n    achievementsUnlocked: number\n  }\n}\n\nexport interface FriendRequest {\n  id: string\n  fromUserId: string\n  toUserId: string\n  fromUsername: string\n  fromAvatar?: string\n  message?: string\n  status: 'pending' | 'accepted' | 'rejected'\n  createdAt: Date\n  respondedAt?: Date\n}\n\nexport interface ShareData {\n  type: 'score' | 'achievement' | 'level_completion' | 'combo'\n  title: string\n  description: string\n  image?: string\n  url?: string\n  \n  // 具体数据\n  data: {\n    score?: number\n    level?: number\n    combo?: number\n    achievement?: {\n      id: string\n      name: string\n      icon: string\n    }\n  }\n}\n\n// 排行榜配置\nexport const LEADERBOARD_CONFIG = {\n  global_score: {\n    name: '全球总分',\n    icon: '🏆',\n    description: '所有玩家的总分排行',\n    valueLabel: '总分',\n    refreshInterval: 300000, // 5分钟\n    maxEntries: 100\n  },\n  weekly_score: {\n    name: '本周分数',\n    icon: '📅',\n    description: '本周获得分数排行',\n    valueLabel: '周分数',\n    refreshInterval: 60000, // 1分钟\n    maxEntries: 50\n  },\n  daily_score: {\n    name: '今日分数',\n    icon: '☀️',\n    description: '今日获得分数排行',\n    valueLabel: '日分数',\n    refreshInterval: 30000, // 30秒\n    maxEntries: 30\n  },\n  level_completion: {\n    name: '关卡完成',\n    icon: '🎯',\n    description: '完成关卡数量排行',\n    valueLabel: '关卡数',\n    refreshInterval: 300000,\n    maxEntries: 100\n  },\n  combo_record: {\n    name: '最高连击',\n    icon: '⚡',\n    description: '最高连击记录排行',\n    valueLabel: '连击数',\n    refreshInterval: 300000,\n    maxEntries: 50\n  },\n  speed_run: {\n    name: '速通记录',\n    icon: '🏃',\n    description: '关卡完成时间排行',\n    valueLabel: '时间',\n    refreshInterval: 300000,\n    maxEntries: 50\n  },\n  achievement_count: {\n    name: '成就大师',\n    icon: '🏅',\n    description: '解锁成就数量排行',\n    valueLabel: '成就数',\n    refreshInterval: 300000,\n    maxEntries: 100\n  }\n}\n\n// 分享平台配置\nexport const SHARE_PLATFORMS = {\n  wechat: {\n    name: '微信',\n    icon: '💬',\n    color: '#07c160',\n    available: true\n  },\n  weibo: {\n    name: '微博',\n    icon: '📱',\n    color: '#e6162d',\n    available: true\n  },\n  qq: {\n    name: 'QQ',\n    icon: '🐧',\n    color: '#12b7f5',\n    available: true\n  },\n  copy_link: {\n    name: '复制链接',\n    icon: '🔗',\n    color: '#6b7280',\n    available: true\n  }\n}\n\n// 好友活动类型\nexport type FriendActivityType = \n  | 'achievement_unlocked'\n  | 'level_completed'\n  | 'high_score'\n  | 'combo_record'\n  | 'friend_added'\n\nexport interface FriendActivity {\n  id: string\n  userId: string\n  username: string\n  avatar?: string\n  type: FriendActivityType\n  data: any\n  createdAt: Date\n  \n  // 活动描述\n  title: string\n  description: string\n  icon: string\n}\n\n// Mock数据生成器\nexport const generateMockLeaderboardData = (type: LeaderboardType): LeaderboardData => {\n  const config = LEADERBOARD_CONFIG[type]\n  const entries: LeaderboardEntry[] = []\n  \n  // 生成模拟数据\n  for (let i = 1; i <= Math.min(config.maxEntries, 20); i++) {\n    const baseScore = Math.floor(Math.random() * 100000) + 10000\n    \n    entries.push({\n      id: `entry_${i}`,\n      userId: `user_${i}`,\n      username: `玩家${i.toString().padStart(3, '0')}`,\n      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}`,\n      rank: i,\n      score: baseScore,\n      value: getValueByType(type, baseScore, i),\n      level: Math.floor(Math.random() * 50) + 1,\n      combo: Math.floor(Math.random() * 20) + 1,\n      time: Math.floor(Math.random() * 300) + 30,\n      achievements: Math.floor(Math.random() * 30) + 1,\n      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),\n      updatedAt: new Date(),\n      isOnline: Math.random() > 0.7,\n      isFriend: Math.random() > 0.8,\n      isCurrentUser: i === 5 // 假设当前用户排名第5\n    })\n  }\n  \n  return {\n    type,\n    entries,\n    totalEntries: config.maxEntries,\n    currentUserRank: 5,\n    currentUserEntry: entries[4], // 第5名\n    lastUpdated: new Date()\n  }\n}\n\nfunction getValueByType(type: LeaderboardType, baseScore: number, rank: number): number {\n  switch (type) {\n    case 'global_score':\n    case 'weekly_score':\n    case 'daily_score':\n      return baseScore\n    case 'level_completion':\n      return Math.max(1, 100 - rank * 2)\n    case 'combo_record':\n      return Math.max(1, 50 - rank)\n    case 'speed_run':\n      return 30 + rank * 5 // 秒数，越小越好\n    case 'achievement_count':\n      return Math.max(1, 50 - rank)\n    default:\n      return baseScore\n  }\n}\n\nexport const generateMockFriends = (): Friend[] => {\n  const friends: Friend[] = []\n  \n  for (let i = 1; i <= 10; i++) {\n    friends.push({\n      id: `friend_${i}`,\n      userId: `user_${i + 100}`,\n      username: `好友${i}`,\n      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=friend${i}`,\n      level: Math.floor(Math.random() * 30) + 1,\n      totalScore: Math.floor(Math.random() * 50000) + 5000,\n      lastActive: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),\n      isOnline: Math.random() > 0.6,\n      friendshipId: `friendship_${i}`,\n      friendedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),\n      status: 'accepted',\n      recentAchievements: [\n        {\n          id: `achievement_${i}_1`,\n          name: '连击高手',\n          icon: '💥',\n          unlockedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)\n        }\n      ],\n      stats: {\n        gamesPlayed: Math.floor(Math.random() * 100) + 10,\n        gamesWon: Math.floor(Math.random() * 50) + 5,\n        maxCombo: Math.floor(Math.random() * 20) + 1,\n        achievementsUnlocked: Math.floor(Math.random() * 20) + 1\n      }\n    })\n  }\n  \n  return friends\n}\n"], "names": ["LEADERBOARD_CONFIG", "SHARE_PLATFORMS", "generateMockLeaderboardData", "type", "config", "entries", "i", "baseScore", "getValueByType", "rank", "generateMockFriends", "friends"], "mappings": "AA4GO,MAAMA,EAAqB,CAChC,aAAc,CACZ,KAAM,OACN,KAAM,KACN,YAAa,YACb,WAAY,KACZ,gBAAiB,IACjB,WAAY,GAAA,EAEd,aAAc,CACZ,KAAM,OACN,KAAM,KACN,YAAa,WACb,WAAY,MACZ,gBAAiB,IACjB,WAAY,EAAA,EAEd,YAAa,CACX,KAAM,OACN,KAAM,KACN,YAAa,WACb,WAAY,MACZ,gBAAiB,IACjB,WAAY,EAAA,EAEd,iBAAkB,CAChB,KAAM,OACN,KAAM,KACN,YAAa,WACb,WAAY,MACZ,gBAAiB,IACjB,WAAY,GAAA,EAEd,aAAc,CACZ,KAAM,OACN,KAAM,IACN,YAAa,WACb,WAAY,MACZ,gBAAiB,IACjB,WAAY,EAAA,EAEd,UAAW,CACT,KAAM,OACN,KAAM,KACN,YAAa,WACb,WAAY,KACZ,gBAAiB,IACjB,WAAY,EAAA,EAEd,kBAAmB,CACjB,KAAM,OACN,KAAM,KACN,YAAa,WACb,WAAY,MACZ,gBAAiB,IACjB,WAAY,GAAA,CAEhB,EAGaC,EAAkB,CAC7B,OAAQ,CACN,KAAM,KACN,KAAM,KACN,MAAO,UACP,UAAW,EAAA,EAEb,MAAO,CACL,KAAM,KACN,KAAM,KACN,MAAO,UACP,UAAW,EAAA,EAEb,GAAI,CACF,KAAM,KACN,KAAM,KACN,MAAO,UACP,UAAW,EAAA,EAEb,UAAW,CACT,KAAM,OACN,KAAM,KACN,MAAO,UACP,UAAW,EAAA,CAEf,EA0BaC,EAA+BC,GAA2C,CACrF,MAAMC,EAASJ,EAAmBG,CAAI,EAChCE,EAA8B,CAAA,EAGpC,QAASC,EAAI,EAAGA,GAAK,KAAK,IAAIF,EAAO,WAAY,EAAE,EAAGE,IAAK,CACzD,MAAMC,EAAY,KAAK,MAAM,KAAK,OAAA,EAAW,GAAM,EAAI,IAEvDF,EAAQ,KAAK,CACX,GAAI,SAASC,CAAC,GACd,OAAQ,QAAQA,CAAC,GACjB,SAAU,KAAKA,EAAE,SAAA,EAAW,SAAS,EAAG,GAAG,CAAC,GAC5C,OAAQ,mDAAmDA,CAAC,GAC5D,KAAMA,EACN,MAAOC,EACP,MAAOC,EAAeL,EAAMI,EAAWD,CAAC,EACxC,MAAO,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EACxC,MAAO,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EACxC,KAAM,KAAK,MAAM,KAAK,OAAA,EAAW,GAAG,EAAI,GACxC,aAAc,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EAC/C,UAAW,IAAI,KAAK,KAAK,IAAA,EAAQ,KAAK,OAAA,EAAW,EAAI,GAAK,GAAK,GAAK,GAAI,EACxE,cAAe,KACf,SAAU,KAAK,OAAA,EAAW,GAC1B,SAAU,KAAK,OAAA,EAAW,GAC1B,cAAeA,IAAM,CAAA,CACtB,CACH,CAEA,MAAO,CACL,KAAAH,EACA,QAAAE,EACA,aAAcD,EAAO,WACrB,gBAAiB,EACjB,iBAAkBC,EAAQ,CAAC,EAC3B,gBAAiB,IAAK,CAE1B,EAEA,SAASG,EAAeL,EAAuBI,EAAmBE,EAAsB,CACtF,OAAQN,EAAA,CACN,IAAK,eACL,IAAK,eACL,IAAK,cACH,OAAOI,EACT,IAAK,mBACH,OAAO,KAAK,IAAI,EAAG,IAAME,EAAO,CAAC,EACnC,IAAK,eACH,OAAO,KAAK,IAAI,EAAG,GAAKA,CAAI,EAC9B,IAAK,YACH,MAAO,IAAKA,EAAO,EACrB,IAAK,oBACH,OAAO,KAAK,IAAI,EAAG,GAAKA,CAAI,EAC9B,QACE,OAAOF,CAAA,CAEb,CAEO,MAAMG,EAAsB,IAAgB,CACjD,MAAMC,EAAoB,CAAA,EAE1B,QAASL,EAAI,EAAGA,GAAK,GAAIA,IACvBK,EAAQ,KAAK,CACX,GAAI,UAAUL,CAAC,GACf,OAAQ,QAAQA,EAAI,GAAG,GACvB,SAAU,KAAKA,CAAC,GAChB,OAAQ,yDAAyDA,CAAC,GAClE,MAAO,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EACxC,WAAY,KAAK,MAAM,KAAK,OAAA,EAAW,GAAK,EAAI,IAChD,WAAY,IAAI,KAAK,KAAK,IAAA,EAAQ,KAAK,OAAA,EAAW,GAAK,GAAK,GAAK,GAAI,EACrE,SAAU,KAAK,OAAA,EAAW,GAC1B,aAAc,cAAcA,CAAC,GAC7B,WAAY,IAAI,KAAK,KAAK,IAAA,EAAQ,KAAK,OAAA,EAAW,GAAK,GAAK,GAAK,GAAK,GAAI,EAC1E,OAAQ,WACR,mBAAoB,CAClB,CACE,GAAI,eAAeA,CAAC,KACpB,KAAM,OACN,KAAM,KACN,WAAY,IAAI,KAAK,KAAK,IAAA,EAAQ,KAAK,OAAA,EAAW,EAAI,GAAK,GAAK,GAAK,GAAI,CAAA,CAC3E,EAEF,MAAO,CACL,YAAa,KAAK,MAAM,KAAK,OAAA,EAAW,GAAG,EAAI,GAC/C,SAAU,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EAC3C,SAAU,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,EAC3C,qBAAsB,KAAK,MAAM,KAAK,OAAA,EAAW,EAAE,EAAI,CAAA,CACzD,CACD,EAGH,OAAOK,CACT"}