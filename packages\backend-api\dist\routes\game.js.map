{"version": 3, "file": "game.js", "sourceRoot": "", "sources": ["../../src/routes/game.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAA2C;AAC3C,8CAAsB;AACtB,+BAAoC;AACpC,mDAAgD;AAChD,mDAA0D;AAC1D,6DAAuE;AACvE,6CAAoE;AAEpE,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AA6SL,4BAAU;AA5S7B,MAAM,EAAE,GAAG,mBAAQ,CAAC,WAAW,EAAE,CAAC;AAGlC,MAAM,YAAY,GAAG,IAAI,GAAG,EASxB,CAAC;AAGL,MAAM,CAAC,GAAG,CAAC,wBAAiB,CAAC,CAAC;AAG9B,MAAM,eAAe,GAAG,aAAG,CAAC,MAAM,CAAC;IACjC,WAAW,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;CACtD,CAAC,CAAC;AAEH,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,EAAE;IAC3E,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5D,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAC5B,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;IAG9B,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CACxB,+DAA+D,EAC/D,CAAC,WAAW,CAAC,CACd,CAAC;IAEF,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAGD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,GAAG,CAC/B,+EAA+E,EAC/E,CAAC,MAAM,EAAE,WAAW,GAAG,CAAC,CAAC,CAC1B,CAAC;QAEF,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,CAAC;YAChC,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAGD,MAAM,MAAM,GAAG,IAAI,uBAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;IAG3B,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE;QAC1B,MAAM;QACN,WAAW;QACX,MAAM;QACN,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,WAAW,EAAE,KAAK,CAAC,YAAY;QAC/B,UAAU,EAAE,KAAK,CAAC,WAAW;QAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;KACtB,CAAC,CAAC;IAGH,MAAM,EAAE,CAAC,GAAG,CACV;;qDAEiD,EACjD,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC;YAC9C,KAAK,EAAE,MAAM,CAAC,QAAQ,EAAE;YACxB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,WAAW,EAAE,KAAK,CAAC,YAAY;YAC/B,UAAU,EAAE,KAAK,CAAC,WAAW;SAC9B,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACV,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,SAAS;YACT,KAAK,EAAE,MAAM,CAAC,QAAQ,EAAE;YACxB,KAAK,EAAE;gBACL,WAAW;gBACX,SAAS,EAAE,KAAK,CAAC,UAAU;gBAC3B,QAAQ,EAAE,KAAK,CAAC,SAAS;gBACzB,WAAW,EAAE,KAAK,CAAC,YAAY;gBAC/B,UAAU,EAAE,KAAK,CAAC,WAAW;gBAC7B,UAAU,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI;aACrE;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,UAAU,EAAE,KAAK;gBACjB,KAAK,EAAE,KAAK;aACb;SACF;QACD,OAAO,EAAE,MAAM;KAChB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,UAAU,GAAG,aAAG,CAAC,MAAM,CAAC;IAC5B,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACzC,IAAI,EAAE,aAAG,CAAC,MAAM,CAAC;QACf,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC3C,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;KAC5C,CAAC,CAAC,QAAQ,EAAE;IACb,EAAE,EAAE,aAAG,CAAC,MAAM,CAAC;QACb,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC3C,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;KAC5C,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,EAAE;IAC1E,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAC5B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;IAGtC,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC5C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1C,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAGD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAgB,EAAE,EAAc,CAAC,CAAC;IAC9E,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAGD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IACtD,OAAO,CAAC,KAAK,IAAI,aAAa,CAAC,UAAU,CAAC;IAC1C,OAAO,CAAC,SAAS,EAAE,CAAC;IAGpB,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,KAAK,GAAG,KAAK,CAAC;IAElB,IAAI,OAAO,CAAC,UAAU,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3E,KAAK,GAAG,IAAI,CAAC;QACb,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC;SAAM,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;QAClC,UAAU,GAAG,IAAI,CAAC;QAClB,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,CAAC;IAC/C,CAAC;IAGD,MAAM,EAAE,CAAC,GAAG,CACV;;kBAEc,EACd,CAAC,IAAI,CAAC,SAAS,CAAC;YACd,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC,EAAE,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CACvE,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,WAAW,EAAE,aAAa,CAAC,UAAU;YACrC,UAAU;YACV,KAAK;YACL,SAAS,EAAE;gBACT,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B;SACF;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,aAAa,GAAG,aAAG,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACzC,QAAQ,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACjC,QAAQ,EAAE,aAAG,CAAC,MAAM,CAAC;QACnB,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;QAC3C,CAAC,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;KAC5C,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEH,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,EAAE;IAC9E,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAC5B,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;IAGhD,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC5C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1C,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAGD,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,GAAG,CAC5B,yEAAyE,EACzE,CAAC,MAAM,EAAE,QAAQ,CAAC,CACnB,CAAC;IAEF,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;QAC1C,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAGD,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CACvB,2DAA2D,EAC3D,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,WAAW,GAAG,CAAC,CAAC;IAGpB,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;QACxB,KAAK,gBAAgB;YACnB,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACtD,WAAW,GAAG,EAAE,CAAC;YACnB,CAAC;YACD,MAAM;QAER,KAAK,WAAW;YACd,OAAO,CAAC,SAAS,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,CAAC;YAC3C,MAAM;QAER,KAAK,aAAa;YAChB,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtE,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACxC,IAAI,OAAO,GAAG,CAAC,CAAC;gBAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACzC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;4BAChC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;4BAChB,OAAO,EAAE,CAAC;wBACZ,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC/B,WAAW,GAAG,OAAO,GAAG,EAAE,CAAC;YAC7B,CAAC;YACD,MAAM;IACV,CAAC;IAGD,MAAM,EAAE,CAAC,GAAG,CACV,uFAAuF,EACvF,CAAC,MAAM,EAAE,QAAQ,CAAC,CACnB,CAAC;IAGF,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IAC9B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IAG3B,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IACtD,OAAO,CAAC,KAAK,IAAI,aAAa,CAAC,UAAU,GAAG,WAAW,CAAC;IAExD,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,aAAa,CAAC,UAAU,GAAG,WAAW;YACnD,OAAO,EAAE,aAAa,CAAC,OAAO;SAC/B;QACD,OAAO,EAAE,QAAQ;KAClB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC"}