import React from 'react'
import styled, { css } from 'styled-components'
import { theme, mixins } from '../../styles/theme'

export type CardVariant = 'default' | 'elevated' | 'outlined' | 'glass'
export type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant
  padding?: CardPadding
  hoverable?: boolean
  children: React.ReactNode
}

const getVariantStyles = (variant: CardVariant) => {
  switch (variant) {
    case 'default':
      return css`
        ${mixins.glassmorphism}
        box-shadow: ${theme.shadows.md};
      `
    
    case 'elevated':
      return css`
        background: ${theme.colors.background.card};
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: ${theme.shadows.xl};
      `
    
    case 'outlined':
      return css`
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: none;
      `
    
    case 'glass':
      return css`
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: ${theme.shadows.inner}, ${theme.shadows.lg};
      `
    
    default:
      return css``
  }
}

const getPaddingStyles = (padding: CardPadding) => {
  switch (padding) {
    case 'none':
      return css`padding: 0;`
    
    case 'sm':
      return css`padding: ${theme.spacing[3]};`
    
    case 'md':
      return css`padding: ${theme.spacing[4]};`
    
    case 'lg':
      return css`padding: ${theme.spacing[6]};`
    
    case 'xl':
      return css`padding: ${theme.spacing[8]};`
    
    default:
      return css`padding: ${theme.spacing[4]};`
  }
}

const StyledCard = styled.div<{
  $variant: CardVariant
  $padding: CardPadding
  $hoverable: boolean
}>`
  ${props => getVariantStyles(props.$variant)}
  ${props => getPaddingStyles(props.$padding)}
  
  border-radius: ${theme.borderRadius.xl};
  transition: all ${theme.transitions.base} ease-in-out;
  position: relative;
  overflow: hidden;
  
  ${props => props.$hoverable && css`
    cursor: pointer;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: ${theme.shadows['2xl']};
      
      ${props.$variant === 'default' && css`
        background: ${theme.colors.background.cardHover};
      `}
      
      ${props.$variant === 'elevated' && css`
        box-shadow: ${theme.shadows['2xl']}, ${theme.shadows.glowGold};
      `}
      
      ${props.$variant === 'outlined' && css`
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.05);
      `}
      
      ${props.$variant === 'glass' && css`
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
      `}
    }
    
    &:active {
      transform: translateY(-2px);
    }
  `}
  
  /* 添加微妙的光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0.5;
  }
  
  /* 添加边缘高光 */
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border-radius: ${theme.borderRadius.lg};
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.6;
  }
`

const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  hoverable = false,
  children,
  ...props
}) => {
  return (
    <StyledCard
      $variant={variant}
      $padding={padding}
      $hoverable={hoverable}
      {...props}
    >
      {children}
    </StyledCard>
  )
}

export default Card
export { Card }

// 预定义的卡片组件
export const GameCard = styled(Card)`
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(251, 191, 36, 0.2);
  box-shadow: ${theme.shadows.lg}, 0 0 30px rgba(251, 191, 36, 0.1);
  
  &:hover {
    border-color: rgba(251, 191, 36, 0.4);
    box-shadow: ${theme.shadows.xl}, 0 0 40px rgba(251, 191, 36, 0.2);
  }
`

export const LevelCard = styled(Card)`
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.1) 0%, 
    rgba(139, 92, 246, 0.1) 100%
  );
  border: 1px solid rgba(99, 102, 241, 0.2);
  
  &:hover {
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: ${theme.shadows.xl}, ${theme.shadows.glowPurple};
  }
`

export const StatsCard = styled(Card)`
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1) 0%, 
    rgba(5, 150, 105, 0.1) 100%
  );
  border: 1px solid rgba(16, 185, 129, 0.2);
  
  &:hover {
    border-color: rgba(16, 185, 129, 0.4);
    box-shadow: ${theme.shadows.xl}, 0 0 30px rgba(16, 185, 129, 0.2);
  }
`
