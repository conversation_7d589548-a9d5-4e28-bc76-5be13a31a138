import React from 'react'
import styled, { css } from 'styled-components'
import { theme, mixins } from '../../styles/theme'

export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost'
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant
  size?: ButtonSize
  fullWidth?: boolean
  loading?: boolean
  icon?: React.ReactNode
  children: React.ReactNode
}

const getVariantStyles = (variant: ButtonVariant) => {
  switch (variant) {
    case 'primary':
      return css`
        background: linear-gradient(135deg, ${theme.colors.primary[500]} 0%, ${theme.colors.primary[600]} 100%);
        color: white;
        box-shadow: ${theme.shadows.glowGold};
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, ${theme.colors.primary[400]} 0%, ${theme.colors.primary[500]} 100%);
          box-shadow: ${theme.shadows.glowGold}, ${theme.shadows.lg};
        }
      `
    
    case 'secondary':
      return css`
        background: linear-gradient(135deg, ${theme.colors.secondary[400]} 0%, ${theme.colors.secondary[500]} 100%);
        color: ${theme.colors.gray[900]};
        box-shadow: ${theme.shadows.md};
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, ${theme.colors.secondary[300]} 0%, ${theme.colors.secondary[400]} 100%);
          box-shadow: ${theme.shadows.lg};
        }
      `
    
    case 'success':
      return css`
        background: linear-gradient(135deg, ${theme.colors.success} 0%, #059669 100%);
        color: white;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #10b981 0%, ${theme.colors.success} 100%);
          box-shadow: 0 0 25px rgba(16, 185, 129, 0.4), ${theme.shadows.lg};
        }
      `
    
    case 'warning':
      return css`
        background: linear-gradient(135deg, ${theme.colors.warning} 0%, #d97706 100%);
        color: white;
        box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #fbbf24 0%, ${theme.colors.warning} 100%);
          box-shadow: 0 0 25px rgba(245, 158, 11, 0.4), ${theme.shadows.lg};
        }
      `
    
    case 'error':
      return css`
        background: linear-gradient(135deg, ${theme.colors.error} 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #f87171 0%, ${theme.colors.error} 100%);
          box-shadow: 0 0 25px rgba(239, 68, 68, 0.4), ${theme.shadows.lg};
        }
      `
    
    case 'ghost':
      return css`
        background: transparent;
        color: ${theme.colors.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.2);
        
        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.3);
        }
      `
    
    default:
      return css``
  }
}

const getSizeStyles = (size: ButtonSize) => {
  switch (size) {
    case 'sm':
      return css`
        padding: ${theme.spacing[2]} ${theme.spacing[3]};
        font-size: ${theme.fontSizes.sm};
        min-height: 32px;
      `
    
    case 'md':
      return css`
        padding: ${theme.spacing[3]} ${theme.spacing[4]};
        font-size: ${theme.fontSizes.base};
        min-height: 40px;
      `
    
    case 'lg':
      return css`
        padding: ${theme.spacing[4]} ${theme.spacing[6]};
        font-size: ${theme.fontSizes.lg};
        min-height: 48px;
      `
    
    case 'xl':
      return css`
        padding: ${theme.spacing[5]} ${theme.spacing[8]};
        font-size: ${theme.fontSizes.xl};
        min-height: 56px;
      `
    
    default:
      return css``
  }
}

const StyledButton = styled.button<{
  $variant: ButtonVariant
  $size: ButtonSize
  $fullWidth: boolean
  $loading: boolean
}>`
  ${mixins.buttonBase}
  ${props => getVariantStyles(props.$variant)}
  ${props => getSizeStyles(props.$size)}
  
  ${props => props.$fullWidth && css`
    width: 100%;
  `}
  
  ${props => props.$loading && css`
    pointer-events: none;
    opacity: 0.7;
  `}
  
  gap: ${theme.spacing[2]};
  font-family: ${theme.fonts.primary};
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover::before {
    left: 100%;
  }
`

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`

const IconWrapper = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
`

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  icon,
  children,
  disabled,
  ...props
}) => {
  return (
    <StyledButton
      $variant={variant}
      $size={size}
      $fullWidth={fullWidth}
      $loading={loading}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {!loading && icon && <IconWrapper>{icon}</IconWrapper>}
      {children}
    </StyledButton>
  )
}

export default Button
export { Button }
