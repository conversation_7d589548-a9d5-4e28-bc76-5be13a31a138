export declare enum GemType {
    RED = 1,
    BLUE = 2,
    GREEN = 3,
    YELLOW = 4,
    PURPLE = 5,
    ORANGE = 6,
    EMPTY = 0,
    OBSTACLE = -1
}
export interface Position {
    x: number;
    y: number;
}
export interface Match {
    gems: Position[];
    type: 'horizontal' | 'vertical' | 'L' | 'T';
    score: number;
}
export interface GameBoard {
    board: number[][];
    size: number;
}
export declare class GameEngine {
    private board;
    private size;
    constructor(size?: number);
    private generateBoard;
    private wouldCreateMatch;
    swapGems(from: Position, to: Position): boolean;
    private areAdjacent;
    findMatches(): Match[];
    private calculateScore;
    removeMatches(matches: Match[]): void;
    applyGravity(): void;
    fillEmpty(): void;
    processCascade(): {
        matches: Match[];
        totalScore: number;
    };
    getBoard(): number[][];
    setBoard(board: number[][]): void;
    hasPossibleMoves(): boolean;
}
//# sourceMappingURL=gameEngine.d.ts.map