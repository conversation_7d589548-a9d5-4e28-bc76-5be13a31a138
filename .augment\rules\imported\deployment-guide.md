---
type: "agent_requested"
description: "Example description"
---
# 部署指南

## 开发环境搭建

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- Git

### 快速启动

#### Windows
```bash
# 运行启动脚本
start-dev.bat
```

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x start-dev.sh

# 运行启动脚本
./start-dev.sh
```

#### 手动启动
```bash
# 1. 安装所有依赖
npm run install:all

# 2. 启动所有服务
npm run dev
```

### 服务地址
启动成功后，可以访问以下地址：

- **游戏前端**: http://localhost:3000
- **后端API**: http://localhost:3001
- **管理后台**: http://localhost:3002
- **API文档**: http://localhost:3001/health

## 生产环境部署

### 1. 构建项目
```bash
# 构建所有项目
npm run build

# 或分别构建
npm run build:frontend
npm run build:backend
npm run build:admin
```

### 2. 环境变量配置

#### 后端环境变量 (.env)
```env
# 生产环境配置
NODE_ENV=production
PORT=3001

# 数据库配置
DATABASE_PATH=/app/data/starry_match.db

# JWT配置 (请更换为安全的密钥)
JWT_SECRET=your_production_jwt_secret_here
JWT_EXPIRES_IN=7d

# CORS配置
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com

# 其他配置...
```

### 3. Docker部署

#### Dockerfile示例
```dockerfile
# 后端Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY packages/backend-api/package*.json ./
RUN npm ci --only=production

# 复制源码
COPY packages/backend-api/dist ./dist
COPY packages/backend-api/.env ./

# 创建数据目录
RUN mkdir -p /app/data

EXPOSE 3001

CMD ["npm", "start"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  backend:
    build: 
      context: .
      dockerfile: packages/backend-api/Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - ./data:/app/data
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/starry_match.db
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: packages/game-frontend/Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

  admin:
    build:
      context: .
      dockerfile: packages/admin-panel/Dockerfile
    ports:
      - "3002:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
      - admin
    restart: unless-stopped
```

### 4. Nginx配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # 游戏前端
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API接口
    location /api/ {
        proxy_pass http://backend:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# 管理后台
server {
    listen 443 ssl http2;
    server_name admin.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location / {
        proxy_pass http://admin:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 5. 数据库备份

```bash
# 备份数据库
cp /app/data/starry_match.db /backup/starry_match_$(date +%Y%m%d_%H%M%S).db

# 定时备份 (crontab)
0 2 * * * cp /app/data/starry_match.db /backup/starry_match_$(date +\%Y\%m\%d_\%H\%M\%S).db
```

### 6. 监控和日志

#### PM2部署 (Node.js进程管理)
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs
```

#### ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'starry-match-backend',
    script: './packages/backend-api/dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

## 性能优化

### 1. 前端优化
- 启用Gzip压缩
- 配置CDN
- 图片懒加载
- 代码分割

### 2. 后端优化
- 数据库索引优化
- API响应缓存
- 连接池配置
- 负载均衡

### 3. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_user_progress_performance ON user_progress(user_id, level_number, is_completed);
CREATE INDEX idx_game_sessions_active ON game_sessions(user_id, is_active, updated_at);

-- 定期清理过期会话
DELETE FROM game_sessions WHERE updated_at < datetime('now', '-1 day') AND is_active = 0;
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :3001
   
   # 杀死进程
   taskkill /PID <PID> /F
   ```

2. **数据库连接失败**
   - 检查数据库文件路径
   - 确认目录权限
   - 查看错误日志

3. **CORS错误**
   - 检查环境变量CORS_ORIGIN配置
   - 确认前端请求地址正确

4. **JWT认证失败**
   - 检查JWT_SECRET配置
   - 确认token格式正确

### 日志查看
```bash
# 查看应用日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/err.log

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```
