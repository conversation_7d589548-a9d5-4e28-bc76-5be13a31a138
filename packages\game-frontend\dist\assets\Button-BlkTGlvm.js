import{j as s,d as p,t as r}from"./index-CNCEp3EQ.js";import{l as o,d as n}from"./ui-ldAE8JkK.js";const b=e=>{switch(e){case"primary":return o`
        background: linear-gradient(135deg, ${r.colors.primary[500]} 0%, ${r.colors.primary[600]} 100%);
        color: white;
        box-shadow: ${r.shadows.glowGold};
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, ${r.colors.primary[400]} 0%, ${r.colors.primary[500]} 100%);
          box-shadow: ${r.shadows.glowGold}, ${r.shadows.lg};
        }
      `;case"secondary":return o`
        background: linear-gradient(135deg, ${r.colors.secondary[400]} 0%, ${r.colors.secondary[500]} 100%);
        color: ${r.colors.gray[900]};
        box-shadow: ${r.shadows.md};
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, ${r.colors.secondary[300]} 0%, ${r.colors.secondary[400]} 100%);
          box-shadow: ${r.shadows.lg};
        }
      `;case"success":return o`
        background: linear-gradient(135deg, ${r.colors.success} 0%, #059669 100%);
        color: white;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #10b981 0%, ${r.colors.success} 100%);
          box-shadow: 0 0 25px rgba(16, 185, 129, 0.4), ${r.shadows.lg};
        }
      `;case"warning":return o`
        background: linear-gradient(135deg, ${r.colors.warning} 0%, #d97706 100%);
        color: white;
        box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #fbbf24 0%, ${r.colors.warning} 100%);
          box-shadow: 0 0 25px rgba(245, 158, 11, 0.4), ${r.shadows.lg};
        }
      `;case"error":return o`
        background: linear-gradient(135deg, ${r.colors.error} 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #f87171 0%, ${r.colors.error} 100%);
          box-shadow: 0 0 25px rgba(239, 68, 68, 0.4), ${r.shadows.lg};
        }
      `;case"ghost":return o`
        background: transparent;
        color: ${r.colors.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.2);
        
        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.3);
        }
      `;default:return o``}},h=e=>{switch(e){case"sm":return o`
        padding: ${r.spacing[2]} ${r.spacing[3]};
        font-size: ${r.fontSizes.sm};
        min-height: 32px;
      `;case"md":return o`
        padding: ${r.spacing[3]} ${r.spacing[4]};
        font-size: ${r.fontSizes.base};
        min-height: 40px;
      `;case"lg":return o`
        padding: ${r.spacing[4]} ${r.spacing[6]};
        font-size: ${r.fontSizes.lg};
        min-height: 48px;
      `;case"xl":return o`
        padding: ${r.spacing[5]} ${r.spacing[8]};
        font-size: ${r.fontSizes.xl};
        min-height: 56px;
      `;default:return o``}},$=n.button`
  ${p.buttonBase}
  ${e=>b(e.$variant)}
  ${e=>h(e.$size)}
  
  ${e=>e.$fullWidth&&o`
    width: 100%;
  `}
  
  ${e=>e.$loading&&o`
    pointer-events: none;
    opacity: 0.7;
  `}
  
  gap: ${r.spacing[2]};
  font-family: ${r.fonts.primary};
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover::before {
    left: 100%;
  }
`,u=n.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`,x=n.span`
  display: flex;
  align-items: center;
  justify-content: center;
`,m=({variant:e="primary",size:i="md",fullWidth:d=!1,loading:a=!1,icon:t,children:g,disabled:c,...l})=>s.jsxs($,{$variant:e,$size:i,$fullWidth:d,$loading:a,disabled:c||a,...l,children:[a&&s.jsx(u,{}),!a&&t&&s.jsx(x,{children:t}),g]});export{m as B};
//# sourceMappingURL=Button-BlkTGlvm.js.map
