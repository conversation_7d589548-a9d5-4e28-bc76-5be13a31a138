import{j as t,t as r,m as d,u as E}from"./index-CNCEp3EQ.js";import{r as a}from"./vendor-Dneogk0_.js";import{d as o,l as P,m as v}from"./ui-ldAE8JkK.js";import{b as S}from"./router-DMCr7QLp.js";import z from"./GameBoard-DvXOxAln.js";import I from"./GameOverModal-4jLpsjyZ.js";import M from"./PowerUpPanel-Cy3BrM2g.js";import{P as q}from"./powerups-DNw9s1Qv.js";import{PowerUpManager as C}from"./powerUpManager-DAya6dWG.js";import"./animationManager-DTsUvCq5.js";import"./specialGems-xbtj_zef.js";import"./Button-BlkTGlvm.js";import"./Card-B65VmGcU.js";const T=v`
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
`,k=v`
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`,G=o.div`
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: ${r.zIndex.fixed};
  display: flex;
  flex-direction: column;
  gap: ${r.spacing[2]};
  pointer-events: none;
  
  ${d.maxMd} {
    top: 10px;
    left: 10px;
  }
`,_=o.div`
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${r.borderRadius.lg};
  padding: ${r.spacing[3]};
  display: flex;
  align-items: center;
  gap: ${r.spacing[2]};
  min-width: 200px;
  box-shadow: ${r.shadows.lg};
  animation: ${k} 0.3s ease-out;
  
  ${e=>e.$isExpiring&&P`
    animation: ${T} 0.5s ease-in-out infinite;
  `}
  
  ${d.maxMd} {
    padding: ${r.spacing[2]};
    min-width: 160px;
  }
`,R=o.div`
  font-size: 24px;
  flex-shrink: 0;
  
  ${d.maxMd} {
    font-size: 20px;
  }
`,L=o.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${r.spacing[1]};
`,A=o.div`
  color: ${r.colors.text.primary};
  font-size: ${r.fontSizes.sm};
  font-weight: ${r.fontWeights.semibold};
  
  ${d.maxMd} {
    font-size: ${r.fontSizes.xs};
  }
`,b=o.div`
  color: ${r.colors.text.secondary};
  font-size: ${r.fontSizes.xs};
  font-family: ${r.fonts.mono};
  
  ${d.maxMd} {
    font-size: 10px;
  }
`,B=o.div`
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: ${r.borderRadius.full};
  overflow: hidden;
`,D=o.div`
  height: 100%;
  background: ${e=>e.$color};
  width: ${e=>e.$progress}%;
  transition: width 0.1s linear;
  border-radius: ${r.borderRadius.full};
  box-shadow: 0 0 10px ${e=>e.$color}40;
`,F=e=>{switch(e){case"score_boost":return r.colors.secondary[400];case"freeze":return"#60a5fa";case"time_extend":return r.colors.success;default:return r.colors.primary[400]}},N=e=>{if(e<60)return`${e}s`;const l=Math.floor(e/60),c=e%60;return`${l}:${c.toString().padStart(2,"0")}`},O=({effects:e})=>{const[l,c]=a.useState({});return a.useEffect(()=>{const s=setInterval(()=>{const p=Date.now(),i={};e.forEach(n=>{if(n.duration){const u=p-n.startTime,m=Math.max(0,n.duration-u);i[n.type]=Math.ceil(m/1e3)}}),c(i)},100);return()=>clearInterval(s)},[e]),e.length===0?null:t.jsx(G,{children:e.map(s=>{const p=q[s.type],i=l[s.type]||0,n=i<=5&&i>0,u=s.duration?(s.duration-(Date.now()-s.startTime))/s.duration*100:100,m=F(s.type);return t.jsxs(_,{$isExpiring:n,children:[t.jsx(R,{children:p.icon}),t.jsxs(L,{children:[t.jsx(A,{children:p.name}),s.duration&&t.jsxs(t.Fragment,{children:[t.jsx(b,{children:N(i)}),t.jsx(B,{children:t.jsx(D,{$progress:Math.max(0,u),$color:m})})]}),s.multiplier&&t.jsxs(b,{children:["倍数: ",s.multiplier,"x"]})]})]},s.type)})})},$=o.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  min-height: 100vh;
`,y=o.h1`
  color: #fbbf24;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-align: center;
  text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
`,W=o.div`
  width: 100%;
  max-width: 1200px;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: ${r.spacing[6]};
  align-items: start;

  ${d.maxLg} {
    grid-template-columns: 1fr;
    max-width: 800px;
    gap: ${r.spacing[4]};
  }
`,X=o.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${r.spacing[4]};
`,V=o.div`
  width: 250px;
  display: flex;
  flex-direction: column;
  gap: ${r.spacing[4]};

  ${d.maxLg} {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
`,H=o.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);

  h2 {
    color: #fbbf24;
    margin-bottom: 1rem;
  }
`,de=()=>{const{gameState:e,startGame:l}=E(),[c]=S(),[s]=a.useState(()=>new C),[p,i]=a.useState(null),[n,u]=a.useState([]),[m,j]=a.useState([{powerUpId:"bomb",type:"bomb",quantity:3},{powerUpId:"rainbow",type:"rainbow",quantity:2},{powerUpId:"hammer",type:"hammer",quantity:5},{powerUpId:"shuffle",type:"shuffle",quantity:1},{powerUpId:"time_extend",type:"time_extend",quantity:2},{powerUpId:"score_boost",type:"score_boost",quantity:1},{powerUpId:"hint",type:"hint",quantity:10},{powerUpId:"freeze",type:"freeze",quantity:1}]);a.useEffect(()=>{const x=c.get("level"),f=x?parseInt(x,10):1;e.status==="idle"&&l(f)},[e.status,l,c]),a.useEffect(()=>(s.onEffectChange=u,()=>{s.onEffectChange=void 0}),[s]);const U=x=>{const f=m.find(w=>w.type===x);if(!f||f.quantity<=0)return;j(w=>w.map(g=>g.type===x?{...g,quantity:g.quantity-1}:g));const h=s.usePowerUp({type:x,board:[],gameState:e});h.success&&(console.log(h.message),h.effects),i(null)};return e.status==="idle"||e.status==="loading"?t.jsxs($,{children:[t.jsx(y,{children:"星光对对碰"}),t.jsx(H,{children:t.jsx("h2",{children:"🎮 正在加载游戏..."})})]}):t.jsxs($,{children:[t.jsx(y,{children:"星光对对碰"}),t.jsxs(W,{children:[t.jsx(X,{children:t.jsx(z,{size:6})}),t.jsx(V,{children:t.jsx(M,{powerUps:m,onUsePowerUp:U,selectedPowerUp:p,onSelectPowerUp:i,disabled:e.status!=="playing"})})]}),t.jsx(O,{effects:n}),t.jsx(I,{isVisible:e.status==="won"||e.status==="lost"})]})};export{de as default};
//# sourceMappingURL=GamePage-h55HzyeI.js.map
