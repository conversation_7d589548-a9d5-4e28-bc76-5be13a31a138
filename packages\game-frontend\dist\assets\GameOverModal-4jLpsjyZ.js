import{u as h,c as v,j as e}from"./index-CNCEp3EQ.js";import{r as j}from"./vendor-Dneogk0_.js";import{d as a}from"./ui-ldAE8JkK.js";import{c as w}from"./router-DMCr7QLp.js";const y=a.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`,k=a.div`
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
  width: 90%;
  border: 2px solid #fbbf24;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
`,S=a.h2`
  color: ${r=>r.isWin?"#10b981":"#ef4444"};
  font-size: 2rem;
  margin-bottom: 1rem;
  text-shadow: 0 0 20px ${r=>r.isWin?"rgba(16, 185, 129, 0.5)":"rgba(239, 68, 68, 0.5)"};
`,L=a.div`
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`,i=a.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
  color: white;
  
  .label {
    font-size: 1rem;
    opacity: 0.8;
  }
  
  .value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #fbbf24;
  }
`,N=a.div`
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1rem 0;
`,C=a.div`
  font-size: 2rem;
  color: ${r=>r.filled?"#fbbf24":"rgba(255, 255, 255, 0.3)"};
  filter: ${r=>r.filled?"drop-shadow(0 0 10px #fbbf24)":"none"};
  transition: all 0.3s ease;
`,z=a.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
`,l=a.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${r=>r.variant==="primary"?`
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }
  `:`
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }
  `}
`,T=({isVisible:r})=>{const{gameState:t,resetGame:x}=h(),c=v(),d=w();if(j.useEffect(()=>{r&&(t.status==="won"||t.status==="lost")&&(t.status==="won"?c.onLevelComplete():c.onLevelFailed())},[r,t.status,c]),!r||t.status!=="won"&&t.status!=="lost")return null;const s=t.status==="won",m=t.targetScore||0,n=Math.min(t.score/m*100,100),p=s?n>=100?3:n>=80?2:n>=60?1:0:0,f=()=>{x()},g=()=>{const o=t.currentLevel||1;d(`/game?level=${o+1}`)},u=()=>{d("/levels")},b=()=>{d("/")};return e.jsx(y,{children:e.jsxs(k,{children:[e.jsx(S,{isWin:s,children:s?"🎉 恭喜过关！":"😔 游戏结束"}),s&&e.jsx(N,{children:[1,2,3].map(o=>e.jsx(C,{filled:o<=p,children:"⭐"},o))}),e.jsxs(L,{children:[e.jsxs(i,{children:[e.jsx("span",{className:"label",children:"最终分数"}),e.jsx("span",{className:"value",children:t.score.toLocaleString()})]}),e.jsxs(i,{children:[e.jsx("span",{className:"label",children:"目标分数"}),e.jsx("span",{className:"value",children:m.toLocaleString()})]}),e.jsxs(i,{children:[e.jsx("span",{className:"label",children:"完成度"}),e.jsxs("span",{className:"value",children:[n.toFixed(1),"%"]})]}),t.movesLeft!==void 0&&e.jsxs(i,{children:[e.jsx("span",{className:"label",children:"剩余步数"}),e.jsx("span",{className:"value",children:t.movesLeft})]})]}),e.jsxs(z,{children:[e.jsx(l,{variant:"primary",onClick:f,children:"重新开始"}),s&&e.jsx(l,{variant:"primary",onClick:g,children:"下一关"}),e.jsx(l,{onClick:u,children:"选择关卡"}),e.jsx(l,{onClick:b,children:"返回主页"})]})]})})};export{T as default};
//# sourceMappingURL=GameOverModal-4jLpsjyZ.js.map
