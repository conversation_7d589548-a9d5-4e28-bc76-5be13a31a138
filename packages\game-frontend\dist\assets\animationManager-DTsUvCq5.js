var h=Object.defineProperty;var d=(o,t,i)=>t in o?h(o,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[t]=i;var r=(o,t,i)=>d(o,typeof t!="symbol"?t+"":t,i);class p{constructor(t){r(this,"animations",new Map);r(this,"particles",[]);r(this,"animationId",null);r(this,"lastFrameTime",0);this.onAnimationUpdate=t}start(){if(this.animationId)return;const t=i=>{this.update(i),this.onAnimationUpdate(),this.hasActiveAnimations()||this.particles.length>0?this.animationId=requestAnimationFrame(t):this.animationId=null};this.animationId=requestAnimationFrame(t)}stop(){this.animationId&&(cancelAnimationFrame(this.animationId),this.animationId=null)}update(t=performance.now()){this.lastFrameTime=t;for(const[i,e]of this.animations){const s=t-e.startTime;Math.min(s/e.duration,1)>=1&&(e.isActive=!1,e.onComplete&&e.onComplete(),this.animations.delete(i))}this.particles=this.particles.filter(i=>(i.life-=16,i.x+=i.vx,i.y+=i.vy,i.vy+=.2,i.vx*=.99,i.life>0))}addSwapAnimation(t,i,e=300,s){const n=`swap_${t.pos.row}_${t.pos.col}_${Date.now()}`,a=`swap_${i.pos.row}_${i.pos.col}_${Date.now()}_2`;this.animations.set(n,{id:n,type:"swap",startTime:this.lastFrameTime,duration:e,from:t.pos,to:i.pos,gemType:t.type,isActive:!0,onComplete:s}),this.animations.set(a,{id:a,type:"swap",startTime:this.lastFrameTime,duration:e,from:i.pos,to:t.pos,gemType:i.type,isActive:!0})}addFallAnimation(t,i,e,s=400,n){const a=`fall_${e.row}_${e.col}_${Date.now()}`;this.animations.set(a,{id:a,type:"fall",startTime:this.lastFrameTime,duration:s,from:i,to:e,gemType:t,isActive:!0,onComplete:n})}addEliminateAnimation(t,i=500,e){t.forEach((s,n)=>{const a=`eliminate_${s.row}_${s.col}_${Date.now()}`;this.animations.set(a,{id:a,type:"eliminate",startTime:this.lastFrameTime+n*50,duration:i,from:s,to:s,gemType:0,isActive:!0,onComplete:n===t.length-1?e:void 0})}),t.forEach(s=>{this.addParticleEffect(s,"explosion")})}addParticleEffect(t,i){const s=t.col*60+30,n=t.row*60+60/2,a=i==="explosion"?8:4;for(let l=0;l<a;l++){const c=Math.PI*2*l/a,m=2+Math.random()*3;this.particles.push({id:`particle_${Date.now()}_${l}`,x:s,y:n,vx:Math.cos(c)*m,vy:Math.sin(c)*m,life:1e3+Math.random()*500,maxLife:1500,color:this.getParticleColor(i),size:3+Math.random()*2,type:i})}}getParticleColor(t){switch(t){case"star":return"#fbbf24";case"sparkle":return"#60a5fa";case"explosion":return"#f87171";default:return"#ffffff"}}getAnimationProgress(t){const i=this.animations.get(t);if(!i)return 1;const e=this.lastFrameTime-i.startTime;return Math.min(e/i.duration,1)}getInterpolatedPosition(t){const i=this.lastFrameTime-t.startTime,e=Math.min(i/t.duration,1),s=this.easeInOutCubic(e);return{row:t.from.row+(t.to.row-t.from.row)*s,col:t.from.col+(t.to.col-t.from.col)*s}}easeInOutCubic(t){return t<.5?4*t*t*t:1-Math.pow(-2*t+2,3)/2}hasActiveAnimations(){return this.animations.size>0}getActiveAnimations(){return Array.from(this.animations.values())}getParticles(){return this.particles}clear(){this.animations.clear(),this.particles.length=0,this.stop()}}export{p as AnimationManager};
//# sourceMappingURL=animationManager-DTsUvCq5.js.map
