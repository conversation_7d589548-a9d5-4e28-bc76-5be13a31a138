import {
  SpecialGemType,
  SpecialGem,
  // <PERSON>mbo<PERSON>hain,
  ComboEffect,
  // SPECIAL_GEM_CONDITIONS,
  COMBO_MULTIPLIERS,
  // SPECIAL_GEM_COMBOS,
  COMBO_REWARDS
} from '../types/specialGems'

export interface MatchResult {
  positions: Array<{ row: number; col: number }>
  type: 'horizontal' | 'vertical' | 'L' | 'T' | 'square' | 'cross'
  color: number
  length: number
}

export interface SpecialGemCreationResult {
  specialGem?: SpecialGem
  shouldCreate: boolean
  type?: SpecialGemType
  position: { row: number; col: number }
}

export class SpecialGemManager {
  // private comboChains: Map<string, ComboChain> = new Map()
  private currentComboCount = 0
  private lastComboTime = 0
  private comboTimeout = 2000 // 2秒内的连续消除算作连击

  // 检查是否应该生成特殊宝石
  public checkSpecialGemCreation(matches: MatchResult[]): SpecialGemCreationResult[] {
    const results: SpecialGemCreationResult[] = []

    for (const match of matches) {
      const result = this.analyzeMatch(match)
      if (result.shouldCreate) {
        results.push(result)
      }
    }

    return results
  }

  // 分析匹配模式
  private analyzeMatch(match: MatchResult): SpecialGemCreationResult {
    const { positions, type, length } = match
    const centerPos = this.getCenterPosition(positions)

    // 6个或更多宝石 - 彩虹宝石
    if (length >= 6) {
      return {
        shouldCreate: true,
        type: 'rainbow',
        position: centerPos,
        specialGem: this.createSpecialGem('rainbow', match.color, centerPos)
      }
    }

    // 5个宝石的不同形状
    if (length === 5) {
      switch (type) {
        case 'square':
          return {
            shouldCreate: true,
            type: 'color_bomb',
            position: centerPos,
            specialGem: this.createSpecialGem('color_bomb', match.color, centerPos)
          }
        case 'cross':
          return {
            shouldCreate: true,
            type: 'lightning',
            position: centerPos,
            specialGem: this.createSpecialGem('lightning', match.color, centerPos)
          }
        case 'L':
        case 'T':
          return {
            shouldCreate: true,
            type: 'bomb',
            position: centerPos,
            specialGem: this.createSpecialGem('bomb', match.color, centerPos)
          }
        default:
          // 直线5个 - 根据方向决定
          const isHorizontal = this.isHorizontalLine(positions)
          return {
            shouldCreate: true,
            type: isHorizontal ? 'line_horizontal' : 'line_vertical',
            position: centerPos,
            specialGem: this.createSpecialGem(
              isHorizontal ? 'line_horizontal' : 'line_vertical',
              match.color,
              centerPos
            )
          }
      }
    }

    // 4个宝石 - 直线消除宝石
    if (length === 4) {
      const isHorizontal = this.isHorizontalLine(positions)
      return {
        shouldCreate: true,
        type: isHorizontal ? 'line_horizontal' : 'line_vertical',
        position: centerPos,
        specialGem: this.createSpecialGem(
          isHorizontal ? 'line_horizontal' : 'line_vertical',
          match.color,
          centerPos
        )
      }
    }

    return {
      shouldCreate: false,
      position: centerPos
    }
  }

  // 创建特殊宝石
  private createSpecialGem(type: SpecialGemType, color: number, position: { row: number; col: number }): SpecialGem {
    return {
      type,
      color,
      row: position.row,
      col: position.col,
      id: `special_${type}_${Date.now()}_${Math.random()}`
    }
  }

  // 获取中心位置
  private getCenterPosition(positions: Array<{ row: number; col: number }>): { row: number; col: number } {
    const avgRow = Math.round(positions.reduce((sum, pos) => sum + pos.row, 0) / positions.length)
    const avgCol = Math.round(positions.reduce((sum, pos) => sum + pos.col, 0) / positions.length)
    return { row: avgRow, col: avgCol }
  }

  // 检查是否为横向直线
  private isHorizontalLine(positions: Array<{ row: number; col: number }>): boolean {
    const rows = new Set(positions.map(p => p.row))
    return rows.size === 1
  }

  // 处理特殊宝石激活
  public activateSpecialGem(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    switch (specialGem.type) {
      case 'line_horizontal':
        return this.activateHorizontalLine(specialGem, board)
      case 'line_vertical':
        return this.activateVerticalLine(specialGem, board)
      case 'bomb':
        return this.activateBomb(specialGem, board)
      case 'color_bomb':
        return this.activateColorBomb(specialGem, board)
      case 'lightning':
        return this.activateLightning(specialGem, board)
      case 'rainbow':
        return this.activateRainbow(specialGem, board)
      default:
        return []
    }
  }

  // 横向消除
  private activateHorizontalLine(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = []
    for (let col = 0; col < board[0].length; col++) {
      positions.push({ row: specialGem.row, col })
    }
    return positions
  }

  // 纵向消除
  private activateVerticalLine(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = []
    for (let row = 0; row < board.length; row++) {
      positions.push({ row, col: specialGem.col })
    }
    return positions
  }

  // 炸弹消除 (3x3)
  private activateBomb(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = []
    const { row, col } = specialGem
    
    for (let r = Math.max(0, row - 1); r <= Math.min(board.length - 1, row + 1); r++) {
      for (let c = Math.max(0, col - 1); c <= Math.min(board[0].length - 1, col + 1); c++) {
        positions.push({ row: r, col: c })
      }
    }
    return positions
  }

  // 彩色炸弹 - 消除所有同色
  private activateColorBomb(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = []
    const targetColor = specialGem.color
    
    for (let row = 0; row < board.length; row++) {
      for (let col = 0; col < board[0].length; col++) {
        if (board[row][col] === targetColor) {
          positions.push({ row, col })
        }
      }
    }
    return positions
  }

  // 闪电 - 十字消除
  private activateLightning(specialGem: SpecialGem, board: number[][]): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = []
    const { row, col } = specialGem
    
    // 横向
    for (let c = 0; c < board[0].length; c++) {
      positions.push({ row, col: c })
    }
    
    // 纵向
    for (let r = 0; r < board.length; r++) {
      positions.push({ row: r, col })
    }
    
    return positions
  }

  // 彩虹宝石 - 与目标宝石交换时的效果
  private activateRainbow(_specialGem: SpecialGem, _board: number[][]): Array<{ row: number; col: number }> {
    // 彩虹宝石的效果需要根据交换的目标宝石来决定
    // 这里返回空数组，实际效果在交换时处理
    return []
  }

  // 处理连击
  public processCombo(eliminatedCount: number): ComboEffect | null {
    const now = Date.now()
    
    // 检查是否在连击时间窗口内
    if (now - this.lastComboTime <= this.comboTimeout) {
      this.currentComboCount++
    } else {
      this.currentComboCount = 1
    }
    
    this.lastComboTime = now
    
    if (this.currentComboCount >= 2) {
      const multiplier = this.getComboMultiplier(this.currentComboCount)
      const scoreBonus = eliminatedCount * 10 * multiplier
      
      return {
        type: 'cascade_combo',
        multiplier,
        description: `${this.currentComboCount}连击！`,
        scoreBonus
      }
    }
    
    return null
  }

  // 获取连击倍数
  private getComboMultiplier(comboCount: number): number {
    if (comboCount in COMBO_MULTIPLIERS) {
      return COMBO_MULTIPLIERS[comboCount as keyof typeof COMBO_MULTIPLIERS]
    }
    return COMBO_MULTIPLIERS.max
  }

  // 重置连击
  public resetCombo(): void {
    this.currentComboCount = 0
    this.lastComboTime = 0
  }

  // 获取当前连击数
  public getCurrentCombo(): number {
    return this.currentComboCount
  }

  // 检查连击奖励
  public checkComboRewards(comboCount: number): any {
    const milestones = COMBO_REWARDS.milestones
    if (comboCount in milestones) {
      return milestones[comboCount as keyof typeof milestones]
    }
    return null
  }
}
