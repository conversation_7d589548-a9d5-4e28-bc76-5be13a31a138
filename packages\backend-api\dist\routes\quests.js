"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.questRoutes = void 0;
const express_1 = require("express");
const database_1 = require("../database/database");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
exports.questRoutes = router;
const db = database_1.Database.getInstance();
router.use(auth_1.authenticateToken);
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const dailyQuests = [
        {
            questCode: 'daily_complete_3',
            name: '完成3个关卡',
            description: '今日完成任意3个关卡',
            type: 'daily',
            targetType: 'complete_levels',
            targetValue: 3,
            currentProgress: 0,
            rewards: {
                coins: 100,
                gems: 0,
                items: []
            },
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        },
        {
            questCode: 'daily_score_10000',
            name: '获得10000分',
            description: '单局游戏获得10000分以上',
            type: 'daily',
            targetType: 'high_score',
            targetValue: 10000,
            currentProgress: 0,
            rewards: {
                coins: 200,
                gems: 1,
                items: []
            },
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
    ];
    const achievements = [
        {
            questCode: 'achievement_first_win',
            name: '初次胜利',
            description: '完成第一个关卡',
            type: 'achievement',
            targetType: 'complete_level',
            targetValue: 1,
            currentProgress: 0,
            rewards: {
                coins: 50,
                gems: 1,
                items: ['hammer']
            }
        }
    ];
    res.json({
        success: true,
        data: {
            daily: dailyQuests,
            achievements: achievements
        }
    });
}));
router.post('/:questCode/claim', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { questCode } = req.params;
    res.json({
        success: true,
        data: {
            questCode,
            rewards: {
                coins: 100,
                gems: 0,
                items: []
            }
        },
        message: '奖励领取成功'
    });
}));
//# sourceMappingURL=quests.js.map